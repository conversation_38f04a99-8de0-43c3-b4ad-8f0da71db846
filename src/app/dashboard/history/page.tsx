import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import ReceiptHistoryClient from '@/components/history/ReceiptHistoryClient';

export default async function ReceiptHistoryPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Receipt History</h1>
          <p className="text-gray-400 mt-2">
            Browse, search, and manage all your processed receipts
          </p>
        </div>
      </div>
      
      <ReceiptHistoryClient />
    </div>
  );
}
