'use client';

import { Upload, X } from 'lucide-react';
import { BusinessInfo } from '../types';

interface BusinessSectionProps {
  business: BusinessInfo;
  updateBusiness: (business: BusinessInfo) => void;
}

export default function BusinessSection({ business, updateBusiness }: BusinessSectionProps) {
  const handleInputChange = (field: keyof BusinessInfo, value: string) => {
    updateBusiness({ ...business, [field]: value });
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      updateBusiness({ ...business, logo: file });
    }
  };

  const removeLogo = () => {
    updateBusiness({ ...business, logo: null });
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-white mb-2">FROM (Your Business)</h2>
        <p className="text-gray-400 text-sm">Enter your business information</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Logo Upload */}
        <div className="lg:col-span-1">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Logo (Optional)
          </label>
          {business.logo ? (
            <div className="relative">
              <img
                src={URL.createObjectURL(business.logo)}
                alt="Business Logo"
                className="w-full h-32 object-contain bg-gray-700 rounded-lg border border-gray-600"
              />
              <button
                onClick={removeLogo}
                className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ) : (
            <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-600 border-dashed rounded-lg cursor-pointer bg-gray-700 hover:bg-gray-600 transition-colors">
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <Upload className="w-8 h-8 mb-2 text-gray-400" />
                <p className="text-xs text-gray-400 text-center">Upload Logo</p>
              </div>
              <input
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleLogoUpload}
              />
            </label>
          )}
        </div>

        {/* Business Details */}
        <div className="lg:col-span-3 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Business Name *
            </label>
            <input
              type="text"
              value={business.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              placeholder="Your Business Name"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Address
              </label>
              <input
                type="text"
                value={business.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="Street Address"
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <input
                type="text"
                value={business.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="City"
              />
              <input
                type="text"
                value={business.state}
                onChange={(e) => handleInputChange('state', e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="State"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                ZIP Code
              </label>
              <input
                type="text"
                value={business.zip}
                onChange={(e) => handleInputChange('zip', e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="ZIP"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Phone
              </label>
              <input
                type="tel"
                value={business.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="Phone Number"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Email
              </label>
              <input
                type="email"
                value={business.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                placeholder="Email Address"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
