import { NextRequest, NextResponse } from 'next/server';
import { processGmailForAllUsers } from '@/lib/gmail/processor';

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Manual Gmail cron test triggered');
    
    // Get target hour from request or use current hour
    const body = await request.json().catch(() => ({}));
    const targetHour = body.targetHour ?? new Date().getHours();
    
    console.log(`🕐 Testing Gmail processing for hour ${targetHour}`);
    
    // Process Gmail for all users at the target hour
    const result = await processGmailForAllUsers(targetHour);
    
    console.log(`✅ Manual Gmail cron test completed:`, result);
    
    return NextResponse.json({
      success: true,
      data: {
        targetHour,
        timestamp: new Date().toISOString(),
        ...result
      }
    });
    
  } catch (error) {
    console.error('❌ Manual Gmail cron test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const targetHour = parseInt(searchParams.get('hour') || new Date().getHours().toString());
    
    console.log(`🧪 Manual Gmail cron test (GET) for hour ${targetHour}`);
    
    const result = await processGmailForAllUsers(targetHour);
    
    return NextResponse.json({
      success: true,
      data: {
        targetHour,
        timestamp: new Date().toISOString(),
        ...result
      }
    });
    
  } catch (error) {
    console.error('❌ Manual Gmail cron test (GET) failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
