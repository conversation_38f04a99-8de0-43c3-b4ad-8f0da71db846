import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { validateGmailTokens } from '@/lib/gmail/auth';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user has Business tier access
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (userData.current_tier !== 'business') {
      return NextResponse.json({ error: 'Business plan required' }, { status: 403 });
    }

    // Test Gmail connection
    const validation = await validateGmailTokens(user.id);

    return NextResponse.json({
      success: true,
      data: {
        valid: validation.valid,
        error: validation.error
      }
    });
  } catch (error) {
    console.error('Error testing Gmail connection:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to test Gmail connection' 
      }, 
      { status: 500 }
    );
  }
}
