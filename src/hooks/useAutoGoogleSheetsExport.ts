'use client';

import { useEffect, useRef } from 'react';
import { createClient } from '@/lib/supabase/client';

interface Receipt {
  id: string;
  processing_status: string;
  google_sheet_row_number: number | null;
  vendor?: string;
  vendor_tax_id?: string;
  receipt_date?: string;
  currency?: string;
  payment_method?: string;
  subtotal?: number;
  tax_rate_percent?: number;
  tax_amount?: number;
  total_amount?: number;
  original_file_name?: string;
  items?: any[];
}

/**
 * Hook to automatically export completed receipts to Google Sheets
 * This runs when the dashboard loads and exports any receipts that are completed
 * but don't have a google_sheet_row_number yet
 */
export function useAutoGoogleSheetsExport() {
  const isRunningRef = useRef(false);
  const supabase = createClient();

  useEffect(() => {
    const exportPendingReceipts = async () => {
      // Prevent multiple simultaneous runs
      if (isRunningRef.current) {
        return;
      }

      isRunningRef.current = true;

      try {
        console.log('🔍 Checking for receipts needing Google Sheets export...');

        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          console.log('No authenticated user, skipping auto export');
          return;
        }

        // Check if user has Google Sheets integration
        const { data: userData } = await supabase
          .from('users')
          .select('google_access_token')
          .eq('id', user.id)
          .single();

        if (!userData?.google_access_token) {
          console.log('User does not have Google Sheets integration, skipping auto export');
          return;
        }

        // Find completed receipts that haven't been exported to Google Sheets yet
        const { data: pendingReceipts, error } = await supabase
          .from('receipts')
          .select(`
            id, processing_status, google_sheet_row_number, original_file_name,
            vendor, vendor_tax_id, receipt_date, currency, payment_method,
            subtotal, tax_rate_percent, tax_amount, total_amount,
            receipt_items (
              description, category, total
            )
          `)
          .eq('user_id', user.id)
          .eq('processing_status', 'completed')
          .is('google_sheet_row_number', null)
          .order('created_at', { ascending: false })
          .limit(10); // Process max 10 at a time to avoid overwhelming

        if (error) {
          console.error('Error fetching pending receipts:', error);
          return;
        }

        if (!pendingReceipts || pendingReceipts.length === 0) {
          console.log('No receipts pending Google Sheets export');
          return;
        }

        console.log(`📊 Found ${pendingReceipts.length} receipts needing Google Sheets export`);

        // Export each receipt
        for (const receipt of pendingReceipts) {
          try {
            console.log(`📤 Exporting receipt ${receipt.id} to Google Sheets...`);

            // Transform receipt data to match the expected format
            const extractedData = {
              vendor: receipt.vendor,
              vendor_tax_id: receipt.vendor_tax_id,
              date: receipt.receipt_date,
              currency: receipt.currency || 'KES',
              payment_method: receipt.payment_method,
              subtotal: receipt.subtotal,
              tax_rate_percent: receipt.tax_rate_percent,
              tax_amount: receipt.tax_amount,
              total_amount: receipt.total_amount,
              items: receipt.receipt_items?.map((item: any) => ({
                description: item.description,
                category: item.category,
                total: item.total
              })) || []
            };

            // Call the Google Sheets export API
            const response = await fetch('/api/google-sheets/export', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                userId: user.id,
                receiptId: receipt.id,
                extractedData,
                originalFileName: receipt.original_file_name || 'receipt.pdf'
              })
            });

            const result = await response.json();

            if (result.success && result.rowNumber) {
              console.log(`✅ Successfully exported receipt ${receipt.id} to Google Sheets row ${result.rowNumber}`);
            } else {
              console.warn(`⚠️ Failed to export receipt ${receipt.id}:`, result.error);
            }

            // Small delay between exports to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 500));

          } catch (exportError) {
            console.error(`❌ Error exporting receipt ${receipt.id}:`, exportError);
          }
        }

        console.log('🎉 Auto Google Sheets export completed');

      } catch (error) {
        console.error('Error in auto Google Sheets export:', error);
      } finally {
        isRunningRef.current = false;
      }
    };

    // Run the export after a short delay to let the page load
    const timeoutId = setTimeout(exportPendingReceipts, 2000);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [supabase]);
}
