'use client'

import React, { useState, useEffect } from 'react'
import {
  Cloud,
  CloudOff,
  ExternalLink,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  User,
  Unlink,
  FolderOpen,
  Download,
  FileImage,
  FileText,
  Loader2
} from 'lucide-react'

interface GoogleDriveStatus {
  connected: boolean
  email: string | null
  folder: {
    id: string
    name: string
    webViewLink: string
  } | null
}

interface DriveFolder {
  id: string
  name: string
  webViewLink: string
}

interface DriveFile {
  id: string
  name: string
  mimeType: string
  size?: string
  modifiedTime: string
  webViewLink: string
  thumbnailLink?: string
}

interface GoogleDriveConnectionProps {
  onStatusChange?: (connected: boolean) => void
  onImportComplete?: (results: any) => void
}

export default function GoogleDriveConnection({ onStatusChange, onImportComplete }: GoogleDriveConnectionProps) {
  const [status, setStatus] = useState<GoogleDriveStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [connecting, setConnecting] = useState(false)
  const [disconnecting, setDisconnecting] = useState(false)
  const [testing, setTesting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // New states for import functionality
  const [folders, setFolders] = useState<DriveFolder[]>([])
  const [selectedFolder, setSelectedFolder] = useState<DriveFolder | null>(null)
  const [files, setFiles] = useState<DriveFile[]>([])
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())
  const [loadingFolders, setLoadingFolders] = useState(false)
  const [loadingFiles, setLoadingFiles] = useState(false)
  const [importing, setImporting] = useState(false)
  const [showImportInterface, setShowImportInterface] = useState(false)

  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/google-drive/status')
      const result = await response.json()
      
      if (result.success) {
        setStatus(result.data)
        onStatusChange?.(result.data.connected)
      } else {
        setError('Failed to load Google Drive status')
      }
    } catch (error) {
      console.error('Error fetching Google Drive status:', error)
      setError('Failed to load Google Drive status')
    } finally {
      setLoading(false)
    }
  }

  const handleConnect = () => {
    setConnecting(true)
    setError(null)
    // Redirect to Google Drive OAuth
    window.location.href = '/api/auth/google-drive'
  }

  const handleDisconnect = async () => {
    if (!confirm('Are you sure you want to disconnect Google Drive? You won\'t be able to import receipts from Drive until you reconnect.')) {
      return
    }

    setDisconnecting(true)
    setError(null)

    try {
      const response = await fetch('/api/google-drive/disconnect', {
        method: 'POST'
      })

      const result = await response.json()

      if (result.success) {
        setStatus({ connected: false, email: null, folder: null })
        setShowImportInterface(false)
        onStatusChange?.(false)
      } else {
        setError(result.error || 'Failed to disconnect Google Drive')
      }
    } catch (error) {
      console.error('Error disconnecting Google Drive:', error)
      setError('Failed to disconnect Google Drive')
    } finally {
      setDisconnecting(false)
    }
  }

  const loadFolders = async () => {
    console.log('🔍 Loading Google Drive folders...')
    setLoadingFolders(true)
    setError(null)

    try {
      const response = await fetch('/api/google-drive/folders')
      console.log('📁 Folders API response status:', response.status)

      const result = await response.json()
      console.log('📁 Folders API result:', result)

      if (result.success) {
        console.log('✅ Folders loaded successfully:', result.data.folders.length, 'folders')
        setFolders(result.data.folders)
      } else {
        console.error('❌ Failed to load folders:', result.error)
        setError(result.error || 'Failed to load folders')
      }
    } catch (error) {
      console.error('💥 Error loading folders:', error)
      setError('Failed to load folders')
    } finally {
      setLoadingFolders(false)
    }
  }

  const loadFiles = async (folderId: string) => {
    setLoadingFiles(true)
    setError(null)

    try {
      const response = await fetch(`/api/google-drive/files?folderId=${folderId}`)
      const result = await response.json()

      if (result.success) {
        setFiles(result.data.files)
        setSelectedFiles(new Set())
      } else {
        setError(result.error || 'Failed to load files')
      }
    } catch (error) {
      console.error('Error loading files:', error)
      setError('Failed to load files')
    } finally {
      setLoadingFiles(false)
    }
  }

  const handleFolderSelect = async (folder: DriveFolder) => {
    setSelectedFolder(folder)
    await loadFiles(folder.id)
  }

  const handleFileToggle = (fileId: string) => {
    const newSelected = new Set(selectedFiles)
    if (newSelected.has(fileId)) {
      newSelected.delete(fileId)
    } else {
      newSelected.add(fileId)
    }
    setSelectedFiles(newSelected)
  }

  const handleImport = async () => {
    if (selectedFiles.size === 0) {
      setError('Please select at least one file to import')
      return
    }

    setImporting(true)
    setError(null)

    try {
      const response = await fetch('/api/google-drive/import', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fileIds: Array.from(selectedFiles)
        })
      })

      const result = await response.json()
      console.log('📥 Import API response:', result)

      if (result.success) {
        console.log('✅ Import successful:', result.data)
        onImportComplete?.(result.data)
        setSelectedFiles(new Set())
        setShowImportInterface(false)
      } else {
        console.error('❌ Import failed:', result.error)
        setError(result.error || 'Failed to import files')
      }
    } catch (error) {
      console.error('Error importing files:', error)
      setError('Failed to import files')
    } finally {
      setImporting(false)
    }
  }

  const handleRefresh = () => {
    setLoading(true)
    setError(null)
    fetchStatus()
  }

  const handleTestConnection = async () => {
    setTesting(true)
    setError(null)

    try {
      const response = await fetch('/api/google-drive/test-connection', {
        method: 'POST'
      })

      const result = await response.json()

      if (result.success) {
        if (result.data.valid) {
          setError(null)
          // Refresh status to get latest info
          fetchStatus()
        } else {
          setError(result.data.error || 'Google Drive connection is invalid')
        }
      } else {
        setError(result.error || 'Failed to test Google Drive connection')
      }
    } catch (error) {
      console.error('Error testing Google Drive connection:', error)
      setError('Failed to test Google Drive connection')
    } finally {
      setTesting(false)
    }
  }

  useEffect(() => {
    fetchStatus()
  }, [])

  // Check for URL parameters indicating connection status
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const driveConnected = urlParams.get('drive_connected')
    const driveEmail = urlParams.get('drive_email')
    const error = urlParams.get('error')

    if (driveConnected === 'true' && driveEmail) {
      // Refresh status after successful connection
      setTimeout(() => {
        fetchStatus()
      }, 1000)
      
      // Clean up URL
      const newUrl = window.location.pathname
      window.history.replaceState({}, '', newUrl)
    }

    if (error) {
      const errorMessages: Record<string, string> = {
        'drive_auth_cancelled': 'Google Drive connection was cancelled',
        'drive_auth_failed': 'Failed to connect to Google Drive',
        'drive_auth_invalid': 'Invalid Google Drive authorization',
        'drive_auth_unauthorized': 'Unauthorized Google Drive access',
        'drive_auth_error': 'An error occurred during Google Drive connection'
      }
      
      setError(errorMessages[error] || 'An error occurred with Google Drive')
      
      // Clean up URL
      const newUrl = window.location.pathname
      window.history.replaceState({}, '', newUrl)
    }
  }, [])

  if (loading) {
    return (
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center space-x-3">
          <RefreshCw className="w-5 h-5 text-gray-400 animate-spin" />
          <span className="text-gray-400">Loading Google Drive status...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4 space-y-3 sm:space-y-0">
        <div className="flex items-center space-x-3">
          <div className={`p-3 rounded-lg ${status?.connected ? 'bg-green-500/20' : 'bg-gray-600/20'}`}>
            {status?.connected ? (
              <Cloud className="w-6 h-6 text-green-400" />
            ) : (
              <CloudOff className="w-6 h-6 text-gray-400" />
            )}
          </div>
          <div className="min-w-0 flex-1">
            <h3 className="text-lg font-semibold text-white">Google Drive Import</h3>
            <p className="text-gray-400 text-sm">
              {status?.connected
                ? 'Import receipt images and PDFs from your Google Drive'
                : 'Connect to import receipts from your Google Drive'
              }
            </p>
          </div>
        </div>

        <button
          onClick={handleRefresh}
          disabled={loading}
          className="p-2 text-gray-400 hover:text-white transition-colors self-start sm:self-auto"
          title="Refresh status"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
          <div>
            <p className="text-red-400 font-medium">Error</p>
            <p className="text-red-300 text-sm">{error}</p>
          </div>
        </div>
      )}

      {status?.connected ? (
        <div className="space-y-4">
          {/* Connected Status */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
            <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
            <div className="flex-grow min-w-0">
              <p className="text-green-400 font-medium">Connected to Google Drive</p>
              <div className="flex items-center space-x-2 mt-1">
                <User className="w-4 h-4 text-green-300 flex-shrink-0" />
                <span className="text-green-300 text-sm truncate">{status.email}</span>
              </div>
            </div>
          </div>

          {/* Import Interface */}
          {showImportInterface ? (
            <div className="space-y-4">
              {/* Folder Selection */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-white font-medium">Select Folder</h4>
                  <button
                    onClick={loadFolders}
                    disabled={loadingFolders}
                    className="text-blue-400 hover:text-blue-300 text-sm"
                  >
                    {loadingFolders ? 'Loading...' : 'Refresh'}
                  </button>
                </div>

                {loadingFolders ? (
                  <div className="flex items-center space-x-2 text-gray-400">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Loading folders...</span>
                  </div>
                ) : (
                  <div className="grid gap-2 max-h-40 overflow-y-auto">
                    {folders.map((folder) => (
                      <button
                        key={folder.id}
                        onClick={() => handleFolderSelect(folder)}
                        className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors text-left ${
                          selectedFolder?.id === folder.id
                            ? 'bg-blue-500/20 border-blue-500/40 text-blue-400'
                            : 'bg-gray-700/50 border-gray-600 text-gray-300 hover:bg-gray-700'
                        }`}
                      >
                        <FolderOpen className="w-5 h-5 flex-shrink-0" />
                        <span className="truncate">{folder.name}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* File Selection */}
              {selectedFolder && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="text-white font-medium">
                      Select Files from "{selectedFolder.name}"
                    </h4>
                    <div className="text-sm text-gray-400">
                      {selectedFiles.size} selected
                    </div>
                  </div>

                  {loadingFiles ? (
                    <div className="flex items-center space-x-2 text-gray-400">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span>Loading files...</span>
                    </div>
                  ) : files.length === 0 ? (
                    <div className="text-gray-400 text-center py-4">
                      No receipt files found in this folder
                    </div>
                  ) : (
                    <div className="grid gap-2 max-h-60 overflow-y-auto">
                      {files.map((file) => (
                        <label
                          key={file.id}
                          className="flex items-center space-x-3 p-3 rounded-lg border border-gray-600 hover:bg-gray-700/50 cursor-pointer"
                        >
                          <input
                            type="checkbox"
                            checked={selectedFiles.has(file.id)}
                            onChange={() => handleFileToggle(file.id)}
                            className="rounded border-gray-500 text-blue-600 focus:ring-blue-500"
                          />
                          {file.mimeType.includes('pdf') ? (
                            <FileText className="w-5 h-5 text-red-400 flex-shrink-0" />
                          ) : (
                            <FileImage className="w-5 h-5 text-green-400 flex-shrink-0" />
                          )}
                          <div className="flex-1 min-w-0">
                            <p className="text-gray-300 truncate">{file.name}</p>
                            <p className="text-gray-500 text-xs">
                              {new Date(file.modifiedTime).toLocaleDateString()}
                              {file.size && ` • ${Math.round(parseInt(file.size) / 1024)} KB`}
                            </p>
                          </div>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Import Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={handleImport}
                  disabled={importing || selectedFiles.size === 0}
                  className="flex-1 flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-green-600/50 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  {importing ? (
                    <>
                      <Loader2 className="w-5 h-5 animate-spin" />
                      <span>Importing...</span>
                    </>
                  ) : (
                    <>
                      <Download className="w-5 h-5" />
                      <span>Import {selectedFiles.size} file{selectedFiles.size !== 1 ? 's' : ''}</span>
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowImportInterface(false)}
                  className="px-4 py-3 text-gray-400 hover:text-white transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <button
              onClick={() => {
                console.log('🚀 Import button clicked, showing interface')
                setShowImportInterface(true)
                console.log('📂 Current folders count:', folders.length)
                if (folders.length === 0) {
                  console.log('📁 No folders cached, loading...')
                  loadFolders()
                } else {
                  console.log('📁 Using cached folders')
                }
              }}
              className="w-full flex items-center justify-center space-x-3 bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
            >
              <Download className="w-5 h-5" />
              <span>Import Receipts from Drive</span>
            </button>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:justify-between">
            <button
              onClick={handleTestConnection}
              disabled={testing}
              className="flex items-center justify-center space-x-2 px-4 py-2 text-blue-400 hover:text-blue-300 hover:bg-blue-500/10 rounded-lg transition-colors disabled:opacity-50"
            >
              {testing ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <CheckCircle className="w-4 h-4" />
              )}
              <span>{testing ? 'Testing...' : 'Test Connection'}</span>
            </button>

            <button
              onClick={handleDisconnect}
              disabled={disconnecting}
              className="flex items-center justify-center space-x-2 px-4 py-2 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors disabled:opacity-50"
            >
              {disconnecting ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Unlink className="w-4 h-4" />
              )}
              <span>{disconnecting ? 'Disconnecting...' : 'Disconnect'}</span>
            </button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Benefits */}
          <div className="space-y-2">
            <h4 className="text-white font-medium">Benefits of connecting Google Drive:</h4>
            <ul className="text-gray-400 text-sm space-y-1">
              <li>• Import receipt images and PDFs directly from Drive</li>
              <li>• Process existing receipts you've already stored</li>
              <li>• Browse and select specific folders and files</li>
              <li>• Automatic OCR processing and Google Sheets export</li>
            </ul>
          </div>

          {/* Connect Button */}
          <button
            onClick={handleConnect}
            disabled={connecting}
            className="w-full flex items-center justify-center space-x-3 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white font-medium py-3 px-4 rounded-lg transition-colors"
          >
            {connecting ? (
              <>
                <RefreshCw className="w-5 h-5 animate-spin" />
                <span>Connecting...</span>
              </>
            ) : (
              <>
                <Cloud className="w-5 h-5" />
                <span>Connect Google Drive</span>
              </>
            )}
          </button>

          <p className="text-gray-500 text-xs text-center">
            We only access files you explicitly select for import. Your other Drive files remain private.
          </p>
        </div>
      )}
    </div>
  )
}
