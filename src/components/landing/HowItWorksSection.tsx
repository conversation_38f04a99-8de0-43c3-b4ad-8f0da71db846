import Link from 'next/link'
import { Upload, Zap, Bar<PERSON>hart3, ArrowR<PERSON> } from 'lucide-react'

export default function HowItWorksSection() {
  return (
    <section id="how-it-works" className="relative z-10 px-4 py-12 sm:py-20 bg-gray-900/50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6 px-2">
            How It Works for Your Small Business
          </h2>
          <p className="text-base sm:text-xl text-gray-300 max-w-3xl mx-auto px-2">
            From receipt upload to organized expense data in just three simple steps. No technical knowledge required - perfect for busy small business owners.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 sm:gap-12 mb-12 sm:mb-16">
          {/* Step 1 */}
          <div className="text-center">
            <div className="relative mb-6 sm:mb-8">
              <div className="w-20 sm:w-24 h-20 sm:h-24 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 hover:scale-110 transition-transform duration-300">
                <Upload className="w-10 sm:w-12 h-10 sm:h-12 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold">
                1
              </div>
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-4 sm:mb-6">Upload Your Business Receipts</h3>
            <p className="text-gray-400 mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
              Drag and drop receipt images or PDFs from your phone, scanner, or computer. Connect your Gmail for automatic receipt detection, or link Google Drive for seamless imports.
            </p>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3 sm:p-4">
              <p className="text-xs sm:text-sm text-gray-500">
                <strong>Supports:</strong> JPG, PNG, PDF, WebP • Poor quality scans • Handwritten receipts
              </p>
            </div>
          </div>

          {/* Step 2 */}
          <div className="text-center">
            <div className="relative mb-6 sm:mb-8">
              <div className="w-20 sm:w-24 h-20 sm:h-24 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 hover:scale-110 transition-transform duration-300">
                <Zap className="w-10 sm:w-12 h-10 sm:h-12 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                2
              </div>
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-4 sm:mb-6">AI Extracts All Business Data</h3>
            <p className="text-gray-400 mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
              Our advanced AI reads and extracts vendor name, date, items, prices, tax amounts, and payment method. Perfect for small business expense categorization and tax preparation.
            </p>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3 sm:p-4">
              <p className="text-xs sm:text-sm text-gray-500">
                <strong>Processing time:</strong> 3-5 seconds • <strong>Accuracy:</strong> 99% • <strong>Review:</strong> Always editable
              </p>
            </div>
          </div>

          {/* Step 3 */}
          <div className="text-center">
            <div className="relative mb-6 sm:mb-8">
              <div className="w-20 sm:w-24 h-20 sm:h-24 bg-gradient-to-r from-blue-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 hover:scale-110 transition-transform duration-300">
                <BarChart3 className="w-10 sm:w-12 h-10 sm:h-12 text-white" />
              </div>
              <div className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                3
              </div>
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-4 sm:mb-6">Organized in Google Sheets</h3>
            <p className="text-gray-400 mb-4 sm:mb-6 text-sm sm:text-base leading-relaxed">
              Extracted data automatically appears in your Google Sheets, organized by year with smart categorization. Perfect for small business financial tracking, budgeting, and tax preparation.
            </p>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-3 sm:p-4">
              <p className="text-xs sm:text-sm text-gray-500">
                <strong>Features:</strong> Real-time sync • Auto-categorization • Year-based organization • Tax-ready format
              </p>
            </div>
          </div>
        </div>

        {/* Small Business Benefits Callout */}
        <div className="bg-gradient-to-r from-pink-500/10 to-purple-600/10 backdrop-blur-md border border-pink-500/20 rounded-2xl p-6 sm:p-8 mb-8 sm:mb-12">
          <div className="text-center">
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-4">
              Perfect for Small Business Owners
            </h3>
            <p className="text-gray-300 text-sm sm:text-base mb-6 max-w-2xl mx-auto">
              No technical knowledge required. No complex setup. No monthly maintenance. 
              Just upload your receipts and focus on growing your business while we handle the data organization.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">✓ No Training Required</span>
              <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">✓ Works on Any Device</span>
              <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">✓ Instant Setup</span>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <Link
            href="/login"
            className="inline-flex items-center gap-2 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-3 sm:py-4 px-6 sm:px-8 rounded-lg text-base sm:text-lg transition-all duration-200 transform hover:scale-105"
          >
            Try It Free for Your Business
            <ArrowRight className="w-4 sm:w-5 h-4 sm:h-5" />
          </Link>
          <p className="text-gray-400 text-xs sm:text-sm mt-3">
            Start with 10 free receipts • No credit card required • Upgrade anytime
          </p>
        </div>
      </div>
    </section>
  )
}