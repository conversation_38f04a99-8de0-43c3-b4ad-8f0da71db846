import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft, Calendar, Clock, User, CheckCircle, AlertTriangle } from 'lucide-react'

export const metadata: Metadata = {
  title: "How to Prepare for Tax Season: A Small Business Guide | ReceiptLabs",
  description: "Get your business ready for tax season with our comprehensive checklist and organization tips. Maximize deductions and minimize stress.",
  keywords: "tax preparation, small business taxes, tax season checklist, business deductions, tax organization",
}

export default function TaxSeasonPreparationPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-4 sm:py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/">
              <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link
              href="/blog"
              className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Blog
            </Link>
            <Link
              href="/login"
              className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
            >
              Sign In
            </Link>
          </div>
        </div>
      </header>

      {/* Article */}
      <article className="relative z-10 px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Article Header */}
          <header className="mb-8">
            <div className="flex flex-wrap items-center gap-4 mb-6 text-sm text-gray-400">
              <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">
                Tax Preparation
              </span>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                January 10, 2025
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                8 min read
              </div>
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                ReceiptLabs Team
              </div>
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              How to Prepare for Tax Season: A Small Business Guide
            </h1>
            
            <p className="text-lg sm:text-xl text-gray-300 leading-relaxed">
              Get your business ready for tax season with our comprehensive checklist and organization tips. 
              Maximize deductions, minimize stress, and ensure compliance with this step-by-step guide.
            </p>
          </header>

          {/* Article Content */}
          <div className="prose prose-lg prose-invert max-w-none">
            <div className="bg-orange-500/10 border border-orange-500/20 rounded-2xl p-6 sm:p-8 mb-8">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-6 h-6 text-orange-400 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-orange-300 font-bold text-lg mb-2">Tax Season Timeline</h3>
                  <p className="text-orange-200">
                    Tax season typically runs from January to April 15th. Start preparing now to avoid last-minute stress 
                    and ensure you don't miss any valuable deductions.
                  </p>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Essential Documents Checklist</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-6">
                Gather these essential documents before meeting with your accountant or filing your taxes:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-white font-semibold mb-3">Income Documents</h4>
                  <div className="space-y-2">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">1099 forms from clients</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">Bank statements</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">Sales records</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">Investment income statements</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="text-white font-semibold mb-3">Expense Documents</h4>
                  <div className="space-y-2">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">All business receipts</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">Mileage logs</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">Home office expenses</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-300 text-sm">Equipment purchase records</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Maximize Your Business Deductions</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Don't leave money on the table. Here are commonly overlooked business deductions:
              </p>
              
              <div className="space-y-4">
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <h4 className="text-green-300 font-semibold mb-2">Office & Equipment</h4>
                  <ul className="text-green-200 space-y-1 text-sm">
                    <li>• Home office expenses (if you work from home)</li>
                    <li>• Computer, software, and equipment purchases</li>
                    <li>• Office supplies and furniture</li>
                    <li>• Internet and phone bills (business portion)</li>
                  </ul>
                </div>
                
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                  <h4 className="text-blue-300 font-semibold mb-2">Travel & Transportation</h4>
                  <ul className="text-blue-200 space-y-1 text-sm">
                    <li>• Business mileage (57.5¢ per mile for 2023)</li>
                    <li>• Parking fees and tolls</li>
                    <li>• Business travel expenses</li>
                    <li>• Client meeting meals (50% deductible)</li>
                  </ul>
                </div>
                
                <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
                  <h4 className="text-purple-300 font-semibold mb-2">Professional Services</h4>
                  <ul className="text-purple-200 space-y-1 text-sm">
                    <li>• Accounting and bookkeeping fees</li>
                    <li>• Legal and professional consultations</li>
                    <li>• Business insurance premiums</li>
                    <li>• Marketing and advertising costs</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Organize Your Records</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Proper organization saves time and ensures you don't miss any deductions:
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold">Separate Business and Personal</h4>
                    <p className="text-gray-300 text-sm">Keep business and personal expenses completely separate to avoid complications.</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold">Categorize Everything</h4>
                    <p className="text-gray-300 text-sm">Use consistent categories that align with tax form line items.</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold">Digital Backup</h4>
                    <p className="text-gray-300 text-sm">Scan and store digital copies of all important documents.</p>
                  </div>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Common Tax Mistakes to Avoid</h2>
            <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6 mb-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-red-300 font-semibold">Missing Receipts</h4>
                    <p className="text-red-200 text-sm">Without proper documentation, you can't claim deductions. Keep all receipts!</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-red-300 font-semibold">Mixing Personal and Business</h4>
                    <p className="text-red-200 text-sm">This creates complications and may trigger audits. Keep them separate.</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="text-red-300 font-semibold">Waiting Until the Last Minute</h4>
                    <p className="text-red-200 text-sm">Rushing leads to mistakes and missed deductions. Start early!</p>
                  </div>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Tax Preparation Timeline</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="bg-pink-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    1
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">January - February</h4>
                    <p className="text-gray-300 text-sm">Gather all documents, organize receipts, and review last year's return.</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    2
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">March</h4>
                    <p className="text-gray-300 text-sm">Meet with your accountant or begin preparing your return.</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    3
                  </div>
                  <div>
                    <h4 className="text-white font-semibold">Early April</h4>
                    <p className="text-gray-300 text-sm">File your return and make any necessary payments before the deadline.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="bg-gradient-to-r from-pink-500/10 to-purple-600/10 backdrop-blur-md border border-pink-500/20 rounded-2xl p-6 sm:p-8 mt-12">
              <h3 className="text-2xl font-bold text-white mb-4">Simplify Your Tax Preparation</h3>
              <p className="text-gray-300 mb-6">
                ReceiptLabs automatically organizes your business expenses throughout the year, making tax season stress-free. 
                All your receipts are categorized and ready for your accountant.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/expense-management-small-business"
                  className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 text-center"
                >
                  Automate Your Expense Tracking
                </Link>
                <Link
                  href="/login"
                  className="border border-gray-600 hover:border-gray-500 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 text-center"
                >
                  Start Free Trial
                </Link>
              </div>
            </div>
          </div>
        </div>
      </article>

      {/* Footer */}
      <footer className="relative z-10 px-4 py-12 border-t border-gray-800 mt-20">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-gray-400 text-sm">
            © 2025 <strong>Receipt</strong>Labs. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}