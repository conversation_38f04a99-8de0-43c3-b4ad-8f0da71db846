import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import HeroSection from '@/components/landing/HeroSection'
import FeaturesSection from '@/components/landing/FeaturesSection'
import BenefitsSection from '@/components/landing/BenefitsSection'
import TestimonialsSection from '@/components/landing/TestimonialsSection'
import PricingSection from '@/components/landing/PricingSection'
import FAQSection from '@/components/landing/FAQSection'
import CTASection from '@/components/landing/CTASection'
import { expenseManagementContent } from '@/lib/data/expense-management-content'

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  }),
}))

describe('Expense Management Landing Page Components', () => {
  describe('HeroSection', () => {
    const heroProps = expenseManagementContent.hero

    it('renders the main headline correctly', () => {
      render(<HeroSection {...heroProps} />)
      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
        'Streamline Expense Management for Your Small Business'
      )
    })

    it('displays trust indicators', () => {
      render(<HeroSection {...heroProps} />)
      expect(screen.getByText('99% Accuracy')).toBeInTheDocument()
      expect(screen.getByText('Instant Processing')).toBeInTheDocument()
      expect(screen.getByText('Bank-Level Security')).toBeInTheDocument()
    })

    it('renders primary and secondary CTAs', () => {
      render(<HeroSection {...heroProps} />)
      expect(screen.getByText('Start Your Free Plan')).toBeInTheDocument()
      expect(screen.getByText('Watch Demo')).toBeInTheDocument()
    })

    it('displays the 3-step process visualization', () => {
      render(<HeroSection {...heroProps} />)
      expect(screen.getByText('1. Upload Receipts')).toBeInTheDocument()
      expect(screen.getByText('2. AI Extracts Data')).toBeInTheDocument()
      expect(screen.getByText('3. Organized in Sheets')).toBeInTheDocument()
    })
  })

  describe('FeaturesSection', () => {
    it('renders all features from content', () => {
      render(<FeaturesSection features={expenseManagementContent.features} />)
      
      expenseManagementContent.features.forEach(feature => {
        expect(screen.getByText(feature.title)).toBeInTheDocument()
        expect(screen.getByText(feature.description)).toBeInTheDocument()
      })
    })

    it('displays NEW badge for new features', () => {
      render(<FeaturesSection features={expenseManagementContent.features} />)
      const newFeature = expenseManagementContent.features.find(f => f.isNew)
      if (newFeature) {
        expect(screen.getByText('NEW')).toBeInTheDocument()
      }
    })

    it('shows plan requirements for premium features', () => {
      render(<FeaturesSection features={expenseManagementContent.features} />)
      const businessFeature = expenseManagementContent.features.find(f => f.planRequirement === 'business')
      if (businessFeature) {
        expect(screen.getByText('Business Plan')).toBeInTheDocument()
      }
    })
  })

  describe('BenefitsSection', () => {
    it('renders all benefits with proper structure', () => {
      render(<BenefitsSection benefits={expenseManagementContent.benefits} />)
      
      expenseManagementContent.benefits.forEach(benefit => {
        expect(screen.getByText(benefit.title)).toBeInTheDocument()
        expect(screen.getByText(benefit.description)).toBeInTheDocument()
      })
    })

    it('displays statistics section', () => {
      render(<BenefitsSection benefits={expenseManagementContent.benefits} />)
      expect(screen.getByText('10+')).toBeInTheDocument()
      expect(screen.getByText('Hours Saved Monthly')).toBeInTheDocument()
      expect(screen.getByText('99%')).toBeInTheDocument()
      expect(screen.getByText('Data Accuracy')).toBeInTheDocument()
    })
  })

  describe('TestimonialsSection', () => {
    it('renders all testimonials', () => {
      render(<TestimonialsSection testimonials={expenseManagementContent.testimonials} />)
      
      expenseManagementContent.testimonials.forEach(testimonial => {
        expect(screen.getByText(`"${testimonial.quote}"`)).toBeInTheDocument()
        expect(screen.getByText(testimonial.author.name)).toBeInTheDocument()
        expect(screen.getByText(testimonial.author.title)).toBeInTheDocument()
      })
    })

    it('displays star ratings for testimonials', () => {
      render(<TestimonialsSection testimonials={expenseManagementContent.testimonials} />)
      // Should have 5 stars for each testimonial (assuming all are 5-star)
      const stars = screen.getAllByRole('img', { hidden: true }) // Lucide icons are rendered as SVGs
      expect(stars.length).toBeGreaterThan(0)
    })
  })

  describe('PricingSection', () => {
    it('renders all pricing plans', () => {
      render(<PricingSection pricing={expenseManagementContent.pricing} />)
      
      expenseManagementContent.pricing.forEach(plan => {
        expect(screen.getByText(plan.name)).toBeInTheDocument()
        expect(screen.getByText(plan.ctaText)).toBeInTheDocument()
      })
    })

    it('highlights the most popular plan', () => {
      render(<PricingSection pricing={expenseManagementContent.pricing} />)
      const popularPlan = expenseManagementContent.pricing.find(p => p.isPopular)
      if (popularPlan) {
        expect(screen.getByText('Most Popular for Small Business')).toBeInTheDocument()
      }
    })

    it('displays pricing in KES currency', () => {
      render(<PricingSection pricing={expenseManagementContent.pricing} />)
      expect(screen.getByText('KES 0')).toBeInTheDocument()
      expect(screen.getByText('KES 4,999')).toBeInTheDocument()
      expect(screen.getByText('KES 6,999')).toBeInTheDocument()
    })
  })

  describe('FAQSection', () => {
    it('renders all FAQ questions', () => {
      render(<FAQSection faqs={expenseManagementContent.faqs} />)
      
      expenseManagementContent.faqs.forEach(faq => {
        expect(screen.getByText(faq.question)).toBeInTheDocument()
      })
    })

    it('expands FAQ answers when clicked', async () => {
      render(<FAQSection faqs={expenseManagementContent.faqs} />)
      
      const firstQuestion = screen.getByText(expenseManagementContent.faqs[0].question)
      fireEvent.click(firstQuestion)
      
      await waitFor(() => {
        expect(screen.getByText(expenseManagementContent.faqs[0].answer)).toBeInTheDocument()
      })
    })

    it('collapses FAQ when clicked again', async () => {
      render(<FAQSection faqs={expenseManagementContent.faqs} />)
      
      const firstQuestion = screen.getByText(expenseManagementContent.faqs[0].question)
      
      // Open
      fireEvent.click(firstQuestion)
      await waitFor(() => {
        expect(screen.getByText(expenseManagementContent.faqs[0].answer)).toBeInTheDocument()
      })
      
      // Close
      fireEvent.click(firstQuestion)
      await waitFor(() => {
        expect(screen.queryByText(expenseManagementContent.faqs[0].answer)).not.toBeInTheDocument()
      })
    })
  })

  describe('CTASection', () => {
    it('renders the final call-to-action', () => {
      render(<CTASection />)
      expect(screen.getByText('Ready to Transform Your Small Business')).toBeInTheDocument()
      expect(screen.getByText('Start Your Free Plan Today')).toBeInTheDocument()
    })

    it('displays trust indicators and guarantees', () => {
      render(<CTASection />)
      expect(screen.getByText('No credit card required')).toBeInTheDocument()
      expect(screen.getByText('30-day money-back guarantee')).toBeInTheDocument()
    })

    it('shows social proof statistics', () => {
      render(<CTASection />)
      expect(screen.getByText('500+')).toBeInTheDocument()
      expect(screen.getByText('Small Businesses Trust Us')).toBeInTheDocument()
    })
  })
})

describe('SEO and Accessibility', () => {
  it('has proper heading hierarchy', () => {
    render(<HeroSection {...expenseManagementContent.hero} />)
    const h1 = screen.getByRole('heading', { level: 1 })
    expect(h1).toBeInTheDocument()
  })

  it('has accessible button labels', () => {
    render(<CTASection />)
    const ctaButton = screen.getByRole('link', { name: /start your free plan today/i })
    expect(ctaButton).toBeInTheDocument()
  })

  it('includes alt text for images', () => {
    render(<HeroSection {...expenseManagementContent.hero} />)
    // Trust indicators should have accessible icons
    const trustIndicators = screen.getAllByText(/accuracy|processing|security/i)
    expect(trustIndicators.length).toBeGreaterThan(0)
  })
})

describe('Responsive Design', () => {
  it('renders mobile-friendly components', () => {
    // Test that components render without errors on different screen sizes
    render(<HeroSection {...expenseManagementContent.hero} />)
    render(<FeaturesSection features={expenseManagementContent.features} />)
    render(<PricingSection pricing={expenseManagementContent.pricing} />)
    
    // Basic smoke test - if components render without throwing, responsive classes are working
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument()
  })
})

describe('Performance', () => {
  it('renders components efficiently', () => {
    const startTime = performance.now()
    
    render(
      <>
        <HeroSection {...expenseManagementContent.hero} />
        <FeaturesSection features={expenseManagementContent.features} />
        <BenefitsSection benefits={expenseManagementContent.benefits} />
        <TestimonialsSection testimonials={expenseManagementContent.testimonials} />
        <PricingSection pricing={expenseManagementContent.pricing} />
        <FAQSection faqs={expenseManagementContent.faqs} />
        <CTASection />
      </>
    )
    
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    // Ensure rendering takes less than 100ms (reasonable for component rendering)
    expect(renderTime).toBeLessThan(100)
  })
})