'use client';

import { useState, useEffect } from 'react';
import { 
  DollarSign, 
  Receipt, 
  TrendingUp, 
  Users, 
  CreditCard, 
  Pie<PERSON><PERSON>,
  BarChart3,
  Download,
  RefreshCw
} from 'lucide-react';
import TimePeriodSelector from './TimePeriodSelector';
import { AnalyticsFilters, EnhancedAnalyticsData } from '@/lib/data/analytics';
import { StatsCard } from '@/components/dashboard/analytics/StatsCard';
import EnhancedSpending<PERSON>hart from './EnhancedSpendingChart';
import VendorAnalysis<PERSON>hart from './VendorAnalysisChart';
import CategoryBreakdownChart from './CategoryBreakdownChart';
import PaymentMethodChart from './PaymentMethodChart';
import RecentActivityChart from './RecentActivityChart';
import CompanyAnalysisSection from './CompanyAnalysisSection';

export default function AnalyticsPageClient() {
  const [filters, setFilters] = useState<AnalyticsFilters>({
    period: 'month',
  });
  
  const [analyticsData, setAnalyticsData] = useState<EnhancedAnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/analytics/enhanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }

      const data = await response.json();
      setAnalyticsData(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching analytics:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, [filters]);

  const handleFiltersChange = (newFilters: AnalyticsFilters) => {
    setFilters(newFilters);
  };

  const handleExport = async (format: 'pdf' | 'csv') => {
    try {
      const response = await fetch(`/api/analytics/export?format=${format}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters),
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `analytics-${new Date().toISOString().slice(0, 10)}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Export error:', err);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: analyticsData?.currency || 'KES',
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-pink"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6 text-center">
        <p className="text-red-400 mb-4">{error}</p>
        <button
          onClick={fetchAnalyticsData}
          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors duration-200"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center text-gray-400 py-12">
        <p>No analytics data available</p>
      </div>
    );
  }

  const changeIndicator = analyticsData.monthlyComparison.percentageChange;
  const isPositive = changeIndicator >= 0;

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div className="flex-1">
          <TimePeriodSelector filters={filters} onFiltersChange={handleFiltersChange} />
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={fetchAnalyticsData}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleExport('csv')}
              className="flex items-center space-x-2 px-4 py-2 bg-accent-purple hover:bg-purple-700 text-white rounded-lg transition-colors duration-200"
            >
              <Download className="w-4 h-4" />
              <span>CSV</span>
            </button>
            <button
              onClick={() => handleExport('pdf')}
              className="flex items-center space-x-2 px-4 py-2 bg-accent-pink hover:bg-pink-700 text-white rounded-lg transition-colors duration-200"
            >
              <Download className="w-4 h-4" />
              <span>PDF</span>
            </button>
          </div>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Spending"
          value={formatCurrency(analyticsData.totalSpending)}
          icon={DollarSign}
          description={`${isPositive ? '+' : ''}${changeIndicator.toFixed(1)}% from last month`}
          trend={isPositive ? 'up' : 'down'}
        />
        <StatsCard
          title="Receipts Processed"
          value={`${analyticsData.receiptCount}`}
          icon={Receipt}
          description={`${analyticsData.processingStats.successRate.toFixed(1)}% success rate`}
        />
        <StatsCard
          title="Average Receipt"
          value={formatCurrency(analyticsData.averageReceiptValue)}
          icon={TrendingUp}
          description="Per receipt average"
        />
        <StatsCard
          title="Top Vendor"
          value={analyticsData.topVendor.vendor}
          icon={Users}
          description={`${analyticsData.topVendor.count} receipts`}
        />
      </div>

      {/* Charts Grid */}
      <div className="grid gap-6 lg:grid-cols-2">
        <EnhancedSpendingChart 
          data={analyticsData.spendingTrend} 
          currency={analyticsData.currency}
        />
        <CategoryBreakdownChart 
          data={analyticsData.topCategories} 
          currency={analyticsData.currency}
        />
        <VendorAnalysisChart 
          data={analyticsData.vendorAnalysis} 
          currency={analyticsData.currency}
        />
        <PaymentMethodChart 
          data={analyticsData.paymentMethodBreakdown} 
          currency={analyticsData.currency}
        />
      </div>

      {/* Company Analysis Section */}
      <CompanyAnalysisSection />

      {/* Recent Activity */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <RecentActivityChart
            data={analyticsData.recentActivity}
            currency={analyticsData.currency}
          />
        </div>
        
        {/* Processing Stats */}
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-accent-purple" />
            Processing Statistics
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Success Rate</span>
              <span className="text-green-400 font-medium">
                {analyticsData.processingStats.successRate.toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Total Processed</span>
              <span className="text-white font-medium">
                {analyticsData.processingStats.totalProcessed}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Failed</span>
              <span className="text-red-400 font-medium">
                {analyticsData.processingStats.failed}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-400">Pending</span>
              <span className="text-yellow-400 font-medium">
                {analyticsData.processingStats.pending}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
