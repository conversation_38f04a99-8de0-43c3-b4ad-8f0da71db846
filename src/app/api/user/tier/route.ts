import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get user tier information
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status, monthly_receipt_limit, receipts_used_this_period, marketing_override')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({
      current_tier: userData.current_tier,
      subscription_status: userData.subscription_status,
      monthly_receipt_limit: userData.monthly_receipt_limit,
      receipts_used_this_period: userData.receipts_used_this_period,
      marketing_override: userData.marketing_override
    });
  } catch (error) {
    console.error('Error getting user tier:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get user tier' 
      }, 
      { status: 500 }
    );
  }
}
