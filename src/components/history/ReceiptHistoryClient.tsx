'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClient } from '@/lib/supabase/client';
import { Search, Filter, Grid, List, Trash2, Download, RefreshCw } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import ReceiptCard from './ReceiptCard';
import ReceiptFilters from './ReceiptFilters';
import ReceiptDetailModal from './ReceiptDetailModal';
import BulkDeleteModal from './BulkDeleteModal';
import { cn } from '@/lib/utils';

export interface Receipt {
  id: string;
  user_id: string;
  original_file_name: string;
  file_path: string;
  file_size: number | null;
  mime_type: string | null;
  vendor: string | null;
  vendor_tax_id: string | null;
  receipt_date: string | null;
  currency: string;
  payment_method: string | null;
  subtotal: number | null;
  tax_rate_percent: number | null;
  tax_amount: number | null;
  total_amount: number | null;
  paid_amount: number | null;
  processing_status: string;
  confidence_score: number | null;
  extraction_method: string | null;
  error_message: string | null;
  google_sheet_row_number: number | null;
  redis_job_id: string | null;
  created_at: string;
  updated_at: string;
}

interface ReceiptStats {
  total: number;
  processed: number;
  pending: number;
  failed: number;
}

interface FilterState {
  search: string;
  status: string;
  dateFrom: string;
  dateTo: string;
  amountMin: string;
  amountMax: string;
  vendor: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

const ITEMS_PER_PAGE = 12;

export default function ReceiptHistoryClient() {
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [filteredReceipts, setFilteredReceipts] = useState<Receipt[]>([]);
  const [stats, setStats] = useState<ReceiptStats>({ total: 0, processed: 0, pending: 0, failed: 0 });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedReceipts, setSelectedReceipts] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);
  const [selectedReceipt, setSelectedReceipt] = useState<Receipt | null>(null);
  const [showBulkDelete, setShowBulkDelete] = useState(false);
  
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    status: '',
    dateFrom: '',
    dateTo: '',
    amountMin: '',
    amountMax: '',
    vendor: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  const supabase = createClient();

  // Fetch receipts from the database
  const fetchReceipts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/receipts/detailed');
      if (!response.ok) {
        throw new Error('Failed to fetch receipts');
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch receipts');
      }

      setReceipts(data.receipts || []);

      // Calculate stats
      const total = data.receipts?.length || 0;
      const processed = data.receipts?.filter((r: Receipt) => r.processing_status === 'completed').length || 0;
      const pending = data.receipts?.filter((r: Receipt) => ['pending', 'processing', 'queued'].includes(r.processing_status)).length || 0;
      const failed = data.receipts?.filter((r: Receipt) => r.processing_status === 'failed').length || 0;

      setStats({ total, processed, pending, failed });
    } catch (err) {
      console.error('Error fetching receipts:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch receipts');
    } finally {
      setLoading(false);
    }
  }, []);

  // Apply filters and sorting
  const applyFilters = useCallback(() => {
    let filtered = [...receipts];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(receipt => 
        receipt.vendor?.toLowerCase().includes(searchLower) ||
        receipt.original_file_name.toLowerCase().includes(searchLower) ||
        receipt.total_amount?.toString().includes(searchLower)
      );
    }

    // Status filter
    if (filters.status) {
      filtered = filtered.filter(receipt => receipt.processing_status === filters.status);
    }

    // Date range filter
    if (filters.dateFrom) {
      filtered = filtered.filter(receipt => 
        receipt.receipt_date && receipt.receipt_date >= filters.dateFrom
      );
    }
    if (filters.dateTo) {
      filtered = filtered.filter(receipt => 
        receipt.receipt_date && receipt.receipt_date <= filters.dateTo
      );
    }

    // Amount range filter
    if (filters.amountMin) {
      const minAmount = parseFloat(filters.amountMin);
      filtered = filtered.filter(receipt => 
        receipt.total_amount && receipt.total_amount >= minAmount
      );
    }
    if (filters.amountMax) {
      const maxAmount = parseFloat(filters.amountMax);
      filtered = filtered.filter(receipt => 
        receipt.total_amount && receipt.total_amount <= maxAmount
      );
    }

    // Vendor filter
    if (filters.vendor) {
      filtered = filtered.filter(receipt => 
        receipt.vendor?.toLowerCase().includes(filters.vendor.toLowerCase())
      );
    }

    // Sorting
    filtered.sort((a, b) => {
      let aValue: any = a[filters.sortBy as keyof Receipt];
      let bValue: any = b[filters.sortBy as keyof Receipt];

      // Handle null values
      if (aValue === null) aValue = '';
      if (bValue === null) bValue = '';

      // Handle different data types
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredReceipts(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [receipts, filters]);

  // Load receipts on component mount
  useEffect(() => {
    fetchReceipts();
  }, [fetchReceipts]);

  // Apply filters when receipts or filters change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Handle receipt selection
  const handleReceiptSelect = (receiptId: string, selected: boolean) => {
    setSelectedReceipts(prev => {
      const newSet = new Set(prev);
      if (selected) {
        newSet.add(receiptId);
      } else {
        newSet.delete(receiptId);
      }
      return newSet;
    });
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      const currentPageReceipts = getCurrentPageReceipts();
      setSelectedReceipts(new Set(currentPageReceipts.map(r => r.id)));
    } else {
      setSelectedReceipts(new Set());
    }
  };

  // Get current page receipts
  const getCurrentPageReceipts = () => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return filteredReceipts.slice(startIndex, endIndex);
  };

  // Calculate pagination
  const totalPages = Math.ceil(filteredReceipts.length / ITEMS_PER_PAGE);
  const currentPageReceipts = getCurrentPageReceipts();

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <RefreshCw className="w-8 h-8 text-pink-500 animate-spin" />
        <span className="ml-3 text-gray-400">Loading receipts...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-4">{error}</div>
        <button
          onClick={fetchReceipts}
          className="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Bar */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
          <div className="text-2xl font-bold text-white">{stats.total}</div>
          <div className="text-gray-400 text-sm">Total Receipts</div>
        </div>
        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
          <div className="text-2xl font-bold text-green-400">{stats.processed}</div>
          <div className="text-gray-400 text-sm">Processed</div>
        </div>
        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
          <div className="text-2xl font-bold text-yellow-400">{stats.pending}</div>
          <div className="text-gray-400 text-sm">Pending</div>
        </div>
        <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-4">
          <div className="text-2xl font-bold text-red-400">{stats.failed}</div>
          <div className="text-gray-400 text-sm">Failed</div>
        </div>
      </div>

      {/* Search and Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search receipts..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          {selectedReceipts.size > 0 && (
            <>
              <Badge className="bg-pink-600/20 text-pink-400">
                {selectedReceipts.size} selected
              </Badge>
              <button
                onClick={() => setShowBulkDelete(true)}
                className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-lg transition-colors text-sm"
              >
                <Trash2 className="w-4 h-4" />
                Delete
              </button>
            </>
          )}
          
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={cn(
              "flex items-center gap-2 px-3 py-2 rounded-lg transition-colors text-sm",
              showFilters 
                ? "bg-pink-600 text-white" 
                : "bg-gray-800 hover:bg-gray-700 text-gray-300"
            )}
          >
            <Filter className="w-4 h-4" />
            Filters
          </button>

          <div className="flex bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={cn(
                "p-2 rounded transition-colors",
                viewMode === 'grid' ? "bg-pink-600 text-white" : "text-gray-400 hover:text-white"
              )}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={cn(
                "p-2 rounded transition-colors",
                viewMode === 'list' ? "bg-pink-600 text-white" : "text-gray-400 hover:text-white"
              )}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <ReceiptFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          receipts={receipts}
        />
      )}

      {/* Results */}
      {filteredReceipts.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            {receipts.length === 0 ? 'No receipts found' : 'No receipts match your filters'}
          </div>
          {receipts.length === 0 && (
            <button
              onClick={() => window.location.href = '/dashboard/upload'}
              className="bg-pink-600 hover:bg-pink-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Upload Your First Receipt
            </button>
          )}
        </div>
      ) : (
        <>
          {/* Receipt Grid/List */}
          <div className={cn(
            viewMode === 'grid'
              ? "grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-3"
              : "space-y-4"
          )}>
            {currentPageReceipts.map((receipt) => (
              <ReceiptCard
                key={receipt.id}
                receipt={receipt}
                viewMode={viewMode}
                selected={selectedReceipts.has(receipt.id)}
                onSelect={(selected) => handleReceiptSelect(receipt.id, selected)}
                onClick={() => setSelectedReceipt(receipt)}
              />
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-8">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 bg-gray-800 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                Previous
              </button>
              
              <div className="flex gap-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={cn(
                      "px-3 py-2 rounded-lg transition-colors",
                      page === currentPage
                        ? "bg-pink-600 text-white"
                        : "bg-gray-800 hover:bg-gray-700 text-gray-300"
                    )}
                  >
                    {page}
                  </button>
                ))}
              </div>

              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 bg-gray-800 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}

      {/* Modals */}
      {selectedReceipt && (
        <ReceiptDetailModal
          receipt={selectedReceipt}
          onClose={() => setSelectedReceipt(null)}
        />
      )}

      {showBulkDelete && (
        <BulkDeleteModal
          selectedReceipts={Array.from(selectedReceipts)}
          receipts={receipts.filter(r => selectedReceipts.has(r.id))}
          onClose={() => setShowBulkDelete(false)}
          onSuccess={() => {
            setSelectedReceipts(new Set());
            setShowBulkDelete(false);
            fetchReceipts();
          }}
        />
      )}
    </div>
  );
}
