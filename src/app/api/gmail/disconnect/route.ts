import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { disconnectUserGmail } from '@/lib/gmail/auth';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user has Business tier access
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (userData.current_tier !== 'business') {
      return NextResponse.json({ error: 'Business plan required' }, { status: 403 });
    }

    // Disconnect Gmail
    await disconnectUserGmail(user.id);

    return NextResponse.json({
      success: true,
      message: 'Gmail disconnected successfully'
    });
  } catch (error) {
    console.error('Error disconnecting Gmail:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to disconnect Gmail' 
      }, 
      { status: 500 }
    );
  }
}
