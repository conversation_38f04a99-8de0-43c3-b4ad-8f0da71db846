import Link from 'next/link'

export default function AuthCodeError() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 shadow-2xl text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">Authentication Error</h1>
            <p className="text-gray-400">
              Sorry, we couldn't complete your sign-in. This could be due to:
            </p>
          </div>
          
          <div className="text-left mb-6 space-y-2">
            <div className="text-sm text-gray-300">
              • The authentication code has expired
            </div>
            <div className="text-sm text-gray-300">
              • The request was cancelled or interrupted
            </div>
            <div className="text-sm text-gray-300">
              • A temporary server issue occurred
            </div>
          </div>
          
          <div className="space-y-3">
            <Link
              href="/login"
              className="w-full inline-flex items-center justify-center bg-pink-600 hover:bg-pink-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
            >
              Try Again
            </Link>
            
            <Link
              href="/"
              className="w-full inline-flex items-center justify-center bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
            >
              Go Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
} 