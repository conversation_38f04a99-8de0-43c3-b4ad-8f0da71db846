'use client';

interface NotesSectionProps {
  notes: string;
  updateNotes: (notes: string) => void;
}

export default function NotesSection({ notes, updateNotes }: NotesSectionProps) {
  return (
    <div className="space-y-6">
      <div className="border-b border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-white mb-2">Notes</h2>
        <p className="text-gray-400 text-sm">Additional information or terms (optional)</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Notes / Terms
        </label>
        <textarea
          value={notes}
          onChange={(e) => updateNotes(e.target.value)}
          rows={3}
          className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent resize-none"
          placeholder="Thank you for your business!"
        />
      </div>
    </div>
  );
}
