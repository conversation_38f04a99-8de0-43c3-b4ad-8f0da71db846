#!/bin/bash

# Gmail Cron Setup Script for Reco Engine
# This script helps you set up the Gmail processing cron job

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}🚀 Gmail Cron Setup for Reco Engine${NC}"
echo -e "${BLUE}====================================${NC}"
echo ""

# Check if we're in the right directory
if [ ! -f "$PROJECT_DIR/package.json" ]; then
    echo -e "${RED}❌ Error: This script must be run from the Reco Engine project directory${NC}"
    echo -e "${RED}   Current directory: $PROJECT_DIR${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Project directory: $PROJECT_DIR${NC}"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Error: Node.js is not installed${NC}"
    echo -e "${YELLOW}   Please install Node.js first: https://nodejs.org/${NC}"
    exit 1
fi

NODE_VERSION=$(node --version)
echo -e "${GREEN}✅ Node.js version: $NODE_VERSION${NC}"

# Check if the Gmail cron script exists
CRON_SCRIPT="$PROJECT_DIR/scripts/gmail-cron.js"
if [ ! -f "$CRON_SCRIPT" ]; then
    echo -e "${RED}❌ Error: Gmail cron script not found at $CRON_SCRIPT${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Gmail cron script found${NC}"

# Make the script executable
chmod +x "$CRON_SCRIPT"
echo -e "${GREEN}✅ Made gmail-cron.js executable${NC}"

# Check environment variables
echo ""
echo -e "${BLUE}🔧 Checking Environment Variables${NC}"
echo -e "${BLUE}=================================${NC}"

ENV_FILE="$PROJECT_DIR/.env.local"
if [ ! -f "$ENV_FILE" ]; then
    echo -e "${RED}❌ Error: .env.local file not found${NC}"
    echo -e "${YELLOW}   Please create .env.local with required environment variables${NC}"
    exit 1
fi

# Check required environment variables
source "$ENV_FILE"

REQUIRED_VARS=(
    "NEXT_PUBLIC_SUPABASE_URL"=https://tgwppavwsgpoqsocqfal.supabase.co
    "SUPABASE_SERVICE_ROLE_KEY"=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRnd3BwYXZ3c2dwb3Fzb2NxZmFsIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDcxOTc5NywiZXhwIjoyMDY2Mjk1Nzk3fQ.pq0TEe9zvI-buQFF_AFfCelxbGdBWZnCtNtqoYZsPic
    "GEMINI_API_KEY"=AIzaSyAcAOIqunv-j6OjxjT2OXaHoOO3Y6T9pDE
)

MISSING_VARS=()

for var in "${REQUIRED_VARS[@]}"; do
    if [ -z "${!var}" ]; then
        MISSING_VARS+=("$var")
        echo -e "${RED}❌ Missing: $var${NC}"
    else
        echo -e "${GREEN}✅ Found: $var${NC}"
    fi
done

if [ ${#MISSING_VARS[@]} -ne 0 ]; then
    echo -e "${RED}❌ Error: Missing required environment variables${NC}"
    echo -e "${YELLOW}   Please add these to your .env.local file:${NC}"
    for var in "${MISSING_VARS[@]}"; do
        echo -e "${YELLOW}   - $var${NC}"
    done
    exit 1
fi

# Create logs directory
LOGS_DIR="$PROJECT_DIR/logs"
if [ ! -d "$LOGS_DIR" ]; then
    mkdir -p "$LOGS_DIR"
    echo -e "${GREEN}✅ Created logs directory: $LOGS_DIR${NC}"
else
    echo -e "${GREEN}✅ Logs directory exists: $LOGS_DIR${NC}"
fi

# Test the Gmail cron script
echo ""
echo -e "${BLUE}🧪 Testing Gmail Cron Script${NC}"
echo -e "${BLUE}============================${NC}"

echo -e "${YELLOW}⏳ Running test execution...${NC}"

# Set environment variables and run test
export $(cat "$ENV_FILE" | grep -v '^#' | xargs)

if cd "$PROJECT_DIR" && node "$CRON_SCRIPT"; then
    echo -e "${GREEN}✅ Gmail cron script test passed${NC}"
else
    echo -e "${RED}❌ Gmail cron script test failed${NC}"
    echo -e "${YELLOW}   Check the error messages above${NC}"
    echo -e "${YELLOW}   Make sure your app is running on localhost:3000${NC}"
    exit 1
fi

# Generate cron job entry
echo ""
echo -e "${BLUE}📅 Cron Job Configuration${NC}"
echo -e "${BLUE}=========================${NC}"

CRON_ENTRY="0 * * * * cd $PROJECT_DIR && /usr/bin/env node scripts/gmail-cron.js >> logs/gmail-cron.log 2>&1"

echo -e "${YELLOW}Add this line to your crontab (run 'crontab -e'):${NC}"
echo ""
echo -e "${GREEN}$CRON_ENTRY${NC}"
echo ""

# Offer to add to crontab automatically
echo -e "${YELLOW}Would you like to add this cron job automatically? (y/n)${NC}"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    # Check if cron job already exists
    if crontab -l 2>/dev/null | grep -q "gmail-cron.js"; then
        echo -e "${YELLOW}⚠️ Gmail cron job already exists in crontab${NC}"
        echo -e "${YELLOW}   Please check 'crontab -l' and remove duplicates if needed${NC}"
    else
        # Add to crontab
        (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
        echo -e "${GREEN}✅ Gmail cron job added to crontab${NC}"
    fi
else
    echo -e "${YELLOW}⏭️ Skipped automatic crontab setup${NC}"
    echo -e "${YELLOW}   You can add the cron job manually later${NC}"
fi

# Show current crontab
echo ""
echo -e "${BLUE}📋 Current Crontab${NC}"
echo -e "${BLUE}=================${NC}"

if crontab -l 2>/dev/null; then
    echo ""
else
    echo -e "${YELLOW}No crontab entries found${NC}"
fi

# Final instructions
echo ""
echo -e "${BLUE}🎉 Setup Complete!${NC}"
echo -e "${BLUE}=================${NC}"
echo ""
echo -e "${GREEN}✅ Gmail cron job is ready to run${NC}"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo -e "${YELLOW}1. Make sure your Reco Engine app is running${NC}"
echo -e "${YELLOW}2. The cron job will run every hour automatically${NC}"
echo -e "${YELLOW}3. Check logs at: $LOGS_DIR/gmail-cron.log${NC}"
echo -e "${YELLOW}4. Monitor processing in your app's Gmail page${NC}"
echo ""
echo -e "${YELLOW}Manual testing:${NC}"
echo -e "${YELLOW}  cd $PROJECT_DIR${NC}"
echo -e "${YELLOW}  node scripts/gmail-cron.js${NC}"
echo ""
echo -e "${YELLOW}View logs:${NC}"
echo -e "${YELLOW}  tail -f logs/gmail-cron.log${NC}"
echo ""
echo -e "${GREEN}🚀 Happy receipt processing!${NC}"
