// Analytics utility for tracking user interactions and conversions

export interface AnalyticsEvent {
  event: string;
  page?: string;
  section?: string;
  action?: string;
  value?: number;
  properties?: Record<string, any>;
}

export const trackEvent = (event: AnalyticsEvent) => {
  // Google Analytics 4 tracking
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', event.event, {
      page_title: event.page,
      page_location: window.location.href,
      section: event.section,
      action: event.action,
      value: event.value,
      ...event.properties
    });
  }

  // Console logging for development
  if (process.env.NODE_ENV === 'development') {
    console.log('Analytics Event:', event);
  }
}

// Predefined tracking events for the expense management landing page
export const trackLandingPageEvents = {
  // Hero section interactions
  heroCtaPrimary: () => trackEvent({
    event: 'cta_click',
    page: 'expense_management_landing',
    section: 'hero',
    action: 'primary_cta_click',
    properties: { cta_text: 'Start Your Free Plan' }
  }),

  heroCtaSecondary: () => trackEvent({
    event: 'cta_click', 
    page: 'expense_management_landing',
    section: 'hero',
    action: 'secondary_cta_click',
    properties: { cta_text: 'Watch Demo' }
  }),

  // Features section
  featureView: (featureId: string) => trackEvent({
    event: 'feature_view',
    page: 'expense_management_landing',
    section: 'features',
    action: 'feature_viewed',
    properties: { feature_id: featureId }
  }),

  // Pricing section
  pricingPlanClick: (planId: string, planName: string) => trackEvent({
    event: 'pricing_plan_click',
    page: 'expense_management_landing', 
    section: 'pricing',
    action: 'plan_selected',
    properties: { plan_id: planId, plan_name: planName }
  }),

  // FAQ interactions
  faqOpen: (faqId: string, question: string) => trackEvent({
    event: 'faq_interaction',
    page: 'expense_management_landing',
    section: 'faq',
    action: 'faq_opened',
    properties: { faq_id: faqId, question: question }
  }),

  // Final CTA
  finalCta: () => trackEvent({
    event: 'cta_click',
    page: 'expense_management_landing',
    section: 'final_cta',
    action: 'final_cta_click',
    properties: { cta_text: 'Start Your Free Plan Today' }
  }),

  // Page engagement
  pageView: () => trackEvent({
    event: 'page_view',
    page: 'expense_management_landing',
    properties: { 
      page_title: 'Expense Management for Small Business',
      referrer: document.referrer
    }
  }),

  scrollDepth: (depth: number) => trackEvent({
    event: 'scroll_depth',
    page: 'expense_management_landing',
    action: 'scroll_milestone',
    value: depth,
    properties: { scroll_depth: depth }
  })
}

// Scroll depth tracking
export const initScrollTracking = () => {
  if (typeof window === 'undefined') return;

  let maxScroll = 0;
  const milestones = [25, 50, 75, 90, 100];
  const tracked = new Set<number>();

  const handleScroll = () => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const scrollPercent = Math.round((scrollTop / docHeight) * 100);

    if (scrollPercent > maxScroll) {
      maxScroll = scrollPercent;
      
      milestones.forEach(milestone => {
        if (scrollPercent >= milestone && !tracked.has(milestone)) {
          tracked.add(milestone);
          trackLandingPageEvents.scrollDepth(milestone);
        }
      });
    }
  };

  window.addEventListener('scroll', handleScroll, { passive: true });
  
  return () => window.removeEventListener('scroll', handleScroll);
}

// Time on page tracking
export const initTimeTracking = () => {
  if (typeof window === 'undefined') return;

  const startTime = Date.now();
  const milestones = [30, 60, 120, 300]; // seconds
  const tracked = new Set<number>();

  const checkTimeSpent = () => {
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    
    milestones.forEach(milestone => {
      if (timeSpent >= milestone && !tracked.has(milestone)) {
        tracked.add(milestone);
        trackEvent({
          event: 'time_on_page',
          page: 'expense_management_landing',
          action: 'time_milestone',
          value: milestone,
          properties: { time_spent: milestone }
        });
      }
    });
  };

  const interval = setInterval(checkTimeSpent, 10000); // Check every 10 seconds
  
  return () => clearInterval(interval);
}