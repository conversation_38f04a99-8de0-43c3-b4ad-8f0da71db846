'use client';

import { CURRENCIES } from '../types';

interface CurrencySectionProps {
  currency: string;
  updateCurrency: (currency: string) => void;
}

export default function CurrencySection({ currency, updateCurrency }: CurrencySectionProps) {
  return (
    <div className="space-y-6">
      <div className="border-b border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-white mb-2">Currency</h2>
        <p className="text-gray-400 text-sm">Select the currency for your receipt</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Currency
        </label>
        <select
          value={currency}
          onChange={(e) => updateCurrency(e.target.value)}
          className="w-full md:w-64 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
        >
          {CURRENCIES.map((curr) => (
            <option key={curr.code} value={curr.code}>
              {curr.symbol} - {curr.name} ({curr.code})
            </option>
          ))}
        </select>
      </div>
    </div>
  );
}
