import { NextRequest } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getNotificationsForPolling, storeNotificationForPolling } from '@/lib/pubsub'

// Server-Sent Events for real-time notifications
export async function GET(request: NextRequest) {
  console.log('📡 SSE connection requested')
  
  try {
    // Verify user authentication
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      console.error('❌ Unauthorized SSE request')
      return new Response('Unauthorized', { status: 401 })
    }
    
    console.log('✅ SSE connection authorized for user:', user.id)
    
    // Create a readable stream for SSE
    const stream = new ReadableStream({
      start(controller) {
        console.log('🚀 Starting SSE stream for user:', user.id)
        
        // Send initial connection message
        const initialMessage = `data: ${JSON.stringify({
          type: 'connection',
          message: 'Connected to notification stream',
          timestamp: new Date().toISOString()
        })}\n\n`
        
        controller.enqueue(new TextEncoder().encode(initialMessage))
        
        // Set up efficient polling for notifications (only when needed)
        let pollInterval: NodeJS.Timeout | null = null
        let lastNotificationCount = 0

        const startPolling = () => {
          if (pollInterval) return

          pollInterval = setInterval(async () => {
            try {
              const notifications = await getNotificationsForPolling(user.id)

              // Only send if we have new notifications
              if (notifications.length > 0) {
                for (const notification of notifications) {
                  const message = `data: ${JSON.stringify({
                    type: 'notification',
                    data: notification,
                    timestamp: new Date().toISOString()
                  })}\n\n`

                  controller.enqueue(new TextEncoder().encode(message))
                  console.log('📤 Sent notification via SSE:', notification)
                }

                // Slow down polling after sending notifications
                setTimeout(() => {
                  if (pollInterval) {
                    clearInterval(pollInterval)
                    pollInterval = null
                    // Restart with slower polling
                    setTimeout(startPolling, 5000) // Wait 5 seconds before resuming
                  }
                }, 1000)
              }
            } catch (error) {
              console.error('❌ Error polling notifications:', error)
            }
          }, 2000) // Poll every 2 seconds (slower than before)
        }

        // Start polling
        startPolling()
        
        // Send heartbeat every 30 seconds to keep connection alive
        const heartbeatInterval = setInterval(() => {
          const heartbeat = `data: ${JSON.stringify({
            type: 'heartbeat',
            timestamp: new Date().toISOString()
          })}\n\n`
          
          controller.enqueue(new TextEncoder().encode(heartbeat))
        }, 30000)
        
        // Clean up on close
        const cleanup = () => {
          console.log('🔌 Cleaning up SSE connection for user:', user.id)
          if (pollInterval) {
            clearInterval(pollInterval)
            pollInterval = null
          }
          clearInterval(heartbeatInterval)
        }
        
        // Store cleanup function for later use
        ;(controller as any).cleanup = cleanup
        
        // Handle client disconnect
        request.signal.addEventListener('abort', () => {
          console.log('🔌 Client disconnected from SSE')
          cleanup()
          controller.close()
        })
      },
      
      cancel() {
        console.log('🔌 SSE stream cancelled')
        if ((this as any).cleanup) {
          (this as any).cleanup()
        }
      }
    })
    
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      },
    })
    
  } catch (error) {
    console.error('❌ SSE setup error:', error)
    return new Response('Internal Server Error', { status: 500 })
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
