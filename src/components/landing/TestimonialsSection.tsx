import { Star } from 'lucide-react'
import { Testimonial } from '@/lib/types/landing-page'

interface TestimonialsSectionProps {
  testimonials: Testimonial[];
}

export default function TestimonialsSection({ testimonials }: TestimonialsSectionProps) {
  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getAvatarGradient = (index: number) => {
    const gradients = [
      'from-pink-500 to-purple-600',
      'from-blue-500 to-green-600', 
      'from-purple-500 to-pink-600',
      'from-green-500 to-blue-600',
      'from-orange-500 to-red-600'
    ]
    return gradients[index % gradients.length]
  }

  return (
    <section className="relative z-10 px-4 py-12 sm:py-20">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6 px-2">
            Trusted by Small Businesses Everywhere
          </h2>
          <p className="text-base sm:text-xl text-gray-300 max-w-3xl mx-auto px-2">
            See how small business owners like you are transforming their expense management and saving valuable time every month.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8 mb-12 sm:mb-16">
          <div className="text-center">
            <div className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2">500+</div>
            <div className="text-gray-400 text-sm sm:text-base">Small Businesses</div>
          </div>
          <div className="text-center">
            <div className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2">25K+</div>
            <div className="text-gray-400 text-sm sm:text-base">Receipts Processed</div>
          </div>
          <div className="text-center">
            <div className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2">99%</div>
            <div className="text-gray-400 text-sm sm:text-base">Accuracy Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-2">10+</div>
            <div className="text-gray-400 text-sm sm:text-base">Hours Saved Monthly</div>
          </div>
        </div>

        {/* Testimonials */}
        <div className="grid md:grid-cols-3 gap-6 sm:gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={testimonial.id} className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl sm:rounded-2xl p-6 sm:p-8 hover:border-gray-600 transition-colors duration-300">
              <div className="flex items-center gap-1 mb-4 sm:mb-6">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-4 sm:w-5 h-4 sm:h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              
              <blockquote className="text-gray-300 mb-6 sm:mb-8 text-sm sm:text-base leading-relaxed">
                "{testimonial.quote}"
              </blockquote>
              
              <div className="flex items-center gap-3 sm:gap-4">
                <div className={`w-10 sm:w-12 h-10 sm:h-12 bg-gradient-to-r ${getAvatarGradient(index)} rounded-full flex items-center justify-center text-white font-bold text-sm sm:text-base`}>
                  {getInitials(testimonial.author.name)}
                </div>
                <div>
                  <div className="text-white font-semibold text-sm sm:text-base">
                    {testimonial.author.name}
                  </div>
                  <div className="text-gray-400 text-xs sm:text-sm">
                    {testimonial.author.title}
                  </div>
                  <div className="text-gray-500 text-xs">
                    {testimonial.author.company}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Small Business Focus Callout */}
        <div className="mt-12 sm:mt-16 bg-gradient-to-r from-blue-500/10 to-green-600/10 backdrop-blur-md border border-blue-500/20 rounded-2xl p-6 sm:p-8">
          <div className="text-center">
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-4">
              Join the Small Business Revolution
            </h3>
            <p className="text-gray-300 text-sm sm:text-base mb-6 max-w-2xl mx-auto">
              Small businesses across industries are choosing ReceiptLabs to streamline their expense management. 
              From restaurants to consulting firms, freelancers to retail stores - see why they're making the switch.
            </p>
            <div className="flex flex-wrap justify-center gap-3 sm:gap-4 text-xs sm:text-sm">
              <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">Restaurants</span>
              <span className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full">Consulting</span>
              <span className="bg-purple-500/20 text-purple-300 px-3 py-1 rounded-full">Freelancers</span>
              <span className="bg-pink-500/20 text-pink-300 px-3 py-1 rounded-full">Retail</span>
              <span className="bg-orange-500/20 text-orange-300 px-3 py-1 rounded-full">Services</span>
              <span className="bg-cyan-500/20 text-cyan-300 px-3 py-1 rounded-full">E-commerce</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}