import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    // Get all user's receipts to delete files
    const { data: receipts } = await supabase
      .from('receipts')
      .select('file_path')
      .eq('user_id', user.id);

    // Delete all receipt files from storage
    if (receipts && receipts.length > 0) {
      const filePaths = receipts.map(r => r.file_path);
      const { error: storageError } = await supabase.storage
        .from('receipts')
        .remove(filePaths);

      if (storageError) {
        console.warn('Error deleting files from storage:', storageError);
        // Don't fail the entire operation for storage errors
      }
    }

    // Delete user data in the correct order (respecting foreign key constraints)
    
    // 1. Get all receipt IDs for the user first
    const { data: userReceipts } = await supabase
      .from('receipts')
      .select('id')
      .eq('user_id', user.id);

    // 2. Delete receipt items if there are receipts
    if (userReceipts && userReceipts.length > 0) {
      const receiptIds = userReceipts.map(r => r.id);
      const { error: itemsError } = await supabase
        .from('receipt_items')
        .delete()
        .in('receipt_id', receiptIds);

      if (itemsError) {
        console.error('Error deleting receipt items:', itemsError);
        return NextResponse.json({
          success: false,
          error: 'Failed to delete receipt items'
        }, { status: 500 });
      }
    }

    // 3. Delete receipts
    const { error: receiptsError } = await supabase
      .from('receipts')
      .delete()
      .eq('user_id', user.id);

    if (receiptsError) {
      console.error('Error deleting receipts:', receiptsError);
      return NextResponse.json({
        success: false,
        error: 'Failed to delete receipts'
      }, { status: 500 });
    }

    // 4. Delete Google Sheets records
    const { error: sheetsError } = await supabase
      .from('google_sheets')
      .delete()
      .eq('user_id', user.id);

    if (sheetsError) {
      console.error('Error deleting Google Sheets records:', sheetsError);
      return NextResponse.json({
        success: false,
        error: 'Failed to delete Google Sheets records'
      }, { status: 500 });
    }

    // 5. Delete user preferences
    const { error: prefsError } = await supabase
      .from('user_preferences')
      .delete()
      .eq('user_id', user.id);

    if (prefsError) {
      console.error('Error deleting user preferences:', prefsError);
      // Don't fail for user preferences as it's not critical
    }

    // 6. Delete user record
    const { error: userError } = await supabase
      .from('users')
      .delete()
      .eq('id', user.id);

    if (userError) {
      console.error('Error deleting user record:', userError);
      return NextResponse.json({
        success: false,
        error: 'Failed to delete user account'
      }, { status: 500 });
    }

    // 7. Delete auth user (this should be done last)
    const { error: authError } = await supabase.auth.admin.deleteUser(user.id);

    if (authError) {
      console.error('Error deleting auth user:', authError);
      // User data is already deleted, so we'll consider this a success
      // The auth user might be cleaned up later
    }

    return NextResponse.json({
      success: true,
      message: 'Account deleted successfully'
    });

  } catch (error) {
    console.error('Delete account API error:', error);

    // Log the full error details for debugging
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    );
  }
}
