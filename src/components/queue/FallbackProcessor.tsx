'use client'

import { useEffect, useRef, useCallback } from 'react'

interface FallbackProcessorProps {
  enabled?: boolean
  cleanupIntervalMs?: number
  processingIntervalMs?: number
}

export default function FallbackProcessor({
  enabled = true,
  cleanupIntervalMs = 600000, // 10 minutes
  processingIntervalMs = 300000 // 5 minutes
}: FallbackProcessorProps) {
  const cleanupIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const processingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const isRunningRef = useRef(false)

  // Cleanup function for stuck jobs and expired data
  const runCleanup = useCallback(async () => {
    if (isRunningRef.current) {
      console.log('FallbackProcessor: Cleanup already running, skipping')
      return
    }

    isRunningRef.current = true

    try {
      console.log('🧹 FallbackProcessor: Running cleanup...')
      
      const response = await fetch('/api/fallback/cleanup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Fallback-Cleanup/1.0'
        }
      })

      if (response.ok) {
        const data = await response.json()
        console.log('✅ FallbackProcessor: Cleanup completed:', data.results)
        
        if (data.results.errors.length > 0) {
          console.warn('⚠️ FallbackProcessor: Cleanup had errors:', data.results.errors)
        }
      } else {
        console.error('❌ FallbackProcessor: Cleanup failed:', response.status)
      }

    } catch (error) {
      console.error('❌ FallbackProcessor: Cleanup error:', error)
    } finally {
      isRunningRef.current = false
    }
  }, [])

  // Processing function for stuck jobs
  const runProcessing = useCallback(async () => {
    if (isRunningRef.current) {
      console.log('FallbackProcessor: Processing already running, skipping')
      return
    }

    isRunningRef.current = true

    try {
      console.log('🔄 FallbackProcessor: Checking for stuck jobs...')
      
      const response = await fetch('/api/pipeline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Fallback-Processor/1.0'
        }
      })

      if (response.ok) {
        const data = await response.json()
        
        if (data.success) {
          console.log('✅ FallbackProcessor: Processed stuck job:', data.jobId)
          
          // If we found a job, check again sooner
          setTimeout(() => {
            if (!isRunningRef.current) {
              runProcessing()
            }
          }, 30000) // 30 seconds
        } else {
          console.log('ℹ️ FallbackProcessor: No stuck jobs found')
        }
      } else {
        console.error('❌ FallbackProcessor: Processing failed:', response.status)
      }

    } catch (error) {
      console.error('❌ FallbackProcessor: Processing error:', error)
    } finally {
      isRunningRef.current = false
    }
  }, [])

  // Schedule next cleanup
  const scheduleCleanup = useCallback(() => {
    if (cleanupIntervalRef.current) {
      clearTimeout(cleanupIntervalRef.current)
    }

    cleanupIntervalRef.current = setTimeout(() => {
      runCleanup()
      scheduleCleanup() // Schedule next cleanup
    }, cleanupIntervalMs)
  }, [runCleanup, cleanupIntervalMs])

  // Schedule next processing
  const scheduleProcessing = useCallback(() => {
    if (processingIntervalRef.current) {
      clearTimeout(processingIntervalRef.current)
    }

    processingIntervalRef.current = setTimeout(() => {
      runProcessing()
      scheduleProcessing() // Schedule next processing
    }, processingIntervalMs)
  }, [runProcessing, processingIntervalMs])

  // Cleanup function
  const cleanup = useCallback(() => {
    console.log('🧹 FallbackProcessor: Cleaning up intervals...')

    if (cleanupIntervalRef.current) {
      clearTimeout(cleanupIntervalRef.current)
      cleanupIntervalRef.current = null
    }

    if (processingIntervalRef.current) {
      clearTimeout(processingIntervalRef.current)
      processingIntervalRef.current = null
    }
  }, [])

  // Setup intervals on mount
  useEffect(() => {
    if (enabled) {
      console.log('🚀 FallbackProcessor: Starting fallback monitoring...', {
        cleanupInterval: `${cleanupIntervalMs / 60000} minutes`,
        processingInterval: `${processingIntervalMs / 60000} minutes`
      })

      // Start with initial delays to let webhooks handle immediate processing
      setTimeout(() => {
        runCleanup()
        scheduleCleanup()
      }, 60000) // 1 minute delay for cleanup

      setTimeout(() => {
        runProcessing()
        scheduleProcessing()
      }, 120000) // 2 minute delay for processing

      return cleanup
    }
  }, [enabled, runCleanup, runProcessing, scheduleCleanup, scheduleProcessing, cleanup, cleanupIntervalMs, processingIntervalMs])

  // Expose global methods for manual triggering
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return

    try {
      (window as any).triggerFallbackCleanup = runCleanup;
      (window as any).triggerFallbackProcessing = runProcessing;
    } catch (error) {
      console.warn('Failed to set global fallback functions:', error)
    }

    return () => {
      try {
        if (typeof window !== 'undefined') {
          if ((window as any).triggerFallbackCleanup === runCleanup) {
            delete (window as any).triggerFallbackCleanup;
          }
          if ((window as any).triggerFallbackProcessing === runProcessing) {
            delete (window as any).triggerFallbackProcessing;
          }
        }
      } catch (error) {
        console.warn('Failed to cleanup global fallback functions:', error)
      }
    }
  }, [enabled, runCleanup, runProcessing])

  // This component doesn't render anything visible
  return null
}
