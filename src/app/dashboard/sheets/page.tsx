import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { ExternalLink, Sheet, Calendar, FileSpreadsheet, Plus, LinkIcon, AlertCircle, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import MigrateButton from './MigrateButton';
import SheetPickerButton from '@/components/sheets/SheetPickerButton';

interface GoogleSheet {
  id: string;
  user_id: string;
  year: number;
  sheet_id: string;
  sheet_url: string;
  sheet_name: string;
  last_row_number: number;
  total_receipts: number;
  created_at: string;
  updated_at: string;
}

interface User {
  google_sheets_connected: boolean;
  google_access_token: string | null;
  google_refresh_token: string | null;
}

async function getGoogleSheets(): Promise<GoogleSheet[]> {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return [];
  }

  try {
    const { data, error } = await supabase
      .from('google_sheets')
      .select('*')
      .eq('user_id', user.id)
      .order('year', { ascending: false });

    if (error) {
      console.error('Error fetching Google Sheets:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error:', error);
    return [];
  }
}

async function getUserConnectionStatus(): Promise<User | null> {
  const supabase = await createClient();

  try {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return null;
    }

    const { data: userData, error } = await supabase
      .from('users')
      .select('google_sheets_connected, google_access_token, google_refresh_token')
      .eq('id', user.id)
      .single();

    if (error) {
      console.error('Error fetching user connection status:', error);
      return null;
    }

    return userData;
  } catch (error) {
    console.error('Error:', error);
    return null;
  }
}

export default async function GoogleSheetsPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }

  const sheets = await getGoogleSheets();
  const userConnectionStatus = await getUserConnectionStatus();
  const isConnected = userConnectionStatus?.google_sheets_connected && 
                     userConnectionStatus?.google_access_token && 
                     userConnectionStatus?.google_refresh_token;

  const currentYear = new Date().getFullYear();
  const currentYearSheet = sheets.find(sheet => sheet.year === currentYear);

  return (
    <div className="p-4 md:p-8">
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-white">Google Sheets</h1>
        <p className="text-gray-400 mt-2">
          View and manage your receipt data in Google Sheets. Each year gets its own dedicated sheet.
        </p>
      </header>

      <main>
        {/* Google Sheets Connection Status */}
        <Card className="bg-gradient-to-r from-gray-900 to-gray-800 border-gray-700">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <LinkIcon className="w-6 h-6 text-blue-400" />
                <div>
                  <CardTitle className="text-white">Google Sheets Connection</CardTitle>
                  <CardDescription className="text-gray-400">
                    Manage your Google Sheets integration
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {isConnected ? (
                  <Badge className="bg-green-500/20 text-green-400 border-green-500/30">
                    Connected
                  </Badge>
                ) : (
                  <Badge className="bg-red-500/20 text-red-400 border-red-500/30">
                    Not Connected
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isConnected ? (
              <div className="space-y-4">
                <div className="text-sm text-gray-300">
                  ✅ Your Google account is connected and receipt data will be automatically exported to Google Sheets.
                </div>
                <div className="flex items-center gap-3">
                  <SheetPickerButton
                    accessToken={userConnectionStatus?.google_access_token}
                  />
                  <span className="text-xs text-gray-400">
                    Use an existing Google Sheet instead of creating a new one
                  </span>
                </div>
                {sheets.length > 0 && (
                  <div className="flex items-center gap-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                    <RefreshCw className="w-5 h-5 text-blue-400" />
                    <div className="flex-1">
                      <p className="text-sm text-blue-200 font-medium">Upgrade Your Sheets</p>
                      <p className="text-xs text-blue-300/80">
                        Add enhanced features like monthly summaries, category analysis, and vendor insights to your existing sheets.
                      </p>
                    </div>
                    <MigrateButton />
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-start gap-3 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                  <AlertCircle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="text-yellow-200 font-medium mb-1">Google Sheets Not Connected</p>
                    <p className="text-yellow-300/80">
                      To automatically export your receipts to Google Sheets, you need to reconnect your Google account with the required permissions.
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Link 
                    href="/api/auth/google-sheets" 
                    className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    <LinkIcon className="w-4 h-4" />
                    Connect Google Sheets
                  </Link>
                  <Link 
                    href="/dashboard" 
                    className="inline-flex items-center gap-2 bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    Continue Without Connection
                  </Link>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Current Year Sheet */}
        {currentYearSheet ? (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4">Current Year ({currentYear})</h2>
            <div className="bg-gradient-to-r from-pink-500/10 to-purple-500/10 border border-pink-500/20 rounded-xl p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <FileSpreadsheet className="w-6 h-6 text-green-400" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">{currentYearSheet.sheet_name}</h3>
                    <p className="text-gray-400 text-sm">
                      {currentYearSheet.total_receipts} receipts • Last updated: {new Date(currentYearSheet.updated_at).toLocaleDateString()}
                    </p>
                    <p className="text-gray-500 text-xs mt-1">
                      Next row: {currentYearSheet.last_row_number + 1}
                    </p>
                  </div>
                </div>
                <a
                  href={currentYearSheet.sheet_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center space-x-2"
                >
                  <span>Open Sheet</span>
                  <ExternalLink className="w-4 h-4" />
                </a>
              </div>
            </div>
          </div>
        ) : (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-white mb-4">Current Year ({currentYear})</h2>
            <div className="bg-gray-800/50 border-2 border-dashed border-gray-600 rounded-xl p-8 text-center">
              <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <Sheet className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">No Sheet for {currentYear}</h3>
              <p className="text-gray-400 mb-4">
                Upload your first receipt this year to automatically create a Google Sheet.
              </p>
              <Link href="/dashboard/upload">
                <button className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200 flex items-center space-x-2 mx-auto">
                  <Plus className="w-4 h-4" />
                  <span>Upload Receipt</span>
                </button>
              </Link>
            </div>
          </div>
        )}

        {/* All Sheets */}
        <div>
          <h2 className="text-xl font-semibold text-white mb-4">All Sheets</h2>
          
          {sheets.length > 0 ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {sheets.map((sheet) => (
                <div
                  key={sheet.id}
                  className="bg-gray-800/50 border border-gray-700 rounded-xl p-6 hover:border-gray-600 transition-colors duration-200"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <Calendar className="w-5 h-5 text-blue-400" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-white">{sheet.year}</h3>
                        <p className="text-gray-400 text-sm">{sheet.total_receipts} receipts</p>
                      </div>
                    </div>
                    {sheet.year === currentYear && (
                      <span className="bg-green-500/20 text-green-400 text-xs px-2 py-1 rounded">
                        Current
                      </span>
                    )}
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Sheet Name:</span>
                      <span className="text-white truncate ml-2">{sheet.sheet_name}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Last Row:</span>
                      <span className="text-white">{sheet.last_row_number}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-400">Created:</span>
                      <span className="text-white">{new Date(sheet.created_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <a
                    href={sheet.sheet_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
                  >
                    <span>View Sheet</span>
                    <ExternalLink className="w-4 h-4" />
                  </a>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-8 text-center">
              <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileSpreadsheet className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">No Google Sheets Yet</h3>
              <p className="text-gray-400 mb-4">
                Google Sheets will be automatically created when you upload and process receipts.
              </p>
              <Link href="/dashboard/upload">
                <button className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200 flex items-center space-x-2 mx-auto">
                  <Plus className="w-4 h-4" />
                  <span>Upload First Receipt</span>
                </button>
              </Link>
            </div>
          )}
        </div>

        {/* Info Section */}
        <div className="mt-8 bg-blue-500/10 border border-blue-500/20 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-3">How It Works</h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-medium text-blue-400 mb-2">Automatic Creation</h4>
              <p className="text-gray-400">
                Google Sheets are automatically created for each calendar year when you upload your first receipt.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-blue-400 mb-2">Real-time Updates</h4>
              <p className="text-gray-400">
                Processed receipts are immediately added to your Google Sheet with all extracted data.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-blue-400 mb-2">Organized Data</h4>
              <p className="text-gray-400">
                Each receipt item gets its own row with vendor, date, amount, category, and more details.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-blue-400 mb-2">Easy Export</h4>
              <p className="text-gray-400">
                Export your data as CSV, Excel, or PDF directly from Google Sheets for accounting or tax purposes.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
} 