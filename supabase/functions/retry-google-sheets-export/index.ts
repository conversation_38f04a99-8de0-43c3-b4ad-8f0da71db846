import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { exportReceiptToGoogleSheets } from "../_shared/google-sheets.ts";

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Parse request body
    const { receiptId, userId, extractedData, fileName } = await req.json();

    // Validate required fields
    if (!receiptId || !userId || !extractedData || !fileName) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing required fields: receiptId, userId, extractedData, fileName' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log(`Retrying Google Sheets export for receipt ${receiptId}`);

    // Create Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Verify the receipt exists and belongs to the user
    const { data: receipt, error: receiptError } = await supabase
      .from('receipts')
      .select('id, user_id, processing_status')
      .eq('id', receiptId)
      .eq('user_id', userId)
      .single();

    if (receiptError || !receipt) {
      console.error('Receipt not found:', receiptError);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Receipt not found or access denied' 
        }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (receipt.processing_status !== 'completed') {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Receipt must be completed before retrying Google Sheets export' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Attempt Google Sheets export
    try {
      const exportResult = await exportReceiptToGoogleSheets(
        userId,
        receiptId,
        extractedData,
        fileName
      );

      if (exportResult.success && exportResult.rowNumber) {
        // Update receipt with Google Sheets row number and clear error message
        const { error: updateError } = await supabase
          .from('receipts')
          .update({ 
            google_sheet_row_number: exportResult.rowNumber,
            error_message: null
          })
          .eq('id', receiptId);

        if (updateError) {
          console.error('Failed to update receipt with row number:', updateError);
        }

        console.log(`Successfully retried Google Sheets export for receipt ${receiptId}, row ${exportResult.rowNumber}`);

        return new Response(
          JSON.stringify({ 
            success: true, 
            rowNumber: exportResult.rowNumber,
            message: 'Google Sheets export completed successfully'
          }),
          { 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      } else {
        const errorMsg = exportResult.error || 'Unknown error during export';
        console.error('Google Sheets export failed:', errorMsg);

        // Update receipt with error message
        await supabase
          .from('receipts')
          .update({ 
            error_message: `Google Sheets export failed: ${errorMsg}` 
          })
          .eq('id', receiptId);

        return new Response(
          JSON.stringify({ 
            success: false, 
            error: errorMsg 
          }),
          { 
            status: 500, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }
    } catch (exportError) {
      const errorMessage = exportError instanceof Error ? exportError.message : 'Unknown export error';
      console.error('Google Sheets export error:', errorMessage);

      // Update receipt with error message
      await supabase
        .from('receipts')
        .update({ 
          error_message: `Google Sheets export error: ${errorMessage}` 
        })
        .eq('id', receiptId);

      return new Response(
        JSON.stringify({ 
          success: false, 
          error: errorMessage 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    console.error('Retry Google Sheets export error:', errorMessage);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: errorMessage 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
