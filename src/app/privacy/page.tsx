import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Privacy Policy | ReceiptLabs',
  description: 'Privacy Policy for ReceiptLabs - Learn how we collect, use, and protect your data.',
  robots: 'index, follow',
};

export default function PrivacyPolicyPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <img
                src="/receiptlabs-logo.svg"
                alt="ReceiptLabs"
                className="h-32 sm:h-24 w-auto"
              />
            </Link>
            <Link
              href="/"
              className="text-gray-400 hover:text-white transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 py-12">
        <div className="prose prose-invert max-w-none">
          <h1 className="text-4xl font-bold text-white mb-8">Privacy Policy</h1>
          
          <p className="text-gray-300 mb-8">
            <strong>Effective Date:</strong> January 2, 2025<br />
            <strong>Last Updated:</strong> January 2, 2025
          </p>

          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">1. Introduction</h2>
              <p className="text-gray-300 mb-4">
                Welcome to <strong>Receipt</strong>Labs ("we," "our," or "us"). We are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our receipt management and data extraction service.
              </p>
              <p className="text-gray-300">
                <strong>Receipt</strong>Labs is an AI-powered receipt management platform that helps users extract data from receipts and organize it into Google Sheets for accounting and business purposes.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">2. Information We Collect</h2>
              
              <h3 className="text-xl font-medium text-white mb-3">2.1 Personal Information</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Account Information:</strong> Email address, name, and profile information from Google OAuth</li>
                <li>• <strong>Contact Information:</strong> Email address for communication and support</li>
                <li>• <strong>Payment Information:</strong> Billing details processed through Paystack (we do not store payment card details)</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">2.2 Receipt and Document Data</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Receipt Images:</strong> Photos and scanned images of receipts you upload</li>
                <li>• <strong>Extracted Data:</strong> Vendor names, dates, amounts, tax information, and item details extracted from receipts</li>
                <li>• <strong>File Metadata:</strong> File names, sizes, upload timestamps, and processing status</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">2.3 Google Services Data</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Google Drive:</strong> Access to specific folders you designate for receipt import</li>
                <li>• <strong>Google Sheets:</strong> Creation and modification of spreadsheets for data export</li>
                {/* Gmail feature temporarily disabled */}
                {/* <li>• <strong>Gmail (Business Plan Only):</strong> Access to read emails and attachments for automated receipt processing</li> */}
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">2.4 Technical Information</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• <strong>Usage Data:</strong> Feature usage, processing times, and system performance metrics</li>
                <li>• <strong>Device Information:</strong> Browser type, operating system, and device identifiers</li>
                <li>• <strong>Log Data:</strong> IP addresses, access times, and error logs for troubleshooting</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">3. How We Use Your Information</h2>
              
              <h3 className="text-xl font-medium text-white mb-3">3.1 Core Service Functionality</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Receipt Processing:</strong> Extract data from receipt images using AI technology</li>
                <li>• <strong>Data Organization:</strong> Create and populate Google Sheets with extracted receipt data</li>
                <li>• <strong>Account Management:</strong> Maintain your user account and subscription status</li>
                {/* Gmail feature temporarily disabled */}
                {/* <li>• <strong>Automated Processing:</strong> Process receipts from Gmail attachments (Business Plan)</li> */}
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">3.2 Service Improvement</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Quality Enhancement:</strong> Improve AI extraction accuracy and processing speed</li>
                <li>• <strong>Feature Development:</strong> Develop new features based on usage patterns</li>
                <li>• <strong>Performance Optimization:</strong> Monitor and optimize system performance</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">3.3 Communication</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• <strong>Service Updates:</strong> Notify you about processing status and system updates</li>
                <li>• <strong>Support:</strong> Respond to your inquiries and provide customer support</li>
                <li>• <strong>Account Notifications:</strong> Send important account and billing information</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">4. Google API Services Usage</h2>
              <p className="text-gray-300 mb-4">
              <strong>Receipt</strong>Labs integrates with Google services to provide core functionality. Our use of Google APIs is limited to the specific purposes outlined below:
              </p>

              <h3 className="text-xl font-medium text-white mb-3">4.1 Google Drive API</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
              <li>• <strong>Purpose:</strong> Import receipt images from designated folders</li>
              <li>• <strong>Scope:</strong> Read-only access to files you specifically share with our application</li>
              <li>• <strong>Data Handling:</strong> Files are processed and then removed from our temporary storage</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">4.2 Google Sheets API</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
              <li>• <strong>Purpose:</strong> Create and update spreadsheets with extracted receipt data</li>
              <li>• <strong>Scope:</strong> Create new sheets and modify sheets created by our application</li>
              <li>• <strong>Data Handling:</strong> Only receipt data you've uploaded is written to your sheets</li>
              </ul>

              {/* Gmail feature temporarily disabled */}
              {/*
              <h3 className="text-xl font-medium text-white mb-3">4.3 Gmail API (Business Plan Only)</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
              <li>• <strong>Purpose:</strong> Automatically detect and process receipt attachments</li>
              <li>• <strong>Scope:</strong> Read-only access to email messages and attachments</li>
              <li>• <strong>Data Handling:</strong> Only receipt attachments are processed; email content is not stored</li>
              </ul>
              */}

              <h3 className="text-xl font-medium text-white mb-3">4.4 Compliance with Google API Limited Use Policy</h3>
              <ul className="text-gray-300 space-y-2">
              <li>
                <strong>Receipt</strong>Labs accesses and uses data from Google APIs (Gmail, Google Drive, and Google Sheets) strictly to provide the features described in this Privacy Policy, such as receipt extraction, processing, and spreadsheet organization.
              </li>
              <li>
                We do not use your Google user data to develop, improve, or train generalized artificial intelligence or machine learning models.
              </li>
              <li>
                We comply fully with Google’s Limited Use policy, which restricts the use of Google API data to the intended and user-authorized purpose.
              </li>
              <li>
                User data is not sold or shared for advertising purposes, and access is limited to the minimum necessary for core functionality.
              </li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">5. Data Storage and Security</h2>
              
              <h3 className="text-xl font-medium text-white mb-3">5.1 Data Storage</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Database:</strong> User data and extracted information stored in Supabase (PostgreSQL)</li>
                <li>• <strong>File Storage:</strong> Receipt images temporarily stored during processing</li>
                <li>• <strong>Location:</strong> Data stored in secure cloud infrastructure with encryption</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">5.2 Security Measures</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Encryption:</strong> Data encrypted in transit and at rest</li>
                <li>• <strong>Access Control:</strong> Strict access controls and authentication requirements</li>
                <li>• <strong>Regular Audits:</strong> Regular security assessments and updates</li>
                <li>• <strong>Data Minimization:</strong> We only collect and retain necessary data</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">5.3 Data Retention</h3>
              <ul className="text-gray-300 space-y-2">
                <li>• <strong>Free Plan:</strong> Data retained for 30 days</li>
                <li>• <strong>Professional Plan:</strong> Data retained for 6 months</li>
                <li>• <strong>Business Plan:</strong> Data retained for 12 months</li>
                <li>• <strong>Account Deletion:</strong> All data permanently deleted within 30 days of account closure</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">6. Data Sharing and Disclosure</h2>

              <h3 className="text-xl font-medium text-white mb-3">6.1 We Do Not Sell Your Data</h3>
              <p className="text-gray-300 mb-4">
                We do not sell, rent, or trade your personal information or receipt data to third parties for marketing purposes.
              </p>

              <h3 className="text-xl font-medium text-white mb-3">6.2 Service Providers</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>AI Processing:</strong> OpenAI for receipt data extraction (data is not used for training)</li>
                <li>• <strong>Payment Processing:</strong> Paystack for secure payment processing</li>
                <li>• <strong>Infrastructure:</strong> Supabase and Vercel for hosting and database services</li>
                <li>• <strong>Analytics:</strong> Basic usage analytics for service improvement</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">6.3 Legal Requirements</h3>
              <p className="text-gray-300">
                We may disclose your information if required by law, court order, or to protect our rights and safety.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">7. Your Rights and Choices</h2>

              <h3 className="text-xl font-medium text-white mb-3">7.1 Access and Control</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Account Access:</strong> View and manage your account information</li>
                <li>• <strong>Data Export:</strong> Download your receipt data in standard formats</li>
                <li>• <strong>Data Deletion:</strong> Delete individual receipts or your entire account</li>
                <li>• <strong>Google Permissions:</strong> Revoke Google API access at any time</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">7.2 Communication Preferences</h3>
              <ul className="text-gray-300 mb-4 space-y-2">
                <li>• <strong>Email Notifications:</strong> Control processing and account notifications</li>
                <li>• <strong>Marketing Communications:</strong> Opt out of promotional emails</li>
              </ul>

              <h3 className="text-xl font-medium text-white mb-3">7.3 Data Portability</h3>
              <p className="text-gray-300">
                You can export your receipt data and extracted information in CSV or Excel format at any time.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">8. International Data Transfers</h2>
              <p className="text-gray-300 mb-4">
                Your data may be processed and stored in countries other than your own. We ensure appropriate safeguards are in place to protect your data in accordance with this Privacy Policy and applicable laws.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">9. Children's Privacy</h2>
              <p className="text-gray-300 mb-4">
                <strong>Receipt</strong>Labs is not intended for use by children under 13 years of age. We do not knowingly collect personal information from children under 13. If we become aware that we have collected such information, we will take steps to delete it promptly.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">10. Changes to This Privacy Policy</h2>
              <p className="text-gray-300 mb-4">
                We may update this Privacy Policy from time to time. We will notify you of any material changes by posting the new Privacy Policy on this page and updating the "Last Updated" date. Your continued use of the service after such changes constitutes acceptance of the updated policy.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">11. Contact Information</h2>
              <p className="text-gray-300 mb-4">
                If you have any questions about this Privacy Policy or our data practices, please contact us:
              </p>
              <div className="bg-gray-800 rounded-lg p-6">
                <p className="text-gray-300 mb-2">
                  <strong>Email:</strong> <EMAIL>
                </p>
                <p className="text-gray-300 mb-2">
                  <strong>Website:</strong> <a href="https://receiptlabs.com" className="text-pink-400 hover:text-pink-300">https://receiptlabs.com</a>
                </p>
                <p className="text-gray-300">
                  <strong>Response Time:</strong> We aim to respond to privacy inquiries within 48 hours.
                </p>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">12. Compliance</h2>
              <p className="text-gray-300 mb-4">
                This Privacy Policy is designed to comply with applicable privacy laws including GDPR, CCPA, and other relevant data protection regulations. We are committed to maintaining the highest standards of data protection and privacy.
              </p>
            </section>
          </div>

          <div className="mt-12 pt-8 border-t border-gray-700">
            <p className="text-gray-400 text-sm">
              This Privacy Policy is part of our commitment to transparency and data protection.
              For our Terms of Service, please visit <Link href="/terms" className="text-pink-400 hover:text-pink-300">our Terms page</Link>.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
