import { google } from 'googleapis';
import { createClient } from '@/lib/supabase/server';
import { createAuthenticatedSheetsClient } from './auth';
import { 
  GoogleSheetsLogger, 
  executeGoogleSheetsOperation,
  parseGoogleSheetsError 
} from './errors';

export interface GoogleSheetInfo {
  id: string;
  url: string;
  name: string;
  year: number;
}

/**
 * Receipt data column structure for Google Sheets
 */
export const RECEIPT_COLUMNS = [
  'Receipt ID',
  'Vendor',
  'Vendor Tax ID', 
  'Date',
  'Currency',
  'Payment Method',
  'Item Description',
  'Item Category',
  'Item Total',
  'Subtotal',
  'Tax Rate %',
  'Tax Amount',
  'Total Amount',
  'Source File'
];

/**
 * Create a new Google Sheet for a user for a specific year
 */
export async function createYearlySheet(
  userId: string, 
  year: number
): Promise<GoogleSheetInfo> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      
      // Check if sheet already exists for this user and year
      const { data: existingSheet } = await supabase
        .from('google_sheets')
        .select('*')
        .eq('user_id', userId)
        .eq('year', year)
        .single();

      if (existingSheet) {
        return {
          id: existingSheet.sheet_id,
          url: existingSheet.sheet_url,
          name: existingSheet.sheet_name,
          year: existingSheet.year
        };
      }

      // Get user info for sheet naming
      const { data: user } = await supabase
        .from('users')
        .select('full_name, email')
        .eq('id', userId)
        .single();

      const userName = user?.full_name || user?.email?.split('@')[0] || 'User';
      const sheetTitle = `Receipts ${year} - ${userName}`;

      // Create authenticated Sheets client
      const { sheets } = await createAuthenticatedSheetsClient(userId);

      // Create the spreadsheet with multiple sheets
      const response = await sheets.spreadsheets.create({
        requestBody: {
          properties: {
            title: sheetTitle,
          },
          sheets: [
            {
              properties: {
                title: 'Receipts',
                gridProperties: {
                  rowCount: 1000,
                  columnCount: RECEIPT_COLUMNS.length,
                },
                tabColor: {
                  red: 1.0,
                  green: 0.0,
                  blue: 0.43,
                  alpha: 1.0
                }
              },
            },
            {
              properties: {
                title: 'Monthly Summary',
                gridProperties: {
                  rowCount: 50,
                  columnCount: 10,
                },
                tabColor: {
                  red: 0.51,
                  green: 0.22,
                  blue: 0.93,
                  alpha: 1.0
                }
              },
            },
            {
              properties: {
                title: 'Category Analysis',
                gridProperties: {
                  rowCount: 50,
                  columnCount: 8,
                },
                tabColor: {
                  red: 0.98,
                  green: 0.5,
                  blue: 0.0,
                  alpha: 1.0
                }
              },
            },
            {
              properties: {
                title: 'Vendor Analysis',
                gridProperties: {
                  rowCount: 50,
                  columnCount: 8,
                },
                tabColor: {
                  red: 0.0,
                  green: 0.96,
                  blue: 1.0,
                  alpha: 1.0
                }
              },
            },
          ],
        },
      });

      const spreadsheetId = response.data.spreadsheetId!;
      const spreadsheetUrl = response.data.spreadsheetUrl!;

      // Setup all sheets with headers, formulas, and formatting
      await setupEnhancedSheets(sheets, spreadsheetId);

      // Format the header row
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: [
            {
              repeatCell: {
                range: {
                  sheetId: 0,
                  startRowIndex: 0,
                  endRowIndex: 1,
                },
                cell: {
                  userEnteredFormat: {
                    backgroundColor: {
                      red: 0.2,
                      green: 0.2,
                      blue: 0.2,
                    },
                    textFormat: {
                      foregroundColor: {
                        red: 1,
                        green: 1,
                        blue: 1,
                      },
                      bold: true,
                    },
                  },
                },
                fields: 'userEnteredFormat(backgroundColor,textFormat)',
              },
            },
            {
              updateSheetProperties: {
                properties: {
                  sheetId: 0,
                  gridProperties: {
                    frozenRowCount: 1,
                  },
                },
                fields: 'gridProperties.frozenRowCount',
              },
            },
          ],
        },
      });

      // Save sheet info to database
      const { error } = await supabase
        .from('google_sheets')
        .insert({
          user_id: userId,
          year,
          sheet_id: spreadsheetId,
          sheet_url: spreadsheetUrl,
          sheet_name: sheetTitle,
          last_row_number: 1, // Header row
          total_receipts: 0,
        });

      if (error) {
        throw error;
      }

      return {
        id: spreadsheetId,
        url: spreadsheetUrl,
        name: sheetTitle,
        year,
      };
    },
    'createYearlySheet',
    userId,
    { year, operation: 'create_yearly_sheet' }
  );

  if (!result.success) {
    throw result.error;
  }
  return result.data!;
}

/**
 * Setup enhanced sheets with headers, formulas, and formatting
 */
async function setupEnhancedSheets(sheets: any, spreadsheetId: string): Promise<void> {
  // Setup Receipts sheet
  await setupReceiptsSheet(sheets, spreadsheetId);

  // Setup Monthly Summary sheet
  await setupMonthlySummarySheet(sheets, spreadsheetId);

  // Setup Category Analysis sheet
  await setupCategoryAnalysisSheet(sheets, spreadsheetId);

  // Setup Vendor Analysis sheet
  await setupVendorAnalysisSheet(sheets, spreadsheetId);
}

/**
 * Setup the main Receipts sheet with headers and formatting
 */
async function setupReceiptsSheet(sheets: any, spreadsheetId: string): Promise<void> {
  // Add headers
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Receipts!A1:N1',
    valueInputOption: 'RAW',
    requestBody: {
      values: [RECEIPT_COLUMNS],
    },
  });

  // Apply formatting to headers
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        {
          repeatCell: {
            range: {
              sheetId: 0, // Receipts sheet ID
              startRowIndex: 0,
              endRowIndex: 1,
              startColumnIndex: 0,
              endColumnIndex: RECEIPT_COLUMNS.length,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: {
                  red: 1.0,
                  green: 0.0,
                  blue: 0.43,
                  alpha: 0.2
                },
                textFormat: {
                  bold: true,
                  foregroundColor: {
                    red: 0.0,
                    green: 0.0,
                    blue: 0.0,
                    alpha: 1.0
                  }
                },
                horizontalAlignment: 'CENTER',
              },
            },
            fields: 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)',
          },
        },
        // Freeze header row
        {
          updateSheetProperties: {
            properties: {
              sheetId: 0,
              gridProperties: {
                frozenRowCount: 1,
              },
            },
            fields: 'gridProperties.frozenRowCount',
          },
        },
        // Auto-resize columns
        {
          autoResizeDimensions: {
            dimensions: {
              sheetId: 0,
              dimension: 'COLUMNS',
              startIndex: 0,
              endIndex: RECEIPT_COLUMNS.length,
            },
          },
        },
        // Add conditional formatting for high-value transactions
        {
          addConditionalFormatRule: {
            rule: {
              ranges: [
                {
                  sheetId: 0,
                  startRowIndex: 1,
                  endRowIndex: 1000,
                  startColumnIndex: 8, // Item Total column
                  endColumnIndex: 9,
                }
              ],
              booleanRule: {
                condition: {
                  type: 'NUMBER_GREATER',
                  values: [{ userEnteredValue: '10000' }]
                },
                format: {
                  backgroundColor: {
                    red: 1.0,
                    green: 0.8,
                    blue: 0.8,
                    alpha: 0.5
                  },
                  textFormat: {
                    bold: true
                  }
                }
              }
            },
            index: 0
          }
        },
        // Add conditional formatting for different payment methods
        {
          addConditionalFormatRule: {
            rule: {
              ranges: [
                {
                  sheetId: 0,
                  startRowIndex: 1,
                  endRowIndex: 1000,
                  startColumnIndex: 5, // Payment Method column
                  endColumnIndex: 6,
                }
              ],
              booleanRule: {
                condition: {
                  type: 'TEXT_EQ',
                  values: [{ userEnteredValue: 'Cash' }]
                },
                format: {
                  backgroundColor: {
                    red: 0.8,
                    green: 1.0,
                    blue: 0.8,
                    alpha: 0.3
                  }
                }
              }
            },
            index: 1
          }
        },
        // Add conditional formatting for credit card payments
        {
          addConditionalFormatRule: {
            rule: {
              ranges: [
                {
                  sheetId: 0,
                  startRowIndex: 1,
                  endRowIndex: 1000,
                  startColumnIndex: 5, // Payment Method column
                  endColumnIndex: 6,
                }
              ],
              booleanRule: {
                condition: {
                  type: 'TEXT_CONTAINS',
                  values: [{ userEnteredValue: 'Card' }]
                },
                format: {
                  backgroundColor: {
                    red: 0.8,
                    green: 0.8,
                    blue: 1.0,
                    alpha: 0.3
                  }
                }
              }
            },
            index: 2
          }
        },
      ],
    },
  });
}

/**
 * Setup Monthly Summary sheet with formulas and formatting
 */
async function setupMonthlySummarySheet(sheets: any, spreadsheetId: string): Promise<void> {
  const headers = [
    'Month',
    'Total Receipts',
    'Total Amount',
    'Average Receipt',
    'Top Vendor',
    'Top Category',
    'Tax Amount',
    'Subtotal',
    'Payment Methods',
    'Notes'
  ];

  // Add headers
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Monthly Summary!A1:J1',
    valueInputOption: 'RAW',
    requestBody: {
      values: [headers],
    },
  });

  // Add sample formulas for current month (these will be updated when data is added)
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  const monthName = new Date(currentYear, currentMonth - 1).toLocaleDateString('en-US', { month: 'long', year: 'numeric' });

  const formulaRows = [
    [
      monthName,
      '=COUNTIFS(Receipts!D:D,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),Receipts!D:D,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
      '=SUMIFS(Receipts!M:M,Receipts!D:D,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),Receipts!D:D,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
      '=IF(B2>0,C2/B2,0)',
      '=INDEX(Receipts!B:B,MODE(MATCH(Receipts!B:B,Receipts!B:B,0)))',
      '=INDEX(Receipts!H:H,MODE(MATCH(Receipts!H:H,Receipts!H:H,0)))',
      '=SUMIFS(Receipts!L:L,Receipts!D:D,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),Receipts!D:D,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
      '=SUMIFS(Receipts!J:J,Receipts!D:D,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),Receipts!D:D,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
      '=TEXTJOIN(", ",TRUE,UNIQUE(FILTER(Receipts!F:F,Receipts!D:D>=DATE(YEAR(TODAY()),MONTH(TODAY()),1))))',
      'Auto-calculated from receipts data'
    ]
  ];

  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Monthly Summary!A2:J2',
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: formulaRows,
    },
  });

  // Apply formatting
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        {
          repeatCell: {
            range: {
              sheetId: 1, // Monthly Summary sheet ID
              startRowIndex: 0,
              endRowIndex: 1,
              startColumnIndex: 0,
              endColumnIndex: headers.length,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: {
                  red: 0.51,
                  green: 0.22,
                  blue: 0.93,
                  alpha: 0.2
                },
                textFormat: {
                  bold: true,
                  foregroundColor: {
                    red: 0.0,
                    green: 0.0,
                    blue: 0.0,
                    alpha: 1.0
                  }
                },
                horizontalAlignment: 'CENTER',
              },
            },
            fields: 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)',
          },
        },
        // Format currency columns
        {
          repeatCell: {
            range: {
              sheetId: 1,
              startRowIndex: 1,
              endRowIndex: 50,
              startColumnIndex: 2, // Total Amount column
              endColumnIndex: 4, // Average Receipt column
            },
            cell: {
              userEnteredFormat: {
                numberFormat: {
                  type: 'CURRENCY',
                  pattern: '"KES"#,##0.00'
                }
              },
            },
            fields: 'userEnteredFormat.numberFormat',
          },
        },
        // Auto-resize columns
        {
          autoResizeDimensions: {
            dimensions: {
              sheetId: 1,
              dimension: 'COLUMNS',
              startIndex: 0,
              endIndex: headers.length,
            },
          },
        },
      ],
    },
  });
}

/**
 * Setup Category Analysis sheet with formulas and formatting
 */
async function setupCategoryAnalysisSheet(sheets: any, spreadsheetId: string): Promise<void> {
  const headers = [
    'Category',
    'Total Amount',
    'Receipt Count',
    'Average Amount',
    'Percentage of Total',
    'Most Recent',
    'Top Vendor in Category',
    'Notes'
  ];

  // Add headers
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Category Analysis!A1:H1',
    valueInputOption: 'RAW',
    requestBody: {
      values: [headers],
    },
  });

  // Add sample categories with formulas
  const sampleCategories = [
    'Food & Dining',
    'Transportation',
    'Shopping',
    'Entertainment',
    'Healthcare',
    'Utilities',
    'Other'
  ];

  const categoryFormulas = sampleCategories.map((category, index) => {
    const row = index + 2;
    return [
      category,
      `=SUMIF(Receipts!H:H,"${category}",Receipts!I:I)`,
      `=COUNTIF(Receipts!H:H,"${category}")`,
      `=IF(C${row}>0,B${row}/C${row},0)`,
      `=IF(SUM(B:B)>0,B${row}/SUM(B:B)*100,0)`,
      `=MAXIFS(Receipts!D:D,Receipts!H:H,"${category}")`,
      `=INDEX(Receipts!B:B,MATCH(MAXIFS(Receipts!I:I,Receipts!H:H,"${category}"),Receipts!I:I,0))`,
      'Auto-calculated from receipts'
    ];
  });

  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: `Category Analysis!A2:H${sampleCategories.length + 1}`,
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: categoryFormulas,
    },
  });

  // Apply formatting
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        {
          repeatCell: {
            range: {
              sheetId: 2, // Category Analysis sheet ID
              startRowIndex: 0,
              endRowIndex: 1,
              startColumnIndex: 0,
              endColumnIndex: headers.length,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: {
                  red: 0.98,
                  green: 0.5,
                  blue: 0.0,
                  alpha: 0.2
                },
                textFormat: {
                  bold: true,
                  foregroundColor: {
                    red: 0.0,
                    green: 0.0,
                    blue: 0.0,
                    alpha: 1.0
                  }
                },
                horizontalAlignment: 'CENTER',
              },
            },
            fields: 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)',
          },
        },
        // Format currency columns
        {
          repeatCell: {
            range: {
              sheetId: 2,
              startRowIndex: 1,
              endRowIndex: 50,
              startColumnIndex: 1, // Total Amount column
              endColumnIndex: 4, // Average Amount column
            },
            cell: {
              userEnteredFormat: {
                numberFormat: {
                  type: 'CURRENCY',
                  pattern: '"KES"#,##0.00'
                }
              },
            },
            fields: 'userEnteredFormat.numberFormat',
          },
        },
        // Format percentage column
        {
          repeatCell: {
            range: {
              sheetId: 2,
              startRowIndex: 1,
              endRowIndex: 50,
              startColumnIndex: 4, // Percentage column
              endColumnIndex: 5,
            },
            cell: {
              userEnteredFormat: {
                numberFormat: {
                  type: 'PERCENT',
                  pattern: '0.00%'
                }
              },
            },
            fields: 'userEnteredFormat.numberFormat',
          },
        },
      ],
    },
  });
}

/**
 * Setup Vendor Analysis sheet with formulas and formatting
 */
async function setupVendorAnalysisSheet(sheets: any, spreadsheetId: string): Promise<void> {
  const headers = [
    'Vendor',
    'Total Spent',
    'Receipt Count',
    'Average Amount',
    'Last Visit',
    'Most Common Category',
    'Total Tax Paid',
    'Notes'
  ];

  // Add headers
  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Vendor Analysis!A1:H1',
    valueInputOption: 'RAW',
    requestBody: {
      values: [headers],
    },
  });

  // Add instructions for dynamic vendor analysis
  const instructionRows = [
    ['Top vendors will appear here automatically as you add receipts'],
    ['Use UNIQUE and FILTER functions to get dynamic vendor list'],
    ['=UNIQUE(FILTER(Receipts!B:B,Receipts!B:B<>""))'],
    ['Then use SUMIF, COUNTIF, and other functions for analysis']
  ];

  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Vendor Analysis!A3:A6',
    valueInputOption: 'RAW',
    requestBody: {
      values: instructionRows,
    },
  });

  // Apply formatting
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        {
          repeatCell: {
            range: {
              sheetId: 3, // Vendor Analysis sheet ID
              startRowIndex: 0,
              endRowIndex: 1,
              startColumnIndex: 0,
              endColumnIndex: headers.length,
            },
            cell: {
              userEnteredFormat: {
                backgroundColor: {
                  red: 0.0,
                  green: 0.96,
                  blue: 1.0,
                  alpha: 0.2
                },
                textFormat: {
                  bold: true,
                  foregroundColor: {
                    red: 0.0,
                    green: 0.0,
                    blue: 0.0,
                    alpha: 1.0
                  }
                },
                horizontalAlignment: 'CENTER',
              },
            },
            fields: 'userEnteredFormat(backgroundColor,textFormat,horizontalAlignment)',
          },
        },
        // Format currency columns
        {
          repeatCell: {
            range: {
              sheetId: 3,
              startRowIndex: 1,
              endRowIndex: 50,
              startColumnIndex: 1, // Total Spent column
              endColumnIndex: 4, // Average Amount column
            },
            cell: {
              userEnteredFormat: {
                numberFormat: {
                  type: 'CURRENCY',
                  pattern: '"KES"#,##0.00'
                }
              },
            },
            fields: 'userEnteredFormat.numberFormat',
          },
        },
        // Format tax column
        {
          repeatCell: {
            range: {
              sheetId: 3,
              startRowIndex: 1,
              endRowIndex: 50,
              startColumnIndex: 6, // Total Tax Paid column
              endColumnIndex: 7,
            },
            cell: {
              userEnteredFormat: {
                numberFormat: {
                  type: 'CURRENCY',
                  pattern: '"KES"#,##0.00'
                }
              },
            },
            fields: 'userEnteredFormat.numberFormat',
          },
        },
        // Auto-resize columns
        {
          autoResizeDimensions: {
            dimensions: {
              sheetId: 3,
              dimension: 'COLUMNS',
              startIndex: 0,
              endIndex: headers.length,
            },
          },
        },
      ],
    },
  });
}

/**
 * Add enhanced conditional formatting to existing sheets
 */
export async function addConditionalFormattingToSheet(
  userId: string,
  spreadsheetId: string
): Promise<void> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const { sheets } = await createAuthenticatedSheetsClient(userId);

      // Add data bars for amount columns
      await sheets.spreadsheets.batchUpdate({
        spreadsheetId,
        requestBody: {
          requests: [
            // Add data bars to Total Amount column
            {
              addConditionalFormatRule: {
                rule: {
                  ranges: [
                    {
                      sheetId: 0,
                      startRowIndex: 1,
                      endRowIndex: 1000,
                      startColumnIndex: 12, // Total Amount column
                      endColumnIndex: 13,
                    }
                  ],
                  gradientRule: {
                    minpoint: {
                      color: {
                        red: 1.0,
                        green: 1.0,
                        blue: 1.0,
                        alpha: 1.0
                      },
                      type: 'MIN'
                    },
                    maxpoint: {
                      color: {
                        red: 1.0,
                        green: 0.0,
                        blue: 0.43,
                        alpha: 0.8
                      },
                      type: 'MAX'
                    }
                  }
                },
                index: 10
              }
            },
            // Highlight weekends in date column
            {
              addConditionalFormatRule: {
                rule: {
                  ranges: [
                    {
                      sheetId: 0,
                      startRowIndex: 1,
                      endRowIndex: 1000,
                      startColumnIndex: 3, // Date column
                      endColumnIndex: 4,
                    }
                  ],
                  booleanRule: {
                    condition: {
                      type: 'CUSTOM_FORMULA',
                      values: [{ userEnteredValue: '=OR(WEEKDAY(D2)=1,WEEKDAY(D2)=7)' }]
                    },
                    format: {
                      backgroundColor: {
                        red: 1.0,
                        green: 1.0,
                        blue: 0.8,
                        alpha: 0.5
                      }
                    }
                  }
                },
                index: 11
              }
            },
            // Highlight high tax rates
            {
              addConditionalFormatRule: {
                rule: {
                  ranges: [
                    {
                      sheetId: 0,
                      startRowIndex: 1,
                      endRowIndex: 1000,
                      startColumnIndex: 10, // Tax Rate % column
                      endColumnIndex: 11,
                    }
                  ],
                  booleanRule: {
                    condition: {
                      type: 'NUMBER_GREATER',
                      values: [{ userEnteredValue: '15' }]
                    },
                    format: {
                      backgroundColor: {
                        red: 1.0,
                        green: 0.9,
                        blue: 0.6,
                        alpha: 0.7
                      },
                      textFormat: {
                        bold: true
                      }
                    }
                  }
                },
                index: 12
              }
            }
          ],
        },
      });
    },
    'addConditionalFormattingToSheet',
    userId,
    { spreadsheetId }
  );

  if (!result.success) {
    throw result.error;
  }
}

/**
 * Update summary sheets with latest data
 */
export async function updateSummarySheets(
  userId: string,
  spreadsheetId: string
): Promise<void> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const { sheets } = await createAuthenticatedSheetsClient(userId);

      // Update Monthly Summary sheet with current month data
      const currentYear = new Date().getFullYear();
      const currentMonth = new Date().getMonth() + 1;
      const monthName = new Date(currentYear, currentMonth - 1).toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });

      const monthlyFormulas = [
        [
          monthName,
          '=COUNTIFS(Receipts!D:D,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),Receipts!D:D,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
          '=SUMIFS(Receipts!M:M,Receipts!D:D,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),Receipts!D:D,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
          '=IF(B2>0,C2/B2,0)',
          '=INDEX(Receipts!B:B,MODE(MATCH(Receipts!B:B,Receipts!B:B,0)))',
          '=INDEX(Receipts!H:H,MODE(MATCH(Receipts!H:H,Receipts!H:H,0)))',
          '=SUMIFS(Receipts!L:L,Receipts!D:D,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),Receipts!D:D,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
          '=SUMIFS(Receipts!J:J,Receipts!D:D,">="&DATE(YEAR(TODAY()),MONTH(TODAY()),1),Receipts!D:D,"<"&DATE(YEAR(TODAY()),MONTH(TODAY())+1,1))',
          '=TEXTJOIN(", ",TRUE,UNIQUE(FILTER(Receipts!F:F,Receipts!D:D>=DATE(YEAR(TODAY()),MONTH(TODAY()),1))))',
          `Updated: ${new Date().toLocaleDateString()}`
        ]
      ];

      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: 'Monthly Summary!A2:J2',
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: monthlyFormulas,
        },
      });

      // Update Category Analysis with dynamic vendor formulas
      const categoryUpdateFormulas = [
        ['Food & Dining', '=SUMIF(Receipts!H:H,"Food & Dining",Receipts!I:I)', '=COUNTIF(Receipts!H:H,"Food & Dining")', '=IF(C2>0,B2/C2,0)', '=IF(SUM(B:B)>0,B2/SUM(B:B)*100,0)', '=MAXIFS(Receipts!D:D,Receipts!H:H,"Food & Dining")', '=INDEX(Receipts!B:B,MATCH(MAXIFS(Receipts!I:I,Receipts!H:H,"Food & Dining"),Receipts!I:I,0))', 'Auto-updated'],
        ['Transportation', '=SUMIF(Receipts!H:H,"Transportation",Receipts!I:I)', '=COUNTIF(Receipts!H:H,"Transportation")', '=IF(C3>0,B3/C3,0)', '=IF(SUM(B:B)>0,B3/SUM(B:B)*100,0)', '=MAXIFS(Receipts!D:D,Receipts!H:H,"Transportation")', '=INDEX(Receipts!B:B,MATCH(MAXIFS(Receipts!I:I,Receipts!H:H,"Transportation"),Receipts!I:I,0))', 'Auto-updated'],
        ['Shopping', '=SUMIF(Receipts!H:H,"Shopping",Receipts!I:I)', '=COUNTIF(Receipts!H:H,"Shopping")', '=IF(C4>0,B4/C4,0)', '=IF(SUM(B:B)>0,B4/SUM(B:B)*100,0)', '=MAXIFS(Receipts!D:D,Receipts!H:H,"Shopping")', '=INDEX(Receipts!B:B,MATCH(MAXIFS(Receipts!I:I,Receipts!H:H,"Shopping"),Receipts!I:I,0))', 'Auto-updated'],
        ['Entertainment', '=SUMIF(Receipts!H:H,"Entertainment",Receipts!I:I)', '=COUNTIF(Receipts!H:H,"Entertainment")', '=IF(C5>0,B5/C5,0)', '=IF(SUM(B:B)>0,B5/SUM(B:B)*100,0)', '=MAXIFS(Receipts!D:D,Receipts!H:H,"Entertainment")', '=INDEX(Receipts!B:B,MATCH(MAXIFS(Receipts!I:I,Receipts!H:H,"Entertainment"),Receipts!I:I,0))', 'Auto-updated'],
        ['Healthcare', '=SUMIF(Receipts!H:H,"Healthcare",Receipts!I:I)', '=COUNTIF(Receipts!H:H,"Healthcare")', '=IF(C6>0,B6/C6,0)', '=IF(SUM(B:B)>0,B6/SUM(B:B)*100,0)', '=MAXIFS(Receipts!D:D,Receipts!H:H,"Healthcare")', '=INDEX(Receipts!B:B,MATCH(MAXIFS(Receipts!I:I,Receipts!H:H,"Healthcare"),Receipts!I:I,0))', 'Auto-updated'],
        ['Utilities', '=SUMIF(Receipts!H:H,"Utilities",Receipts!I:I)', '=COUNTIF(Receipts!H:H,"Utilities")', '=IF(C7>0,B7/C7,0)', '=IF(SUM(B:B)>0,B7/SUM(B:B)*100,0)', '=MAXIFS(Receipts!D:D,Receipts!H:H,"Utilities")', '=INDEX(Receipts!B:B,MATCH(MAXIFS(Receipts!I:I,Receipts!H:H,"Utilities"),Receipts!I:I,0))', 'Auto-updated'],
        ['Other', '=SUMIF(Receipts!H:H,"Other",Receipts!I:I)', '=COUNTIF(Receipts!H:H,"Other")', '=IF(C8>0,B8/C8,0)', '=IF(SUM(B:B)>0,B8/SUM(B:B)*100,0)', '=MAXIFS(Receipts!D:D,Receipts!H:H,"Other")', '=INDEX(Receipts!B:B,MATCH(MAXIFS(Receipts!I:I,Receipts!H:H,"Other"),Receipts!I:I,0))', 'Auto-updated']
      ];

      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: 'Category Analysis!A2:H8',
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: categoryUpdateFormulas,
        },
      });

      // Add a summary row to Vendor Analysis sheet
      const vendorSummaryFormula = [
        ['SUMMARY', '=SUM(B:B)', '=SUM(C:C)', '=IF(C9>0,B9/C9,0)', '=MAX(E:E)', '=MODE(F:F)', '=SUM(G:G)', `Updated: ${new Date().toLocaleDateString()}`]
      ];

      await sheets.spreadsheets.values.update({
        spreadsheetId,
        range: 'Vendor Analysis!A9:H9',
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: vendorSummaryFormula,
        },
      });
    },
    'updateSummarySheets',
    userId,
    { spreadsheetId }
  );

  if (!result.success) {
    throw result.error;
  }
}

/**
 * Get or create a Google Sheet for a user for the current year
 */
export async function getOrCreateCurrentYearSheet(userId: string): Promise<GoogleSheetInfo> {
  const currentYear = new Date().getFullYear();
  return await createYearlySheet(userId, currentYear);
}

/**
 * Get sheet information for a specific user and year
 */
export async function getSheetInfo(
  userId: string, 
  year: number
): Promise<GoogleSheetInfo | null> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      const { data: sheet, error } = await supabase
        .from('google_sheets')
        .select('*')
        .eq('user_id', userId)
        .eq('year', year)
        .single();

      if (error) {
        // Not finding a sheet is not a critical error for this function
        if (error.code === 'PGRST116') return null;
        throw error;
      }

      if (!sheet) return null;

      return {
        id: sheet.sheet_id,
        url: sheet.sheet_url,
        name: sheet.sheet_name,
        year: sheet.year,
      };
    },
    'getSheetInfo',
    userId,
    { year }
  );
  
  if (!result.success) {
    // Treat as sheet not found on failure
    return null;
  }
  return result.data ?? null;
}

/**
 * List all sheets for a user
 */
export async function getUserSheets(userId: string): Promise<GoogleSheetInfo[]> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      const { data: sheets, error } = await supabase
        .from('google_sheets')
        .select('*')
        .eq('user_id', userId)
        .order('year', { ascending: false });

      if (error) throw error;
      if (!sheets) return [];

      return sheets.map((sheet: any) => ({
        id: sheet.sheet_id,
        url: sheet.sheet_url,
        name: sheet.sheet_name,
        year: sheet.year,
      }));
    },
    'getUserSheets',
    userId
  );
  
  if (!result.success) {
    // Return empty array on failure
    return [];
  }
  return result.data ?? [];
}

/**
 * Update the last written row number for a sheet
 */
export async function updateSheetLastRow(
  userId: string, 
  year: number, 
  lastRowNumber: number
): Promise<void> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      const { error } = await supabase
        .from('google_sheets')
        .update({ last_row_number: lastRowNumber })
        .eq('user_id', userId)
        .eq('year', year);

      if (error) throw error;
    },
    'updateSheetLastRow',
    userId,
    { year, lastRowNumber }
  );

  if (!result.success) {
    throw result.error;
  }
}

/**
 * Increment the total receipt count for a sheet
 */
export async function incrementSheetReceiptCount(
  userId: string, 
  year: number
): Promise<void> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      const { error } = await supabase.rpc('increment_sheet_receipt_count', {
        p_user_id: userId,
        p_year: year
      });
      
      if (error) throw error;
    },
    'incrementSheetReceiptCount',
    userId,
    { year }
  );

  if (!result.success) {
    throw result.error;
  }
} 