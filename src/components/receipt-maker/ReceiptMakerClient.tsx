'use client';

import { useState } from 'react';
import ReceiptMakerHeader from './ReceiptMakerHeader';
import ReceiptForm from './ReceiptForm';
import ReceiptActions from './ReceiptActions';
import SuccessModal from './SuccessModal';
import { ReceiptData } from './types';

const initialReceiptData: ReceiptData = {
  business: {
    name: '',
    address: '',
    city: '',
    state: '',
    zip: '',
    phone: '',
    email: '',
    logo: null,
  },
  customer: {
    name: '',
    address: '',
    city: '',
    state: '',
    zip: '',
  },
  receipt: {
    number: `REC-${Date.now().toString().slice(-6)}`,
    date: new Date().toISOString().split('T')[0],
    paymentMethod: 'Cash',
  },
  items: [
    { description: '', quantity: 1, rate: 0, amount: 0 }
  ],
  tax: {
    rate: 0,
    amount: 0,
  },
  discount: {
    amount: 0,
    type: 'amount',
  },
  notes: 'Thank you for your business!',
  totals: {
    subtotal: 0,
    total: 0,
  },
  currency: 'USD',
};

export default function ReceiptMakerClient() {
  const [receiptData, setReceiptData] = useState<ReceiptData>(initialReceiptData);
  const [isGenerating, setIsGenerating] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const updateReceiptData = (updates: Partial<ReceiptData>) => {
    setReceiptData(prev => ({ ...prev, ...updates }));
  };

  const calculateTotals = (items: any[], taxRate: number, discount: any) => {
    const subtotal = items.reduce((sum, item) => sum + (item.amount || 0), 0);
    const taxAmount = (subtotal * taxRate) / 100;

    let discountAmount = 0;
    if (discount.type === 'percentage') {
      discountAmount = (subtotal * discount.amount) / 100;
    } else {
      discountAmount = discount.amount;
    }

    const total = subtotal + taxAmount - discountAmount;

    return {
      subtotal,
      taxAmount,
      discountAmount,
      total: Math.max(0, total),
    };
  };

  return (
    <div className="min-h-screen bg-gray-900">
      <ReceiptMakerHeader />
      
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="bg-gray-800 rounded-xl border border-gray-700 p-6 md:p-8">
          <div className="mb-8">
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
              Create Professional Receipt
            </h1>
            <p className="text-gray-400">
              Fill out the form below to generate a professional receipt in PDF or PNG format
            </p>
          </div>

          <ReceiptForm 
            receiptData={receiptData}
            updateReceiptData={updateReceiptData}
            calculateTotals={calculateTotals}
          />

          <ReceiptActions
            receiptData={receiptData}
            isGenerating={isGenerating}
            setIsGenerating={setIsGenerating}
            onDownloadSuccess={() => setShowSuccess(true)}
          />
        </div>

        {/* Lead Generation Section */}
        <div className="mt-12 bg-gradient-to-r from-pink-600/20 to-purple-600/20 border border-pink-600/30 rounded-xl p-6 text-center">
          <h2 className="text-xl font-bold text-white mb-2">
            Tired of Dealing with Receipts Manually?
          </h2>
          <p className="text-gray-300 mb-4">
            Automate your receipt processing with AI-powered extraction from Gmail and Google Drive
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <a
              href="/dashboard"
              className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200"
            >
              Try <strong>Receipt</strong>Labs Free
            </a>
            <a
              href="/"
              className="border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-medium px-6 py-3 rounded-lg transition-all duration-200"
            >
              Learn More
            </a>
          </div>
        </div>

        {/* Features Section */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-gray-800/50 border border-gray-700 rounded-xl">
            <div className="w-12 h-12 bg-pink-600/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-pink-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Professional Quality</h3>
            <p className="text-gray-400 text-sm">Generate high-quality receipts that look professional and are ready for printing or digital sharing.</p>
          </div>

          <div className="text-center p-6 bg-gray-800/50 border border-gray-700 rounded-xl">
            <div className="w-12 h-12 bg-purple-600/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Secure & Private</h3>
            <p className="text-gray-400 text-sm">All processing happens in your browser. Your data never leaves your device, ensuring complete privacy.</p>
          </div>

          <div className="text-center p-6 bg-gray-800/50 border border-gray-700 rounded-xl">
            <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">Multiple Formats</h3>
            <p className="text-gray-400 text-sm">Download your receipts as PDF for professional use or PNG for easy sharing and embedding.</p>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 border-t border-gray-700 mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
            </div>
            <p className="text-gray-400 text-sm mb-4">
              Professional receipt generation and AI-powered receipt processing
            </p>
            <div className="flex justify-center space-x-6 text-sm">
              <a href="/" className="text-gray-400 hover:text-white transition-colors">Home</a>
              <a href="/#features" className="text-gray-400 hover:text-white transition-colors">Features</a>
              <a href="/#pricing" className="text-gray-400 hover:text-white transition-colors">Pricing</a>
              <a href="/dashboard" className="text-gray-400 hover:text-white transition-colors">Get Started</a>
            </div>
            <div className="mt-6 pt-6 border-t border-gray-700">
              <p className="text-gray-500 text-xs">
                © 2025 <strong>Receipt</strong>Labs. All rights reserved. | Free receipt generator tool.
              </p>
            </div>
          </div>
        </div>
      </footer>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccess}
        onClose={() => setShowSuccess(false)}
      />
    </div>
  );
}
