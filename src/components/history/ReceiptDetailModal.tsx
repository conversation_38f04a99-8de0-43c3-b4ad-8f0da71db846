'use client';

import { useState, useEffect } from 'react';
import { X, Download, ExternalLink, FileText, Image, Calendar, DollarSign, Building, CreditCard, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Receipt } from './ReceiptHistoryClient';
import { createClient } from '@/lib/supabase/client';

interface ReceiptDetailModalProps {
  receipt: Receipt;
  onClose: () => void;
}

interface ReceiptItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number | null;
  total_price: number;
  category: string | null;
}

const statusColors: { [key: string]: string } = {
  pending: 'bg-yellow-500/20 text-yellow-400',
  queued: 'bg-orange-500/20 text-orange-400',
  processing: 'bg-blue-500/20 text-blue-400',
  completed: 'bg-green-500/20 text-green-400',
  failed: 'bg-red-500/20 text-red-400',
};

const statusIcons: { [key: string]: React.ReactNode } = {
  pending: <Clock className="w-4 h-4" />,
  queued: <Clock className="w-4 h-4" />,
  processing: <Clock className="w-4 h-4 animate-spin" />,
  completed: <CheckCircle className="w-4 h-4" />,
  failed: <AlertCircle className="w-4 h-4" />,
};

export default function ReceiptDetailModal({ receipt, onClose }: ReceiptDetailModalProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [receiptItems, setReceiptItems] = useState<ReceiptItem[]>([]);
  const [itemsLoading, setItemsLoading] = useState(true);

  const supabase = createClient();

  useEffect(() => {
    // Load receipt image
    const loadImage = async () => {
      try {
        setImageLoading(true);
        const { data, error } = await supabase.storage
          .from('receipts')
          .createSignedUrl(receipt.file_path, 3600); // 1 hour expiry

        if (error) {
          console.error('Error loading image:', error);
          setImageError(true);
        } else {
          setImageUrl(data.signedUrl);
        }
      } catch (error) {
        console.error('Error loading image:', error);
        setImageError(true);
      } finally {
        setImageLoading(false);
      }
    };

    // Load receipt items
    const loadItems = async () => {
      try {
        setItemsLoading(true);
        const { data, error } = await supabase
          .from('receipt_items')
          .select('*')
          .eq('receipt_id', receipt.id)
          .order('id');

        if (error) {
          console.error('Error loading receipt items:', error);
        } else {
          setReceiptItems(data || []);
        }
      } catch (error) {
        console.error('Error loading receipt items:', error);
      } finally {
        setItemsLoading(false);
      }
    };

    loadImage();
    loadItems();
  }, [receipt.id, receipt.file_path, supabase]);

  const formatCurrency = (amount: number | null): string => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-KE', { 
      style: 'currency', 
      currency: receipt.currency || 'KES' 
    }).format(amount);
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return 'Invalid Date';
    }
  };

  const formatDateTime = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Invalid Date';
    }
  };

  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'queued': return 'Queued';
      case 'processing': return 'Processing';
      case 'completed': return 'Completed';
      case 'failed': return 'Failed';
      default: return status;
    }
  };

  const downloadImage = async () => {
    if (!imageUrl) return;
    
    try {
      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = receipt.original_file_name;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading image:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 border border-gray-700 rounded-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-pink-600/20 rounded-lg flex items-center justify-center">
              {receipt.mime_type?.includes('pdf') ? (
                <FileText className="w-5 h-5 text-pink-400" />
              ) : (
                <Image className="w-5 h-5 text-pink-400" />
              )}
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                {receipt.vendor || 'Unknown Vendor'}
              </h2>
              <p className="text-gray-400 text-sm">{receipt.original_file_name}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {imageUrl && (
              <button
                onClick={downloadImage}
                className="p-2 bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white rounded-lg transition-colors"
                title="Download Receipt"
              >
                <Download className="w-5 h-5" />
              </button>
            )}
            <button
              onClick={onClose}
              className="p-2 bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex h-[calc(90vh-80px)]">
          {/* Left Panel - Receipt Image */}
          <div className="w-1/2 p-6 border-r border-gray-700">
            <div className="h-full bg-gray-800/50 rounded-lg flex items-center justify-center">
              {imageLoading ? (
                <div className="text-gray-400">Loading image...</div>
              ) : imageError || !imageUrl ? (
                <div className="text-center text-gray-400">
                  <FileText className="w-16 h-16 mx-auto mb-4" />
                  <p>Unable to load image</p>
                  <p className="text-sm">File: {receipt.original_file_name}</p>
                </div>
              ) : (
                <img
                  src={imageUrl}
                  alt={receipt.original_file_name}
                  className="max-w-full max-h-full object-contain rounded-lg"
                  onError={() => setImageError(true)}
                />
              )}
            </div>
          </div>

          {/* Right Panel - Receipt Details */}
          <div className="w-1/2 p-6 overflow-y-auto">
            <div className="space-y-6">
              {/* Status */}
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-white">Processing Status</h3>
                <Badge
                  className={cn(
                    'capitalize flex items-center gap-2',
                    statusColors[receipt.processing_status] || 'bg-gray-500/20 text-gray-400'
                  )}
                >
                  {statusIcons[receipt.processing_status]}
                  {getStatusDisplayName(receipt.processing_status)}
                </Badge>
              </div>

              {/* Error Message */}
              {receipt.error_message && (
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                  <div className="flex items-start gap-2 text-red-400">
                    <AlertCircle className="w-5 h-5 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium">Processing Error</p>
                      <p className="text-sm mt-1">{receipt.error_message}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    <Building className="w-4 h-4 inline mr-1" />
                    Vendor
                  </label>
                  <p className="text-white">{receipt.vendor || 'N/A'}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    <Calendar className="w-4 h-4 inline mr-1" />
                    Receipt Date
                  </label>
                  <p className="text-white">{formatDate(receipt.receipt_date)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    <CreditCard className="w-4 h-4 inline mr-1" />
                    Payment Method
                  </label>
                  <p className="text-white">{receipt.payment_method || 'N/A'}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Currency
                  </label>
                  <p className="text-white">{receipt.currency}</p>
                </div>
              </div>

              {/* Financial Details */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">Financial Details</h4>
                <div className="bg-gray-800/50 rounded-lg p-4 space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Subtotal:</span>
                    <span className="text-white">{formatCurrency(receipt.subtotal)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Tax ({receipt.tax_rate_percent || 0}%):</span>
                    <span className="text-white">{formatCurrency(receipt.tax_amount)}</span>
                  </div>
                  <div className="border-t border-gray-700 pt-3">
                    <div className="flex justify-between text-lg font-semibold">
                      <span className="text-white">Total:</span>
                      <span className="text-pink-400">{formatCurrency(receipt.total_amount)}</span>
                    </div>
                  </div>
                  {receipt.paid_amount && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Paid:</span>
                      <span className="text-white">{formatCurrency(receipt.paid_amount)}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Receipt Items */}
              {receiptItems.length > 0 && (
                <div>
                  <h4 className="text-lg font-semibold text-white mb-3">Items</h4>
                  <div className="space-y-2">
                    {receiptItems.map((item) => (
                      <div key={item.id} className="bg-gray-800/50 rounded-lg p-3">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <p className="text-white font-medium">{item.description}</p>
                            {item.category && (
                              <p className="text-gray-400 text-sm">{item.category}</p>
                            )}
                          </div>
                          <div className="text-right ml-4">
                            <p className="text-white">
                              {item.quantity} × {formatCurrency(item.unit_price)}
                            </p>
                            <p className="text-pink-400 font-medium">
                              {formatCurrency(item.total_price)}
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Metadata */}
              <div>
                <h4 className="text-lg font-semibold text-white mb-3">Metadata</h4>
                <div className="bg-gray-800/50 rounded-lg p-4 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Created:</span>
                    <span className="text-white">{formatDateTime(receipt.created_at)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Updated:</span>
                    <span className="text-white">{formatDateTime(receipt.updated_at)}</span>
                  </div>
                  {receipt.confidence_score && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Confidence:</span>
                      <span className="text-white">{(receipt.confidence_score * 100).toFixed(1)}%</span>
                    </div>
                  )}
                  {receipt.extraction_method && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Extraction Method:</span>
                      <span className="text-white capitalize">{receipt.extraction_method}</span>
                    </div>
                  )}
                  {receipt.google_sheet_row_number && (
                    <div className="flex justify-between">
                      <span className="text-gray-400">Google Sheet Row:</span>
                      <span className="text-green-400">{receipt.google_sheet_row_number}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-400">File Size:</span>
                    <span className="text-white">
                      {receipt.file_size ? `${(receipt.file_size / 1024 / 1024).toFixed(2)} MB` : 'N/A'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
