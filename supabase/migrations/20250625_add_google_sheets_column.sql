-- Add Google Sheets connection status to users table
-- This migration safely adds the google_sheets_connected column

-- Add the column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'google_sheets_connected') THEN
        ALTER TABLE users ADD COLUMN google_sheets_connected BOOLEAN DEFAULT FALSE;
        
        -- Update existing users with Google tokens to mark as connected
        UPDATE users 
        SET google_sheets_connected = TRUE 
        WHERE google_access_token IS NOT NULL AND google_refresh_token IS NOT NULL;
        
        -- Add comment for documentation
        COMMENT ON COLUMN users.google_sheets_connected IS 'Indicates if user has connected their Google Sheets account';
    END IF;
END $$; 