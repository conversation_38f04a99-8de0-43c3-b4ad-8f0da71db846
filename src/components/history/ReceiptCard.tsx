'use client';

import { useState } from 'react';
import { FileText, Image, Calendar, DollarSign, Building, AlertCircle, CheckCircle, Clock, X } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Receipt } from './ReceiptHistoryClient';

interface ReceiptCardProps {
  receipt: Receipt;
  viewMode: 'grid' | 'list';
  selected: boolean;
  onSelect: (selected: boolean) => void;
  onClick: () => void;
}

const statusColors: { [key: string]: string } = {
  pending: 'bg-yellow-500/20 text-yellow-400',
  queued: 'bg-orange-500/20 text-orange-400',
  processing: 'bg-blue-500/20 text-blue-400',
  completed: 'bg-green-500/20 text-green-400',
  failed: 'bg-red-500/20 text-red-400',
};

const statusIcons: { [key: string]: React.ReactNode } = {
  pending: <Clock className="w-4 h-4" />,
  queued: <Clock className="w-4 h-4" />,
  processing: <Clock className="w-4 h-4 animate-spin" />,
  completed: <CheckCircle className="w-4 h-4" />,
  failed: <AlertCircle className="w-4 h-4" />,
};

export default function ReceiptCard({ receipt, viewMode, selected, onSelect, onClick }: ReceiptCardProps) {
  const [imageError, setImageError] = useState(false);

  const formatCurrency = (amount: number | null): string => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-KE', { 
      style: 'currency', 
      currency: receipt.currency || 'KES' 
    }).format(amount);
  };

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Invalid Date';
    }
  };

  const getFileIcon = () => {
    if (receipt.mime_type?.includes('pdf')) {
      return <FileText className="w-8 h-8 text-red-400" />;
    }
    return <Image className="w-8 h-8 text-blue-400" />;
  };

  const getStatusDisplayName = (status: string) => {
    switch (status) {
      case 'pending': return 'Pending';
      case 'queued': return 'Queued';
      case 'processing': return 'Processing';
      case 'completed': return 'Completed';
      case 'failed': return 'Failed';
      default: return status;
    }
  };

  if (viewMode === 'list') {
    return (
      <div
        className={cn(
          "bg-gray-800/50 border border-gray-700 rounded-lg p-4 hover:border-gray-600 transition-all duration-200 cursor-pointer",
          selected && "ring-2 ring-pink-500 border-pink-500"
        )}
        onClick={onClick}
      >
        <div className="flex items-center gap-4">
          {/* Selection Checkbox */}
          <div className="flex-shrink-0">
            <input
              type="checkbox"
              checked={selected}
              onChange={(e) => {
                e.stopPropagation();
                onSelect(e.target.checked);
              }}
              className="w-4 h-4 text-pink-600 bg-gray-700 border-gray-600 rounded focus:ring-pink-500 focus:ring-2"
            />
          </div>

          {/* File Icon */}
          <div className="flex-shrink-0">
            {getFileIcon()}
          </div>

          {/* Receipt Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="min-w-0 flex-1">
                <h3 className="text-white font-medium truncate">
                  {receipt.vendor || 'Unknown Vendor'}
                </h3>
                <p className="text-gray-400 text-sm truncate">
                  {receipt.original_file_name}
                </p>
              </div>
              
              <div className="flex items-center gap-4 ml-4">
                <div className="text-right">
                  <div className="text-white font-semibold">
                    {formatCurrency(receipt.total_amount)}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {formatDate(receipt.receipt_date)}
                  </div>
                </div>
                
                <Badge
                  className={cn(
                    'capitalize flex items-center gap-1',
                    statusColors[receipt.processing_status] || 'bg-gray-500/20 text-gray-400'
                  )}
                >
                  {statusIcons[receipt.processing_status]}
                  {getStatusDisplayName(receipt.processing_status)}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {receipt.error_message && (
          <div className="mt-3 pt-3 border-t border-gray-700">
            <div className="flex items-start gap-2 text-red-400 text-sm">
              <AlertCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
              <span className="truncate">{receipt.error_message}</span>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Grid view
  return (
    <div
      className={cn(
        "bg-gray-800/50 border border-gray-700 rounded-lg p-3 hover:border-gray-600 transition-all duration-200 cursor-pointer group",
        selected && "ring-2 ring-pink-500 border-pink-500"
      )}
      onClick={onClick}
    >
      {/* Header with selection */}
      <div className="flex items-start justify-between mb-2">
        <input
          type="checkbox"
          checked={selected}
          onChange={(e) => {
            e.stopPropagation();
            onSelect(e.target.checked);
          }}
          className="w-3 h-3 text-pink-600 bg-gray-700 border-gray-600 rounded focus:ring-pink-500 focus:ring-2"
        />

        <Badge
          className={cn(
            'capitalize flex items-center gap-1 text-xs px-1 py-0',
            statusColors[receipt.processing_status] || 'bg-gray-500/20 text-gray-400'
          )}
        >
          {statusIcons[receipt.processing_status]}
          {getStatusDisplayName(receipt.processing_status)}
        </Badge>
      </div>

      {/* File Preview */}
      <div className="flex items-center justify-center h-16 mb-2 bg-gray-700/50 rounded-lg">
        {getFileIcon()}
      </div>

      {/* Receipt Details */}
      <div className="space-y-1">
        <h3 className="text-white font-medium truncate text-sm" title={receipt.vendor || 'Unknown Vendor'}>
          {receipt.vendor || 'Unknown Vendor'}
        </h3>

        <div className="flex items-center gap-1 text-gray-400 text-xs">
          <DollarSign className="w-3 h-3" />
          <span className="font-semibold text-white">
            {formatCurrency(receipt.total_amount)}
          </span>
        </div>

        <div className="flex items-center gap-2 text-gray-400 text-sm">
          <Calendar className="w-4 h-4" />
          <span>{formatDate(receipt.receipt_date)}</span>
        </div>

        <div className="text-gray-500 text-xs truncate" title={receipt.original_file_name}>
          {receipt.original_file_name}
        </div>
      </div>

      {/* Error Message */}
      {receipt.error_message && (
        <div className="mt-3 pt-3 border-t border-gray-700">
          <div className="flex items-start gap-2 text-red-400 text-xs">
            <AlertCircle className="w-3 h-3 mt-0.5 flex-shrink-0" />
            <span className="line-clamp-2">{receipt.error_message}</span>
          </div>
        </div>
      )}

      {/* Google Sheets Info */}
      {receipt.google_sheet_row_number && (
        <div className="mt-2 text-xs text-green-400">
          Sheet Row: {receipt.google_sheet_row_number}
        </div>
      )}
    </div>
  );
}
