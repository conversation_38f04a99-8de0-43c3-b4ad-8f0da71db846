'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { CreditCard } from 'lucide-react';

interface PaymentMethodChartProps {
  data: { method: string; total: number; count: number }[];
  currency?: string;
}

const COLORS = ['#FF006E', '#8338EC', '#FB8500', '#00F5FF', '#FFBE0B', '#10B981'];

export default function PaymentMethodChart({ data, currency = 'KES' }: PaymentMethodChartProps) {
  const hasData = data && data.length > 0;

  // Format currency based on the currency type
  const formatCurrency = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
  };

  const formatCurrencyDetailed = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  const totalAmount = hasData ? data.reduce((sum, item) => sum + item.total, 0) : 0;
  const totalTransactions = hasData ? data.reduce((sum, item) => sum + item.count, 0) : 0;

  // Calculate percentages
  const dataWithPercentages = hasData ? data.map(item => ({
    ...item,
    percentage: totalAmount > 0 ? (item.total / totalAmount) * 100 : 0
  })) : [];

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-white flex items-center">
            <CreditCard className="w-5 h-5 mr-2 text-accent-purple" />
            Payment Methods
          </h3>
          <p className="text-sm text-gray-400">Spending by payment type</p>
        </div>
        {hasData && (
          <div className="text-right">
            <p className="text-sm text-gray-400">Total Transactions</p>
            <p className="text-lg font-semibold text-white">{totalTransactions}</p>
          </div>
        )}
      </div>
      
      {hasData ? (
        <>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Pie Chart */}
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={dataWithPercentages}
                    cx="50%"
                    cy="50%"
                    innerRadius={40}
                    outerRadius={80}
                    paddingAngle={5}
                    dataKey="total"
                  >
                    {dataWithPercentages.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: '#1F2937',
                      border: '1px solid #374151',
                      borderRadius: '12px',
                      color: '#F9FAFB',
                      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                    }}
                    formatter={(value: any, name: string, props: any) => [
                      <div key="payment-tooltip" className="space-y-1">
                        <div className="font-medium">{props.payload.method}</div>
                        <div>Amount: {formatCurrencyDetailed(value)}</div>
                        <div>Transactions: {props.payload.count}</div>
                        <div>Percentage: {props.payload.percentage.toFixed(1)}%</div>
                      </div>
                    ]}
                    labelFormatter={() => ''}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            
            {/* Payment Method List */}
            <div className="space-y-3">
              {dataWithPercentages.map((method, index) => (
                <div key={method.method} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <div>
                      <p className="text-white font-medium">{method.method}</p>
                      <p className="text-xs text-gray-400">{method.count} transactions</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-medium">{formatCurrency(method.total)}</p>
                    <p className="text-xs text-gray-400">{method.percentage.toFixed(1)}%</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Summary Stats */}
          <div className="mt-6 grid grid-cols-2 gap-4">
            <div className="p-3 bg-accent-pink/10 border border-accent-pink/20 rounded-lg text-center">
              <p className="text-accent-pink text-sm font-medium">Total Spent</p>
              <p className="text-accent-pink text-lg font-bold">{formatCurrency(totalAmount)}</p>
            </div>
            <div className="p-3 bg-accent-purple/10 border border-accent-purple/20 rounded-lg text-center">
              <p className="text-accent-purple text-sm font-medium">Avg per Transaction</p>
              <p className="text-accent-purple text-lg font-bold">
                {formatCurrency(totalTransactions > 0 ? totalAmount / totalTransactions : 0)}
              </p>
            </div>
          </div>
        </>
      ) : (
        <div className="h-[300px] flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gray-700/30 rounded-full flex items-center justify-center">
              <CreditCard className="w-10 h-10" />
            </div>
            <p className="text-lg font-medium mb-2">No payment data yet</p>
            <p className="text-sm">Upload receipts to see payment method breakdown</p>
          </div>
        </div>
      )}
    </div>
  );
}
