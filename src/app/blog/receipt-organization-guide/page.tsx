import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft, Calendar, Clock, User, CheckCircle, FolderOpen, Shield } from 'lucide-react'

export const metadata: Metadata = {
  title: "The Complete Guide to Receipt Organization for Small Businesses | ReceiptLabs",
  description: "Discover the best practices for organizing and storing business receipts to ensure compliance and easy retrieval. Never lose a receipt again!",
  keywords: "receipt organization, business receipts, receipt storage, small business organization, receipt management, digital receipts",
}

export default function ReceiptOrganizationGuidePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-4 sm:py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/">
              <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link
              href="/blog"
              className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Blog
            </Link>
            <Link
              href="/login"
              className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
            >
              Sign In
            </Link>
          </div>
        </div>
      </header>

      {/* Article */}
      <article className="relative z-10 px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Article Header */}
          <header className="mb-8">
            <div className="flex flex-wrap items-center gap-4 mb-6 text-sm text-gray-400">
              <span className="bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full">
                Organization
              </span>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                January 5, 2025
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                6 min read
              </div>
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                ReceiptLabs Team
              </div>
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              The Complete Guide to Receipt Organization for Small Businesses
            </h1>
            
            <p className="text-lg sm:text-xl text-gray-300 leading-relaxed">
              Discover the best practices for organizing and storing business receipts to ensure compliance and easy retrieval. 
              Transform your chaotic receipt pile into a streamlined system that saves time and stress.
            </p>
          </header>

          {/* Article Content */}
          <div className="prose prose-lg prose-invert max-w-none">
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-6 sm:p-8 mb-8">
              <h2 className="text-2xl font-bold text-white mb-4">Why Receipt Organization Matters</h2>
              <p className="text-gray-300 mb-4">
                Proper receipt organization isn't just about staying tidy—it's essential for tax compliance, expense tracking, 
                and financial planning. The IRS requires businesses to maintain records that support income, deductions, and credits 
                claimed on tax returns.
              </p>
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mt-4">
                <p className="text-blue-300 text-sm">
                  <strong>Did you know?</strong> The IRS generally requires businesses to keep records for at least 3 years, 
                  but some situations may require longer retention periods.
                </p>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">The Digital-First Approach</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Going digital is the most efficient way to organize receipts. Digital receipts are easier to search, 
                backup, and share with accountants or bookkeepers.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <h4 className="text-green-300 font-semibold mb-3 flex items-center gap-2">
                    <CheckCircle className="w-5 h-5" />
                    Benefits of Digital Receipts
                  </h4>
                  <ul className="text-green-200 space-y-1 text-sm">
                    <li>• Never lose a receipt again</li>
                    <li>• Instant search and retrieval</li>
                    <li>• Automatic backup and sync</li>
                    <li>• Easy sharing with accountants</li>
                    <li>• Reduced physical storage needs</li>
                  </ul>
                </div>
                
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                  <h4 className="text-blue-300 font-semibold mb-3 flex items-center gap-2">
                    <FolderOpen className="w-5 h-5" />
                    Digital Organization Tips
                  </h4>
                  <ul className="text-blue-200 space-y-1 text-sm">
                    <li>• Use consistent naming conventions</li>
                    <li>• Create folders by month/year</li>
                    <li>• Tag receipts by category</li>
                    <li>• Include vendor and amount in filename</li>
                    <li>• Regular backup to cloud storage</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Essential Receipt Categories</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-6">
                Organize your receipts into these essential categories for easier tax preparation and expense tracking:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-3">
                    <h4 className="text-purple-300 font-semibold text-sm">Office & Equipment</h4>
                    <p className="text-purple-200 text-xs">Computers, software, office supplies, furniture</p>
                  </div>
                  
                  <div className="bg-pink-500/10 border border-pink-500/20 rounded-lg p-3">
                    <h4 className="text-pink-300 font-semibold text-sm">Travel & Transportation</h4>
                    <p className="text-pink-200 text-xs">Mileage, flights, hotels, parking, meals</p>
                  </div>
                  
                  <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-3">
                    <h4 className="text-orange-300 font-semibold text-sm">Professional Services</h4>
                    <p className="text-orange-200 text-xs">Legal, accounting, consulting, insurance</p>
                  </div>
                  
                  <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                    <h4 className="text-green-300 font-semibold text-sm">Marketing & Advertising</h4>
                    <p className="text-green-200 text-xs">Website, ads, promotional materials, events</p>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
                    <h4 className="text-blue-300 font-semibold text-sm">Utilities & Communications</h4>
                    <p className="text-blue-200 text-xs">Internet, phone, electricity, rent</p>
                  </div>
                  
                  <div className="bg-cyan-500/10 border border-cyan-500/20 rounded-lg p-3">
                    <h4 className="text-cyan-300 font-semibold text-sm">Training & Education</h4>
                    <p className="text-cyan-200 text-xs">Courses, books, conferences, certifications</p>
                  </div>
                  
                  <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-3">
                    <h4 className="text-yellow-300 font-semibold text-sm">Inventory & Supplies</h4>
                    <p className="text-yellow-200 text-xs">Raw materials, products, packaging</p>
                  </div>
                  
                  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                    <h4 className="text-red-300 font-semibold text-sm">Miscellaneous</h4>
                    <p className="text-red-200 text-xs">Bank fees, licenses, subscriptions</p>
                  </div>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Receipt Storage Best Practices</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="bg-pink-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    1
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-2">Immediate Capture</h4>
                    <p className="text-gray-300 text-sm">
                      Take a photo of receipts immediately after purchase. Don't wait until you get back to the office—
                      receipts can fade or get lost.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    2
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-2">Quality Matters</h4>
                    <p className="text-gray-300 text-sm">
                      Ensure receipt photos are clear and readable. Include the entire receipt, especially the date, 
                      vendor name, and total amount.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    3
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-2">Add Context</h4>
                    <p className="text-gray-300 text-sm">
                      Include notes about the business purpose, especially for meals, entertainment, and travel expenses. 
                      This information is crucial for tax deductions.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold flex-shrink-0">
                    4
                  </div>
                  <div>
                    <h4 className="text-white font-semibold mb-2">Regular Processing</h4>
                    <p className="text-gray-300 text-sm">
                      Set aside time weekly to process and categorize receipts. Don't let them pile up—
                      it becomes overwhelming quickly.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Digital vs. Physical Storage</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <h4 className="text-green-300 font-semibold mb-3">✅ Digital Storage (Recommended)</h4>
                  <ul className="text-green-200 space-y-2 text-sm">
                    <li>• Searchable and easily accessible</li>
                    <li>• Automatic backup and sync</li>
                    <li>• No physical storage space needed</li>
                    <li>• Easy to share with accountants</li>
                    <li>• Integration with accounting software</li>
                    <li>• Environmental friendly</li>
                  </ul>
                </div>
                
                <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
                  <h4 className="text-orange-300 font-semibold mb-3">📁 Physical Storage</h4>
                  <ul className="text-orange-200 space-y-2 text-sm">
                    <li>• Requires significant storage space</li>
                    <li>• Risk of loss due to fire/flood</li>
                    <li>• Difficult to search and organize</li>
                    <li>• Receipts can fade over time</li>
                    <li>• Hard to share with others</li>
                    <li>• Time-consuming to maintain</li>
                  </ul>
                </div>
              </div>
              
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mt-4">
                <p className="text-blue-300 text-sm">
                  <strong>IRS Acceptance:</strong> The IRS accepts digital copies of receipts as long as they're clear and readable. 
                  You don't need to keep physical copies if you have good digital records.
                </p>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Security and Backup</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <div className="flex items-start gap-3 mb-4">
                <Shield className="w-6 h-6 text-purple-400 mt-1 flex-shrink-0" />
                <div>
                  <h4 className="text-purple-300 font-semibold mb-2">Protect Your Financial Data</h4>
                  <p className="text-gray-300 text-sm">
                    Business receipts contain sensitive financial information. Implement proper security measures to protect your data.
                  </p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">Use encrypted cloud storage services</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">Enable two-factor authentication</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">Regular backup to multiple locations</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300 text-sm">Limit access to authorized personnel only</span>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">Monthly Organization Routine</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Establish a monthly routine to keep your receipt organization system running smoothly:
              </p>
              
              <div className="space-y-4">
                <div className="bg-pink-500/10 border border-pink-500/20 rounded-lg p-4">
                  <h4 className="text-pink-300 font-semibold mb-2">Week 1: Collection Review</h4>
                  <p className="text-pink-200 text-sm">Review all receipts collected during the month and ensure they're properly captured.</p>
                </div>
                
                <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
                  <h4 className="text-purple-300 font-semibold mb-2">Week 2: Categorization</h4>
                  <p className="text-purple-200 text-sm">Categorize all receipts and add necessary business purpose notes.</p>
                </div>
                
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                  <h4 className="text-blue-300 font-semibold mb-2">Week 3: Data Entry</h4>
                  <p className="text-blue-200 text-sm">Enter receipt data into your accounting system or expense tracking tool.</p>
                </div>
                
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <h4 className="text-green-300 font-semibold mb-2">Week 4: Reconciliation</h4>
                  <p className="text-green-200 text-sm">Reconcile receipts with bank statements and credit card statements.</p>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="bg-gradient-to-r from-pink-500/10 to-purple-600/10 backdrop-blur-md border border-pink-500/20 rounded-2xl p-6 sm:p-8 mt-12">
              <h3 className="text-2xl font-bold text-white mb-4">Automate Your Receipt Organization</h3>
              <p className="text-gray-300 mb-6">
                Stop spending hours organizing receipts manually. ReceiptLabs automatically captures, categorizes, 
                and organizes your business receipts with AI-powered processing and Google Sheets integration.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/expense-management-small-business"
                  className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 text-center"
                >
                  Try ReceiptLabs Free
                </Link>
                <Link
                  href="/login"
                  className="border border-gray-600 hover:border-gray-500 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 text-center"
                >
                  Start Organizing Today
                </Link>
              </div>
            </div>
          </div>
        </div>
      </article>

      {/* Footer */}
      <footer className="relative z-10 px-4 py-12 border-t border-gray-800 mt-20">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-gray-400 text-sm">
            © 2025 <strong>Receipt</strong>Labs. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}