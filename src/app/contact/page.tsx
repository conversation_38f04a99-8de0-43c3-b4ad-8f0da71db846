import { Metadata } from 'next';
import Link from 'next/link';
import { Mail, MessageCircle, Clock, MapPin, Phone, Send } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Contact Us | ReceiptLabs',
  description: 'Get in touch with ReceiptLabs support team. We\'re here to help with your receipt management and AI data extraction needs.',
  keywords: 'contact ReceiptLabs, support, help, customer service, receipt management support',
  openGraph: {
    title: 'Contact ReceiptLabs Support',
    description: 'Get expert help with your receipt management and AI data extraction needs.',
    type: 'website',
  },
};

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <img
                src="/receiptlabs-logo.svg"
                alt="ReceiptLabs"
                className="h-32 sm:h-24 w-auto"
              />
            </Link>
            <Link
              href="/"
              className="text-gray-400 hover:text-white transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Get in Touch
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto mb-8">
            Have questions about <strong>Receipt</strong>Labs? Need help with your account? 
            Our support team is here to assist you with all your receipt management needs.
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            
            {/* Email Support */}
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 text-center hover:border-pink-500/50 transition-colors">
              <div className="w-16 h-16 bg-pink-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Mail className="w-8 h-8 text-pink-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Email Support</h3>
              <p className="text-gray-300 mb-6">
                Get help with technical issues, billing questions, or general inquiries.
              </p>
              <a 
                href="mailto:<EMAIL>"
                className="inline-flex items-center gap-2 bg-pink-600 hover:bg-pink-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
              >
                <Send className="w-4 h-4" />
                <EMAIL>
              </a>
              <div className="mt-4 text-sm text-gray-400">
                Response time: 24-48 hours
              </div>
            </div>

            {/* Live Chat */}
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 text-center hover:border-purple-500/50 transition-colors">
              <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <MessageCircle className="w-8 h-8 text-purple-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Live Chat</h3>
              <p className="text-gray-300 mb-6">
                Chat with our support team in real-time for immediate assistance.
              </p>
              <Link
                href="/dashboard"
                className="inline-flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
              >
                <MessageCircle className="w-4 h-4" />
                Start Chat
              </Link>
              <div className="mt-4 text-sm text-gray-400">
                Available during business hours
              </div>
            </div>

            {/* Business Hours */}
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 text-center hover:border-blue-500/50 transition-colors">
              <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <Clock className="w-8 h-8 text-blue-400" />
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Business Hours</h3>
              <p className="text-gray-300 mb-6">
                Our support team is available during these hours for live assistance.
              </p>
              <div className="space-y-2 text-white">
                <div>Monday - Friday</div>
                <div className="text-2xl font-bold">9 AM - 5 PM</div>
                <div className="text-gray-400">East Africa Time (EAT)</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 px-4 bg-gray-800/30">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Contact Information</h2>
            <p className="text-gray-300">
              Multiple ways to reach our team for different types of inquiries
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* General Contact */}
            <div className="bg-gray-800/50 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">General Inquiries</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-pink-400" />
                  <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center gap-3">
                  <MessageCircle className="w-5 h-5 text-purple-400" />
                  <span className="text-gray-300">Live chat available in dashboard</span>
                </div>
              </div>
            </div>

            {/* Specialized Contact */}
            <div className="bg-gray-800/50 rounded-xl p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Specialized Support</h3>
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-blue-400" />
                  <div>
                    <div className="text-gray-300">Technical Issues</div>
                    <a href="mailto:<EMAIL>" className="text-sm text-blue-400 hover:text-blue-300">
                      <EMAIL>
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-green-400" />
                  <div>
                    <div className="text-gray-300">Billing & Subscriptions</div>
                    <a href="mailto:<EMAIL>" className="text-sm text-green-400 hover:text-green-300">
                      <EMAIL>
                    </a>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Mail className="w-5 h-5 text-yellow-400" />
                  <div>
                    <div className="text-gray-300">Privacy & Legal</div>
                    <a href="mailto:<EMAIL>" className="text-sm text-yellow-400 hover:text-yellow-300">
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-gray-300">
              Quick answers to common questions about <strong>Receipt</strong>Labs
            </p>
          </div>

          <div className="space-y-6">
            <div className="bg-gray-800/50 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">How accurate is the AI data extraction?</h3>
              <p className="text-gray-300">
                Our AI achieves 95%+ accuracy on clear receipt images. We provide confidence scores and allow manual corrections for any extracted data.
              </p>
            </div>

            <div className="bg-gray-800/50 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">What file formats do you support?</h3>
              <p className="text-gray-300">
                We support JPG, PNG, and PDF files up to 5MB in size. Our AI works best with clear, well-lit images of receipts.
              </p>
            </div>

            <div className="bg-gray-800/50 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Can I cancel my subscription anytime?</h3>
              <p className="text-gray-300">
                Yes, you can cancel your subscription at any time. Your data will be retained according to your plan's retention policy.
              </p>
            </div>

            <div className="bg-gray-800/50 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Is my data secure?</h3>
              <p className="text-gray-300">
                Absolutely. All data is encrypted in transit and at rest. We comply with GDPR, CCPA, and other privacy regulations. 
                Read our <Link href="/privacy" className="text-pink-400 hover:text-pink-300">Privacy Policy</Link> for details.
              </p>
            </div>
          </div>

          <div className="text-center mt-8">
            <p className="text-gray-300 mb-4">
              Don't see your question answered?
            </p>
            <a 
              href="mailto:<EMAIL>"
              className="inline-flex items-center gap-2 bg-pink-600 hover:bg-pink-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
            >
              <Mail className="w-4 h-4" />
              Contact Support
            </a>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-gray-800 py-8">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <p className="text-gray-400 mb-4">
            © 2025 <strong>Receipt</strong>Labs. All rights reserved.
          </p>
          <div className="flex justify-center space-x-6">
            <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors text-sm">
              Privacy Policy
            </Link>
            <Link href="/terms" className="text-gray-400 hover:text-white transition-colors text-sm">
              Terms of Service
            </Link>
            <Link href="/" className="text-gray-400 hover:text-white transition-colors text-sm">
              Home
            </Link>
          </div>
        </div>
      </footer>
    </div>
  );
}
