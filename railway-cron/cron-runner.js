#!/usr/bin/env node

const https = require('https');

const VERCEL_APP_URL = process.env.VERCEL_APP_URL;
const CRON_SECRET = process.env.CRON_SECRET;

const jobType = process.argv[2];

if (!jobType) {
  console.error('Job type required');
  process.exit(1);
}

const endpoints = {
  'gmail-processor': '/api/cron/gmail-processor',
  'data-retention-monitor': '/api/cron/data-retention-monitor', 
  'data-retention-cleanup': '/api/cron/data-retention-cleanup'
};

const endpoint = endpoints[jobType];
if (!endpoint) {
  console.error('Invalid job type:', jobType);
  process.exit(1);
}

const url = `${VERCEL_APP_URL}${endpoint}`;

console.log(`[${new Date().toISOString()}] Running ${jobType}...`);

const options = {
  method: 'GET',
  headers: {
    'Authorization': `Bearer ${CRON_SECRET}`,
    'Content-Type': 'application/json'
  }
};

const req = https.request(url, options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log(`[${new Date().toISOString()}] ${jobType} completed:`, data);
  });
});

req.on('error', (error) => {
  console.error(`[${new Date().toISOString()}] ${jobType} failed:`, error);
});

req.end();
