// Subscription management service for Reco Engine
import { createClient } from '@/lib/supabase/server';
import { paystackClient } from '@/lib/paystack/client';
import { SUBSCRIPTION_TIERS, getTierById } from './tiers';
import type { User, SubscriptionTransaction, TierLimits } from '../../../supabase/functions/_shared/database-types';

export interface SubscriptionUpgradeRequest {
  userId: string;
  targetTier: 'professional' | 'business';
  userEmail: string;
  userName?: string;
}

export interface PaymentInitializationResult {
  authorization_url: string;
  reference: string;
  access_code: string;
}

export class SubscriptionManager {
  private async getSupabase() {
    return await createClient();
  }

  // Initialize payment for subscription upgrade
  async initializeSubscriptionPayment(
    request: SubscriptionUpgradeRequest
  ): Promise<PaymentInitializationResult> {
    const tier = getTierById(request.targetTier);
    if (!tier) {
      throw new Error('Invalid subscription tier');
    }

    // Generate unique reference
    const reference = paystackClient.generateReference('sub');
    
    // Create transaction record
    await this.createSubscriptionTransaction({
      user_id: request.userId,
      paystack_reference: reference,
      amount: tier.price_kes,
      currency: 'KES',
      status: 'pending',
      tier: request.targetTier,
      metadata: {
        tier_name: tier.name,
        user_email: request.userEmail,
        user_name: request.userName,
      }
    });

    // Initialize Paystack transaction
    const paymentData = await paystackClient.initializeTransaction({
      email: request.userEmail,
      amount: paystackClient.toKobo(tier.price_kes),
      currency: 'KES',
      reference,
      callback_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/callback`,
      metadata: {
        user_id: request.userId,
        tier: request.targetTier,
        tier_name: tier.name,
        type: 'subscription_upgrade'
      },
      channels: ['card', 'bank', 'ussd', 'qr', 'mobile_money', 'bank_transfer']
    });

    return paymentData;
  }

  // Verify payment and upgrade subscription
  async verifyAndUpgradeSubscription(reference: string): Promise<{
    success: boolean;
    user?: User;
    transaction?: SubscriptionTransaction;
    error?: string;
  }> {
    try {
      // Get transaction from database
      const supabase = await this.getSupabase();
      const { data: dbTransaction, error: dbError } = await supabase
        .from('subscription_transactions')
        .select('*')
        .eq('paystack_reference', reference)
        .single();

      if (dbError || !dbTransaction) {
        return { success: false, error: 'Transaction not found' };
      }

      // Verify with Paystack
      const paystackTransaction = await paystackClient.verifyTransaction(reference);

      if (paystackTransaction.status !== 'success') {
        // Update transaction status
        await this.updateSubscriptionTransaction(reference, {
          status: paystackTransaction.status as any,
          gateway_response: paystackTransaction.gateway_response,
          paystack_transaction_id: paystackTransaction.id
        });

        return { 
          success: false, 
          error: `Payment failed: ${paystackTransaction.gateway_response}` 
        };
      }

      // Payment successful - upgrade user
      const upgradeResult = await this.upgradeUserSubscription(
        dbTransaction.user_id,
        dbTransaction.tier as any,
        {
          paystack_customer_code: paystackTransaction.customer.customer_code,
          paystack_authorization_code: paystackTransaction.authorization?.authorization_code,
          paystack_transaction_id: paystackTransaction.id,
          paid_at: paystackTransaction.paid_at
        }
      );

      if (!upgradeResult.success) {
        return { success: false, error: upgradeResult.error };
      }

      // Update transaction as successful
      await this.updateSubscriptionTransaction(reference, {
        status: 'success',
        gateway_response: paystackTransaction.gateway_response,
        paystack_transaction_id: paystackTransaction.id,
        paid_at: paystackTransaction.paid_at
      });

      return {
        success: true,
        user: upgradeResult.user,
        transaction: dbTransaction
      };

    } catch (error) {
      console.error('Error verifying subscription payment:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Upgrade user subscription
  private async upgradeUserSubscription(
    userId: string,
    targetTier: 'professional' | 'business',
    paymentData: {
      paystack_customer_code: string;
      paystack_authorization_code?: string;
      paystack_transaction_id: number;
      paid_at?: string;
    }
  ): Promise<{ success: boolean; user?: User; error?: string }> {
    try {
      const tier = getTierById(targetTier);
      if (!tier) {
        return { success: false, error: 'Invalid tier' };
      }

      const now = new Date();
      const nextBilling = new Date(now);
      nextBilling.setMonth(nextBilling.getMonth() + 1);

      const supabase = await this.getSupabase();
      const { data: user, error } = await supabase
        .from('users')
        .update({
          current_tier: targetTier,
          subscription_status: 'active',
          monthly_receipt_limit: tier.features.receipts_per_month === -1 ? -1 : tier.features.receipts_per_month,
          data_retention_months: tier.features.data_retention_months,
          paystack_customer_code: paymentData.paystack_customer_code,
          paystack_authorization_code: paymentData.paystack_authorization_code,
          current_period_start: now.toISOString(),
          current_period_end: nextBilling.toISOString(),
          next_billing_date: nextBilling.toISOString(),
          last_payment_date: paymentData.paid_at || now.toISOString(),
          receipts_used_this_period: 0, // Reset usage on upgrade
          updated_at: now.toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error upgrading user subscription:', error);
        return { success: false, error: error.message };
      }

      return { success: true, user };

    } catch (error) {
      console.error('Error in upgradeUserSubscription:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  }

  // Create subscription transaction record
  private async createSubscriptionTransaction(
    transaction: Omit<SubscriptionTransaction, 'id' | 'created_at' | 'updated_at'>
  ): Promise<SubscriptionTransaction> {
    const supabase = await this.getSupabase();
    const { data, error } = await supabase
      .from('subscription_transactions')
      .insert(transaction)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to create subscription transaction: ${error.message}`);
    }

    return data;
  }

  // Update subscription transaction
  private async updateSubscriptionTransaction(
    reference: string,
    updates: Partial<SubscriptionTransaction>
  ): Promise<void> {
    const supabase = await this.getSupabase();
    const { error } = await supabase
      .from('subscription_transactions')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('paystack_reference', reference);

    if (error) {
      throw new Error(`Failed to update subscription transaction: ${error.message}`);
    }
  }

  // Get user's tier limits and usage
  async getUserTierLimits(userId: string): Promise<TierLimits | null> {
    try {
      const supabase = await this.getSupabase();

      // First check if the function exists, if not, use direct query
      const { data, error } = await supabase
        .rpc('check_tier_limits', { user_id_param: userId });

      if (error) {
        console.error('Error calling check_tier_limits function:', error);

        // Fallback to direct query if function doesn't exist
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('current_tier, monthly_receipt_limit, receipts_used_this_period, subscription_status, data_retention_months')
          .eq('id', userId)
          .single();

        if (userError || !userData) {
          console.error('Error getting user data:', userError);
          return null;
        }

        // Calculate limits manually
        const remainingReceipts = userData.monthly_receipt_limit === -1
          ? -1
          : Math.max(0, userData.monthly_receipt_limit - (userData.receipts_used_this_period || 0));

        const canProcess = userData.monthly_receipt_limit === -1
          ? true
          : (userData.receipts_used_this_period || 0) < userData.monthly_receipt_limit;

        return {
          can_process_receipt: canProcess && userData.subscription_status !== 'expired',
          receipts_remaining: remainingReceipts,
          has_analytics_access: userData.current_tier === 'professional' || userData.current_tier === 'business',
          has_google_drive_access: userData.current_tier === 'business',
          has_gmail_auto_processing: userData.current_tier === 'business',
          data_retention_months: userData.data_retention_months || 1
        };
      }

      return data?.[0] || null;
    } catch (error) {
      console.error('Error in getUserTierLimits:', error);
      return null;
    }
  }

  // Increment receipt usage
  async incrementReceiptUsage(userId: string): Promise<boolean> {
    try {
      const supabase = await this.getSupabase();

      // Get current values first
      const { data: currentData } = await supabase
        .from('users')
        .select('receipts_used_this_period, receipts_processed')
        .eq('id', userId)
        .single();

      const { error } = await supabase
        .from('users')
        .update({
          receipts_used_this_period: (currentData?.receipts_used_this_period || 0) + 1,
          receipts_processed: (currentData?.receipts_processed || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      return !error;
    } catch (error) {
      console.error('Error incrementing receipt usage:', error);
      return false;
    }
  }

  // Reset monthly usage (called by cron job)
  async resetMonthlyUsage(): Promise<void> {
    try {
      const supabase = await this.getSupabase();
      const { error } = await supabase
        .rpc('reset_monthly_usage');

      if (error) {
        console.error('Error resetting monthly usage:', error);
      }
    } catch (error) {
      console.error('Error in resetMonthlyUsage:', error);
    }
  }

  // Get subscription transactions for a user
  async getUserSubscriptionTransactions(userId: string): Promise<SubscriptionTransaction[]> {
    const supabase = await this.getSupabase();
    const { data, error } = await supabase
      .from('subscription_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error getting subscription transactions:', error);
      return [];
    }

    return data || [];
  }
}

// Export singleton instance
export const subscriptionManager = new SubscriptionManager();
