// QStash-based cron job setup for Vercel deployment
// More reliable than Vercel crons for critical tasks

interface QStashSchedule {
  destination: string;
  cron: string;
  body?: string;
  headers?: Record<string, string>;
}

export class QStashCronManager {
  private qstashToken: string;
  private baseUrl: string;

  constructor() {
    this.qstashToken = process.env.QSTASH_TOKEN!;
    this.baseUrl = process.env.VERCEL_URL || process.env.NEXT_PUBLIC_APP_URL!;
  }

  async setupDataRetentionCrons() {
    const schedules: QStashSchedule[] = [
      {
        destination: `${this.baseUrl}/api/cron/data-retention-monitor`,
        cron: '0 2 * * *', // Daily at 2 AM
        headers: {
          'Authorization': `Bearer ${process.env.CRON_SECRET}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          source: 'qstash',
          job: 'monitor'
        })
      },
      {
        destination: `${this.baseUrl}/api/cron/data-retention-cleanup`,
        cron: '0 3 * * 0', // Weekly on Sunday at 3 AM
        headers: {
          'Authorization': `Bearer ${process.env.CRON_SECRET}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          source: 'qstash',
          job: 'cleanup'
        })
      }
    ];

    for (const schedule of schedules) {
      await this.createSchedule(schedule);
    }
  }

  private async createSchedule(schedule: QStashSchedule) {
    const response = await fetch('https://qstash.upstash.io/v1/schedules', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.qstashToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(schedule)
    });

    if (!response.ok) {
      throw new Error(`Failed to create QStash schedule: ${response.statusText}`);
    }

    return response.json();
  }
}
