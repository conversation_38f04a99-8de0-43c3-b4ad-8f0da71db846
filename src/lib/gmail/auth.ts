import { google } from 'googleapis';
import { createClient } from '@/lib/supabase/server';

export interface GmailTokens {
  access_token: string;
  refresh_token: string;
  expires_at?: number;
  email?: string;
}

export interface GmailAuthResult {
  success: boolean;
  tokens?: GmailTokens;
  userInfo?: {
    email: string;
    name: string;
    picture?: string;
  };
  error?: string;
}

/**
 * Creates a Google OAuth2 client configured for Gmail API
 */
export function createGmailAuth() {
  const oauth2Client = new google.auth.OAuth2(
    process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    `${process.env.NEXT_PUBLIC_APP_URL}/auth/gmail/callback`
  );

  return oauth2Client;
}

/**
 * Get the authorization URL for Gmail OAuth consent
 */
export function getGmailAuthUrl(userId: string) {
  const oauth2Client = createGmailAuth();
  
  const scopes = [
    'https://www.googleapis.com/auth/gmail.readonly', // Read access to Gmail
    'https://www.googleapis.com/auth/userinfo.email', // Get user email
    'https://www.googleapis.com/auth/userinfo.profile', // Get user profile
  ];

  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    prompt: 'consent', // Force consent to get refresh token
    include_granted_scopes: true,
    state: userId, // Pass user ID to identify user in callback
  });

  return authUrl;
}

/**
 * Exchange authorization code for Gmail tokens
 */
export async function exchangeCodeForGmailTokens(code: string): Promise<GmailAuthResult> {
  try {
    const oauth2Client = createGmailAuth();
    const { tokens } = await oauth2Client.getToken(code);
    
    if (!tokens.access_token) {
      return {
        success: false,
        error: 'No access token received from Google'
      };
    }

    // Set credentials to get user info
    oauth2Client.setCredentials(tokens);
    
    // Get user information
    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
    const userInfoResponse = await oauth2.userinfo.get();
    const userInfo = userInfoResponse.data;

    if (!userInfo.email) {
      return {
        success: false,
        error: 'Could not retrieve user email from Google'
      };
    }

    return {
      success: true,
      tokens: {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token || '',
        expires_at: tokens.expiry_date || undefined,
        email: userInfo.email
      },
      userInfo: {
        email: userInfo.email,
        name: userInfo.name || '',
        picture: userInfo.picture || undefined
      }
    };
  } catch (error) {
    console.error('Error exchanging code for Gmail tokens:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Create authenticated Gmail client for a user
 */
export async function createAuthenticatedGmailClient(userId: string) {
  try {
    const supabase = await createClient();
    
    // Get user's Gmail tokens from database
    const { data: user, error } = await supabase
      .from('users')
      .select('gmail_access_token, gmail_refresh_token, gmail_email')
      .eq('id', userId)
      .single();

    if (error || !user) {
      throw new Error('User not found');
    }

    if (!user.gmail_access_token) {
      throw new Error('User has not authorized Gmail access');
    }

    const oauth2Client = createGmailAuth();
    
    // Set the user's tokens
    oauth2Client.setCredentials({
      access_token: user.gmail_access_token,
      refresh_token: user.gmail_refresh_token,
    });

    // Handle token refresh with better error handling
    oauth2Client.on('tokens', async (tokens) => {
      try {
        console.log('Gmail tokens refreshed for user:', userId);
        if (tokens.refresh_token) {
          // Update both access and refresh tokens
          await updateUserGmailTokens(userId, {
            access_token: tokens.access_token!,
            refresh_token: tokens.refresh_token
          });
          console.log('Updated both access and refresh tokens');
        } else if (tokens.access_token) {
          // Update only access token
          await updateUserGmailTokens(userId, {
            access_token: tokens.access_token,
            refresh_token: user.gmail_refresh_token! // Keep old refresh token
          });
          console.log('Updated access token only');
        }
      } catch (error) {
        console.error('Error refreshing Gmail tokens for user', userId, ':', error);
        // Mark the connection as potentially invalid
        await markGmailTokensAsInvalid(userId);
      }
    });

    // Create and return authenticated Gmail client
    const gmail = google.gmail({ version: 'v1', auth: oauth2Client });
    return { gmail, oauth2Client };
  } catch (error) {
    console.error('Error creating authenticated Gmail client:', error);
    throw error;
  }
}

/**
 * Update user's Gmail tokens in database
 */
async function updateUserGmailTokens(userId: string, tokens: GmailTokens): Promise<void> {
  try {
    const supabase = await createClient();
    const { error } = await supabase
      .from('users')
      .update({
        gmail_access_token: tokens.access_token,
        gmail_refresh_token: tokens.refresh_token,
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error updating Gmail tokens:', error);
    throw error;
  }
}

/**
 * Mark Gmail tokens as invalid (connection lost)
 */
async function markGmailTokensAsInvalid(userId: string): Promise<void> {
  try {
    const supabase = await createClient();
    const { error } = await supabase
      .from('users')
      .update({
        gmail_connected: false,
        gmail_access_token: null,
        gmail_refresh_token: null,
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error marking Gmail tokens as invalid:', error);
    throw error;
  }
}

/**
 * Store Gmail connection info for user
 */
export async function connectUserGmail(
  userId: string,
  tokens: GmailTokens,
  userInfo: { email: string; name: string; picture?: string }
): Promise<void> {
  try {
    const supabase = await createClient();
    const { error } = await supabase
      .from('users')
      .update({
        gmail_connected: true,
        gmail_email: userInfo.email,
        gmail_access_token: tokens.access_token,
        gmail_refresh_token: tokens.refresh_token,
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error connecting Gmail:', error);
    throw error;
  }
}

/**
 * Disconnect Gmail for user
 */
export async function disconnectUserGmail(userId: string): Promise<void> {
  try {
    const supabase = await createClient();
    const { error } = await supabase
      .from('users')
      .update({
        gmail_connected: false,
        gmail_email: null,
        gmail_access_token: null,
        gmail_refresh_token: null,
        gmail_processing_enabled: false,
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error disconnecting Gmail:', error);
    throw error;
  }
}

/**
 * Validate Gmail tokens by making a test API call
 */
export async function validateGmailTokens(userId: string): Promise<{ valid: boolean; error?: string }> {
  try {
    const { gmail } = await createAuthenticatedGmailClient(userId);
    
    // Try to get user profile (lightweight test)
    await gmail.users.getProfile({ userId: 'me' });
    
    return { valid: true };
  } catch (error) {
    console.error('Gmail token validation failed:', error);
    return { 
      valid: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
