import { createClient } from '@/lib/supabase/server';
import { unstable_noStore as noStore } from 'next/cache';

export async function getReceiptsForUser(limit?: number) {
  noStore();
  const supabase = await createClient();

  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    console.error('No user found');
    return [];
  }

  try {
    console.log('Fetching receipts for user:', user.id, limit ? `(limit: ${limit})` : '');
    let query = supabase
      .from('receipts')
      .select(`
        id,
        vendor,
        receipt_date,
        total_amount,
        processing_status,
        google_sheet_row_number,
        error_message
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (limit) {
      query = query.limit(limit);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Supabase error fetching receipts:', error);
      throw new Error(`Failed to fetch receipts: ${error.message}`);
    }

    console.log('Successfully fetched receipts:', data?.length || 0);
    return data || [];
  } catch (error) {
    console.error('Database Error:', error);
    // Return empty array instead of throwing to prevent page crash
    return [];
  }
}

export async function getReceiptsForUserDetailed() {
  noStore();
  const supabase = await createClient();

  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    console.error('No user found');
    return [];
  }

  try {
    const { data, error } = await supabase
      .from('receipts')
      .select(`
        id,
        user_id,
        original_file_name,
        file_path,
        file_size,
        mime_type,
        vendor,
        vendor_tax_id,
        receipt_date,
        currency,
        payment_method,
        subtotal,
        tax_rate_percent,
        tax_amount,
        total_amount,
        paid_amount,
        processing_status,
        confidence_score,
        extraction_method,
        error_message,
        google_sheet_row_number,
        redis_job_id,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching detailed receipts:', error);
      throw new Error('Failed to fetch receipts.');
    }

    return data;
  } catch (error) {
    console.error('Database Error:', error);
    throw new Error('Failed to fetch receipts.');
  }
}