import Link from 'next/link'
import MobileNav from '@/components/homepage/MobileNav'

export default function Header() {
  return (
    <header className="relative z-10 px-4 py-4 sm:py-6">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex items-center">
          <Link href="/">
            <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
          </Link>
        </div>

        <div className="flex items-center space-x-4">
          <nav className="hidden md:flex items-center space-x-6">
            <a href="#features" className="text-gray-300 hover:text-white transition-colors">Features</a>
            <a href="#how-it-works" className="text-gray-300 hover:text-white transition-colors">How it Works</a>
            <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">Pricing</a>
            <Link href="/" className="text-gray-300 hover:text-white transition-colors">Home</Link>
          </nav>
          <Link
            href="/login"
            className="hidden md:block bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
          >
            Sign In
          </Link>
          <MobileNav />
        </div>
      </div>
    </header>
  )
}