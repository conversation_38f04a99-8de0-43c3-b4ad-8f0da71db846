'use client';

import { useState } from 'react';
import { Link, CheckCircle, XCircle, RefreshCw, ExternalLink, AlertCircle, Settings, Unlink } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { UserData } from './SettingsClient';

interface IntegrationsSettingsProps {
  userData: UserData;
  onUserDataUpdate: () => void;
}

export default function IntegrationsSettings({ userData, onUserDataUpdate }: IntegrationsSettingsProps) {
  const [connecting, setConnecting] = useState<string | null>(null);
  const [disconnecting, setDisconnecting] = useState<string | null>(null);

  const handleConnectGoogleSheets = async () => {
    try {
      setConnecting('sheets');
      
      // Redirect to Google OAuth
      window.location.href = '/api/auth/google/sheets';
    } catch (error) {
      console.error('Error connecting Google Sheets:', error);
      setConnecting(null);
    }
  };

  const handleConnectGoogleDrive = async () => {
    try {
      setConnecting('drive');
      
      // Redirect to Google OAuth for Drive
      window.location.href = '/api/auth/google/drive';
    } catch (error) {
      console.error('Error connecting Google Drive:', error);
      setConnecting(null);
    }
  };

  const handleDisconnectGoogle = async (service: 'sheets' | 'drive') => {
    try {
      setDisconnecting(service);

      const response = await fetch('/api/integrations/google/disconnect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ service }),
      });

      const result = await response.json();

      if (result.success) {
        onUserDataUpdate();
      } else {
        throw new Error(result.error || 'Failed to disconnect');
      }
    } catch (error) {
      console.error(`Error disconnecting Google ${service}:`, error);
      alert(`Failed to disconnect Google ${service}. Please try again.`);
    } finally {
      setDisconnecting(null);
    }
  };

  const handleTestConnection = async (service: 'sheets' | 'drive') => {
    try {
      const response = await fetch(`/api/integrations/google/test?service=${service}`);
      const result = await response.json();

      if (result.success) {
        alert(`Google ${service} connection is working correctly!`);
      } else {
        alert(`Google ${service} connection test failed: ${result.error}`);
      }
    } catch (error) {
      console.error(`Error testing ${service} connection:`, error);
      alert(`Failed to test Google ${service} connection.`);
    }
  };

  const isGoogleConnected = userData.google_access_token && userData.google_refresh_token;

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Google Sheets Integration */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-600/20 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg className="w-6 h-6 text-green-400" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19,3H5C3.9,3 3,3.9 3,5V19C3,20.1 3.9,21 5,21H19C20.1,21 21,20.1 21,19V5C21,3.9 20.1,3 19,3M19,19H5V5H19V19Z" />
                <path d="M7,7H9V9H7V7M11,7H17V9H11V7M7,11H9V13H7V11M11,11H17V13H11V11M7,15H9V17H7V15M11,15H17V17H11V15Z" />
              </svg>
            </div>
            <div className="min-w-0">
              <h3 className="text-lg sm:text-xl font-semibold text-white">Google Sheets</h3>
              <p className="text-gray-400 text-sm">Export receipts to Google Sheets automatically</p>
            </div>
          </div>

          <div className="flex items-center gap-2 flex-shrink-0">
            {userData.google_sheets_connected ? (
              <Badge className="bg-green-500/20 text-green-400 flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Connected
              </Badge>
            ) : (
              <Badge className="bg-gray-500/20 text-gray-400 flex items-center gap-1">
                <XCircle className="w-3 h-3" />
                Not Connected
              </Badge>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-700/50 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2">Features</h4>
            <ul className="text-gray-300 text-sm space-y-1">
              <li>• Automatically export receipt data to spreadsheets</li>
              <li>• Organize receipts by year in separate sheets</li>
              <li>• Real-time sync when receipts are processed</li>
              <li>• Customizable column mapping and formatting</li>
            </ul>
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
            {userData.google_sheets_connected ? (
              <>
                <button
                  onClick={() => handleTestConnection('sheets')}
                  className="flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                >
                  <RefreshCw className="w-4 h-4" />
                  Test Connection
                </button>
                <button
                  onClick={() => window.open('/dashboard/sheets', '_blank')}
                  className="flex items-center justify-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                >
                  <ExternalLink className="w-4 h-4" />
                  Manage Sheets
                </button>
                <button
                  onClick={() => handleDisconnectGoogle('sheets')}
                  disabled={disconnecting === 'sheets'}
                  className="flex items-center justify-center gap-2 bg-red-600 hover:bg-red-700 disabled:bg-red-600/50 text-white px-4 py-2 rounded-lg transition-colors text-sm"
                >
                  <Unlink className="w-4 h-4" />
                  {disconnecting === 'sheets' ? 'Disconnecting...' : 'Disconnect'}
                </button>
              </>
            ) : (
              <button
                onClick={handleConnectGoogleSheets}
                disabled={connecting === 'sheets'}
                className="flex items-center justify-center gap-2 bg-pink-600 hover:bg-pink-700 disabled:bg-pink-600/50 text-white px-4 py-2 rounded-lg transition-colors text-sm"
              >
                <Link className="w-4 h-4" />
                {connecting === 'sheets' ? 'Connecting...' : 'Connect Google Sheets'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Google Drive Integration */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                <path d="M7.71,3.5L1.15,15L4.58,21L11.13,9.5M9.73,15L6.3,21H19.42L22.85,15M22.28,14L15.42,2H8.58L8.57,2L15.43,14H22.28Z" />
              </svg>
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white">Google Drive</h3>
              <p className="text-gray-400 text-sm">Import receipts from Google Drive folders</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {isGoogleConnected ? (
              <Badge className="bg-green-500/20 text-green-400 flex items-center gap-1">
                <CheckCircle className="w-3 h-3" />
                Connected
              </Badge>
            ) : (
              <Badge className="bg-gray-500/20 text-gray-400 flex items-center gap-1">
                <XCircle className="w-3 h-3" />
                Not Connected
              </Badge>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-gray-700/50 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2">Features</h4>
            <ul className="text-gray-300 text-sm space-y-1">
              <li>• Import receipts from specified Drive folders</li>
              <li>• Automatic processing of new files</li>
              <li>• Support for PDF and image formats</li>
              <li>• Batch import capabilities</li>
            </ul>
          </div>

          <div className="flex items-center gap-3">
            {isGoogleConnected ? (
              <>
                <button
                  onClick={() => handleTestConnection('drive')}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  Test Connection
                </button>
                <button
                  onClick={() => window.open('/dashboard/upload', '_blank')}
                  className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <ExternalLink className="w-4 h-4" />
                  Import Files
                </button>
                <button
                  onClick={() => handleDisconnectGoogle('drive')}
                  disabled={disconnecting === 'drive'}
                  className="flex items-center gap-2 bg-red-600 hover:bg-red-700 disabled:bg-red-600/50 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <Unlink className="w-4 h-4" />
                  {disconnecting === 'drive' ? 'Disconnecting...' : 'Disconnect'}
                </button>
              </>
            ) : (
              <button
                onClick={handleConnectGoogleDrive}
                disabled={connecting === 'drive'}
                className="flex items-center gap-2 bg-pink-600 hover:bg-pink-700 disabled:bg-pink-600/50 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Link className="w-4 h-4" />
                {connecting === 'drive' ? 'Connecting...' : 'Connect Google Drive'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Integration Status */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <h3 className="text-xl font-semibold text-white mb-6">Integration Status</h3>

        <div className="space-y-4">
          <div className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
            <div className="flex items-center gap-3">
              <Settings className="w-5 h-5 text-gray-400" />
              <div>
                <p className="text-white font-medium">OAuth Connection</p>
                <p className="text-gray-400 text-sm">Google account authentication status</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {isGoogleConnected ? (
                <Badge className="bg-green-500/20 text-green-400">Active</Badge>
              ) : (
                <Badge className="bg-red-500/20 text-red-400">Inactive</Badge>
              )}
            </div>
          </div>

          {!isGoogleConnected && (
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-yellow-200 font-medium">Google Integration Required</p>
                  <p className="text-yellow-300 text-sm mt-1">
                    Connect your Google account to enable Sheets export and Drive import features.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* API Information */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <h3 className="text-xl font-semibold text-white mb-6">API Information</h3>

        <div className="bg-gray-700/50 rounded-lg p-4">
          <h4 className="text-white font-medium mb-3">Permissions Granted</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Google Sheets - Read/Write</span>
              <Badge className={userData.google_sheets_connected ? "bg-green-500/20 text-green-400" : "bg-gray-500/20 text-gray-400"}>
                {userData.google_sheets_connected ? 'Granted' : 'Not Granted'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Google Drive - Read Only</span>
              <Badge className={isGoogleConnected ? "bg-green-500/20 text-green-400" : "bg-gray-500/20 text-gray-400"}>
                {isGoogleConnected ? 'Granted' : 'Not Granted'}
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
