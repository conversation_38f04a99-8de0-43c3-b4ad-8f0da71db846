import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import AnalyticsPageClient from '@/components/analytics/AnalyticsPageClient';
import { hasAnalyticsAccess } from '@/lib/subscription/tiers';
import AnalyticsUpgradePrompt from '@/components/analytics/AnalyticsUpgradePrompt';

export default async function AnalyticsPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }

  // Get user's current tier and marketing override
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('current_tier, subscription_status, marketing_override')
    .eq('id', user.id)
    .single();

  if (userError || !userData) {
    redirect('/dashboard');
  }

  // Check if user has analytics access
  const hasAccess = hasAnalyticsAccess(userData.current_tier, userData.marketing_override);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Analytics</h1>
          <p className="text-gray-400 mt-2">
            Comprehensive insights into your receipt data and spending patterns
          </p>
        </div>
      </div>

      {hasAccess ? (
        <AnalyticsPageClient />
      ) : (
        <AnalyticsUpgradePrompt currentTier={userData.current_tier} />
      )}
    </div>
  );
}
