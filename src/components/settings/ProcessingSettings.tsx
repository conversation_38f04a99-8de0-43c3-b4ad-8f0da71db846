'use client';

import { useState } from 'react';
import { Brain, DollarSign, Zap, Target, Save, RefreshCw, AlertCircle } from 'lucide-react';
import { UserPreferences } from './SettingsClient';

interface ProcessingSettingsProps {
  userPreferences: UserPreferences;
  onPreferencesUpdate: () => void;
}

const currencies = [
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥' },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$' },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$' },
];

const exportFormats = [
  { value: 'csv', label: 'CSV (Comma Separated Values)', description: 'Compatible with Excel and most spreadsheet apps' },
  { value: 'json', label: 'JSON (JavaScript Object Notation)', description: 'Structured data format for developers' },
  { value: 'xlsx', label: 'Excel Workbook', description: 'Native Microsoft Excel format' },
];

const retentionOptions = [
  { value: 12, label: '1 Year' },
  { value: 24, label: '2 Years' },
  { value: 36, label: '3 Years' },
  { value: 60, label: '5 Years' },
  { value: 84, label: '7 Years' },
  { value: -1, label: 'Forever' },
];

export default function ProcessingSettings({ userPreferences, onPreferencesUpdate }: ProcessingSettingsProps) {
  const [preferences, setPreferences] = useState<UserPreferences>(userPreferences);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const handlePreferenceChange = (key: keyof UserPreferences, value: any) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      const response = await fetch('/api/user/preferences', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(preferences),
      });

      const result = await response.json();

      if (result.success) {
        setHasChanges(false);
        onPreferencesUpdate();
      } else {
        throw new Error(result.error || 'Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      alert('Failed to save preferences. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    setPreferences(userPreferences);
    setHasChanges(false);
  };

  return (
    <div className="space-y-8">
      {/* AI Processing Settings */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center">
            <Brain className="w-5 h-5 text-purple-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">AI Processing</h3>
            <p className="text-gray-400 text-sm">Configure how receipts are processed and extracted</p>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <label className="flex items-center gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.auto_process_receipts}
                onChange={(e) => handlePreferenceChange('auto_process_receipts', e.target.checked)}
                className="w-4 h-4 text-pink-600 bg-gray-700 border-gray-600 rounded focus:ring-pink-500 focus:ring-2"
              />
              <div>
                <span className="text-white font-medium">Auto-process receipts</span>
                <p className="text-gray-400 text-sm">Automatically process receipts when uploaded</p>
              </div>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-3">
              <Target className="w-4 h-4 inline mr-1" />
              Confidence Threshold: {(preferences.confidence_threshold * 100).toFixed(0)}%
            </label>
            <div className="space-y-3">
              <input
                type="range"
                min="0.5"
                max="0.95"
                step="0.05"
                value={preferences.confidence_threshold}
                onChange={(e) => handlePreferenceChange('confidence_threshold', parseFloat(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-gray-500">
                <span>50% (More results)</span>
                <span>95% (Higher accuracy)</span>
              </div>
              <div className="bg-gray-700/50 rounded-lg p-3">
                <p className="text-gray-300 text-sm">
                  Receipts with confidence scores below this threshold will be flagged for manual review.
                  Higher values mean more accurate results but may require more manual verification.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Currency Settings */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-green-600/20 rounded-lg flex items-center justify-center">
            <DollarSign className="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Currency Settings</h3>
            <p className="text-gray-400 text-sm">Set your default currency for receipt processing</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-400 mb-3">
            Default Currency
          </label>
          <select
            value={preferences.default_currency}
            onChange={(e) => handlePreferenceChange('default_currency', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          >
            {currencies.map((currency) => (
              <option key={currency.code} value={currency.code}>
                {currency.symbol} {currency.name} ({currency.code})
              </option>
            ))}
          </select>
          <p className="text-gray-500 text-sm mt-2">
            This will be used as the default currency when processing receipts that don't specify one.
          </p>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center">
            <Zap className="w-5 h-5 text-blue-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Notifications</h3>
            <p className="text-gray-400 text-sm">Choose when to receive email notifications</p>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="flex items-center gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.notification_email}
                onChange={(e) => handlePreferenceChange('notification_email', e.target.checked)}
                className="w-4 h-4 text-pink-600 bg-gray-700 border-gray-600 rounded focus:ring-pink-500 focus:ring-2"
              />
              <div>
                <span className="text-white font-medium">Email notifications</span>
                <p className="text-gray-400 text-sm">Receive general updates and important announcements</p>
              </div>
            </label>
          </div>

          <div>
            <label className="flex items-center gap-3 cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.notification_processing}
                onChange={(e) => handlePreferenceChange('notification_processing', e.target.checked)}
                className="w-4 h-4 text-pink-600 bg-gray-700 border-gray-600 rounded focus:ring-pink-500 focus:ring-2"
              />
              <div>
                <span className="text-white font-medium">Processing notifications</span>
                <p className="text-gray-400 text-sm">Get notified when receipt processing completes or fails</p>
              </div>
            </label>
          </div>
        </div>
      </div>

      {/* Data Export Settings */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-orange-600/20 rounded-lg flex items-center justify-center">
            <RefreshCw className="w-5 h-5 text-orange-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Export Preferences</h3>
            <p className="text-gray-400 text-sm">Configure default export settings</p>
          </div>
        </div>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-3">
              Default Export Format
            </label>
            <div className="space-y-3">
              {exportFormats.map((format) => (
                <label key={format.value} className="flex items-start gap-3 cursor-pointer">
                  <input
                    type="radio"
                    name="exportFormat"
                    value={format.value}
                    checked={preferences.export_format === format.value}
                    onChange={(e) => handlePreferenceChange('export_format', e.target.value)}
                    className="w-4 h-4 text-pink-600 bg-gray-700 border-gray-600 focus:ring-pink-500 focus:ring-2 mt-0.5"
                  />
                  <div>
                    <span className="text-white font-medium">{format.label}</span>
                    <p className="text-gray-400 text-sm">{format.description}</p>
                  </div>
                </label>
              ))}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-3">
              Data Retention Period
            </label>
            <select
              value={preferences.data_retention_months}
              onChange={(e) => handlePreferenceChange('data_retention_months', parseInt(e.target.value))}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            >
              {retentionOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <p className="text-gray-500 text-sm mt-2">
              How long to keep your receipt data before automatic deletion. Choose "Forever" to keep data indefinitely.
            </p>
          </div>
        </div>
      </div>

      {/* Save Changes */}
      {hasChanges && (
        <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-yellow-200 font-medium">Unsaved Changes</p>
                <p className="text-yellow-300 text-sm">You have unsaved changes to your processing preferences.</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={handleReset}
                disabled={saving}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-600/50 text-white rounded-lg transition-colors"
              >
                Reset
              </button>
              <button
                onClick={handleSave}
                disabled={saving}
                className="flex items-center gap-2 bg-pink-600 hover:bg-pink-700 disabled:bg-pink-600/50 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <Save className="w-4 h-4" />
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
