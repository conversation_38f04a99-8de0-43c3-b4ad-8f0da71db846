-- Enhanced Pricing Tiers and Paystack Integration Migration
-- Date: 2025-06-27

-- Update users table to support new tier structure and Paystack integration
ALTER TABLE users 
  DROP CONSTRAINT IF EXISTS users_current_tier_check,
  DROP CONSTRAINT IF EXISTS users_subscription_status_check;

-- Add new tier constraints
ALTER TABLE users 
  ADD CONSTRAINT users_current_tier_check 
  CHECK (current_tier IN ('free', 'professional', 'business'));

-- Add new subscription status constraints  
ALTER TABLE users 
  ADD CONSTRAINT users_subscription_status_check 
  CHECK (subscription_status IN ('inactive', 'active', 'cancelled', 'expired', 'past_due'));

-- Add new columns for enhanced subscription management
ALTER TABLE users ADD COLUMN IF NOT EXISTS paystack_customer_code VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS paystack_authorization_code VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_plan_code VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS monthly_receipt_limit INTEGER DEFAULT 10;
ALTER TABLE users ADD COLUMN IF NOT EXISTS current_period_start TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS current_period_end TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS receipts_used_this_period INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_payment_date TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS next_billing_date TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS data_retention_months INTEGER DEFAULT 1;

-- Create subscription_transactions table for payment tracking
CREATE TABLE IF NOT EXISTS subscription_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  paystack_reference VARCHAR(255) UNIQUE NOT NULL,
  paystack_transaction_id BIGINT,
  amount DECIMAL(12,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'KES',
  status VARCHAR(20) DEFAULT 'pending', -- pending, success, failed, abandoned
  tier VARCHAR(20) NOT NULL,
  payment_method VARCHAR(50),
  gateway_response TEXT,
  metadata JSONB,
  paid_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subscription_transactions_user_id ON subscription_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscription_transactions_reference ON subscription_transactions(paystack_reference);
CREATE INDEX IF NOT EXISTS idx_subscription_transactions_status ON subscription_transactions(status);
CREATE INDEX IF NOT EXISTS idx_users_paystack_customer ON users(paystack_customer_code);
CREATE INDEX IF NOT EXISTS idx_users_current_tier ON users(current_tier);
CREATE INDEX IF NOT EXISTS idx_users_subscription_status ON users(subscription_status);

-- Add RLS policies for subscription_transactions
ALTER TABLE subscription_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own subscription transactions" ON subscription_transactions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscription transactions" ON subscription_transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create function to reset monthly usage
CREATE OR REPLACE FUNCTION reset_monthly_usage()
RETURNS void AS $$
BEGIN
  UPDATE users 
  SET receipts_used_this_period = 0,
      current_period_start = CURRENT_TIMESTAMP,
      current_period_end = CURRENT_TIMESTAMP + INTERVAL '1 month'
  WHERE current_period_end < CURRENT_TIMESTAMP
    AND subscription_status = 'active';
END;
$$ LANGUAGE plpgsql;

-- Create function to check tier limits
CREATE OR REPLACE FUNCTION check_tier_limits(user_id_param UUID)
RETURNS TABLE(
  can_process_receipt BOOLEAN,
  receipts_remaining INTEGER,
  has_analytics_access BOOLEAN,
  has_google_drive_access BOOLEAN,
  data_retention_months INTEGER
) AS $$
DECLARE
  user_tier TEXT;
  user_limit INTEGER;
  user_used INTEGER;
  user_status TEXT;
  user_retention INTEGER;
  remaining_receipts INTEGER;
  can_process BOOLEAN;
  has_analytics BOOLEAN;
  has_drive BOOLEAN;
BEGIN
  -- Get user data
  SELECT
    current_tier,
    monthly_receipt_limit,
    COALESCE(receipts_used_this_period, 0),
    subscription_status,
    COALESCE(data_retention_months, 1)
  INTO user_tier, user_limit, user_used, user_status, user_retention
  FROM users
  WHERE id = user_id_param;

  -- If user not found, return default values
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 0, false, false, 1;
    RETURN;
  END IF;

  -- Calculate remaining receipts
  remaining_receipts := GREATEST(0, user_limit - user_used);

  -- Check if user can process receipts
  can_process := remaining_receipts > 0 AND user_status IN ('active', 'inactive');

  -- Check tier-based features
  has_analytics := user_tier IN ('professional', 'business');
  has_drive := user_tier = 'business';

  -- Return results
  RETURN QUERY SELECT
    can_process,
    remaining_receipts,
    has_analytics,
    has_drive,
    user_retention;
END;
$$ LANGUAGE plpgsql;

-- Update existing users to new tier structure
UPDATE users 
SET current_tier = CASE 
  WHEN current_tier = 'tier1' THEN 'professional'
  WHEN current_tier = 'tier2' THEN 'business'
  ELSE 'free'
END;

-- Set monthly limits based on tier
UPDATE users 
SET monthly_receipt_limit = CASE 
  WHEN current_tier = 'free' THEN 10
  WHEN current_tier = 'professional' THEN 500
  WHEN current_tier = 'business' THEN -1 -- unlimited
END;

-- Set data retention based on tier
UPDATE users 
SET data_retention_months = CASE 
  WHEN current_tier = 'free' THEN 1
  WHEN current_tier = 'professional' THEN 6
  WHEN current_tier = 'business' THEN 12
END;

-- Initialize billing periods for active users
UPDATE users 
SET current_period_start = CURRENT_TIMESTAMP,
    current_period_end = CURRENT_TIMESTAMP + INTERVAL '1 month'
WHERE subscription_status = 'active' 
  AND current_period_start IS NULL;

-- Create trigger to update updated_at timestamp for subscription_transactions
CREATE OR REPLACE FUNCTION update_subscription_transactions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_subscription_transactions_updated_at
    BEFORE UPDATE ON subscription_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_subscription_transactions_updated_at();
