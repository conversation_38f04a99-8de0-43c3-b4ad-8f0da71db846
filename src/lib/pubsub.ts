import { redis } from './redis'

// Redis pub/sub channels
export const CHANNELS = {
  JOB_STATUS: 'job:status',
  USER_NOTIFICATIONS: 'user:notifications',
  SYSTEM_EVENTS: 'system:events'
} as const

// Job status update interface
export interface JobStatusUpdate {
  event_type: 'job.status_changed' | 'job.completed' | 'job.failed'
  job_id: string
  receipt_id: string
  user_id: string
  old_status?: string
  new_status: string
  confidence_score?: number
  error_message?: string
  google_sheet_row_number?: number
  updated_at: string
}

// User notification interface
export interface UserNotification {
  event_type: 'receipt.processed' | 'receipt.failed' | 'receipt.uploaded' | 'webhook.triggered'
  user_id: string
  receipt_id: string
  message: string
  data?: Record<string, any>
  timestamp: string
}

// System event interface
export interface SystemEvent {
  event_type: 'queue.health' | 'webhook.triggered' | 'processing.stats'
  data: Record<string, any>
  timestamp: string
}

// Publish job status update
export async function publishJobStatusUpdate(update: JobStatusUpdate): Promise<void> {
  try {
    const message = JSON.stringify(update)
    
    // Publish to general job status channel
    await (redis as any).publish(CHANNELS.JOB_STATUS, message)
    
    // Publish to user-specific channel
    const userChannel = `${CHANNELS.USER_NOTIFICATIONS}:${update.user_id}`
    await (redis as any).publish(userChannel, message)
    
    console.log('📢 Published job status update:', {
      job_id: update.job_id,
      new_status: update.new_status,
      user_id: update.user_id
    })
  } catch (error) {
    console.error('❌ Failed to publish job status update:', error)
  }
}

// Publish user notification
export async function publishUserNotification(notification: UserNotification): Promise<void> {
  try {
    const message = JSON.stringify(notification)
    
    // Publish to user-specific channel
    const userChannel = `${CHANNELS.USER_NOTIFICATIONS}:${notification.user_id}`
    await (redis as any).publish(userChannel, message)
    
    console.log('📢 Published user notification:', {
      event_type: notification.event_type,
      user_id: notification.user_id,
      receipt_id: notification.receipt_id
    })
  } catch (error) {
    console.error('❌ Failed to publish user notification:', error)
  }
}

// Publish system event
export async function publishSystemEvent(event: SystemEvent): Promise<void> {
  try {
    const message = JSON.stringify(event)
    await (redis as any).publish(CHANNELS.SYSTEM_EVENTS, message)
    
    console.log('📢 Published system event:', event.event_type)
  } catch (error) {
    console.error('❌ Failed to publish system event:', error)
  }
}

// Subscribe to job status updates
export async function subscribeToJobStatus(
  callback: (update: JobStatusUpdate) => void
): Promise<() => void> {
  try {
    // Create a new Redis connection for subscribing
    const subscriber = redis
    
    await (subscriber as any).subscribe(CHANNELS.JOB_STATUS, (message: string) => {
      try {
        const update: JobStatusUpdate = JSON.parse(message)
        callback(update)
      } catch (error) {
        console.error('❌ Failed to parse job status update:', error)
      }
    })
    
    console.log('👂 Subscribed to job status updates')
    
    // Return unsubscribe function
    return async () => {
      await (subscriber as any).unsubscribe(CHANNELS.JOB_STATUS)
      console.log('🔇 Unsubscribed from job status updates')
    }
  } catch (error) {
    console.error('❌ Failed to subscribe to job status:', error)
    return () => {} // Return no-op function
  }
}

// Subscribe to user-specific notifications
export async function subscribeToUserNotifications(
  userId: string,
  callback: (notification: UserNotification | JobStatusUpdate) => void
): Promise<() => void> {
  try {
    // Create a new Redis connection for subscribing
    const subscriber = redis
    const userChannel = `${CHANNELS.USER_NOTIFICATIONS}:${userId}`
    
    await (subscriber as any).subscribe(userChannel, (message: string) => {
      try {
        const notification = JSON.parse(message)
        callback(notification)
      } catch (error) {
        console.error('❌ Failed to parse user notification:', error)
      }
    })
    
    console.log('👂 Subscribed to user notifications:', userId)
    
    // Return unsubscribe function
    return async () => {
      await (subscriber as any).unsubscribe(userChannel)
      console.log('🔇 Unsubscribed from user notifications:', userId)
    }
  } catch (error) {
    console.error('❌ Failed to subscribe to user notifications:', error)
    return () => {} // Return no-op function
  }
}

// Subscribe to system events
export async function subscribeToSystemEvents(
  callback: (event: SystemEvent) => void
): Promise<() => void> {
  try {
    // Create a new Redis connection for subscribing
    const subscriber = redis
    
    await (subscriber as any).subscribe(CHANNELS.SYSTEM_EVENTS, (message: string) => {
      try {
        const event: SystemEvent = JSON.parse(message)
        callback(event)
      } catch (error) {
        console.error('❌ Failed to parse system event:', error)
      }
    })
    
    console.log('👂 Subscribed to system events')
    
    // Return unsubscribe function
    return async () => {
      await (subscriber as any).unsubscribe(CHANNELS.SYSTEM_EVENTS)
      console.log('🔇 Unsubscribed from system events')
    }
  } catch (error) {
    console.error('❌ Failed to subscribe to system events:', error)
    return () => {} // Return no-op function
  }
}

// Helper function to create Redis subscriber instance
export function createRedisSubscriber() {
  // For Upstash Redis, we need to create a separate connection for pub/sub
  // This is because Upstash Redis doesn't support traditional pub/sub
  // We'll use polling as a fallback for real-time updates
  return redis
}

// Fallback: Store notifications in Redis with TTL for polling
export async function storeNotificationForPolling(
  userId: string,
  notification: UserNotification | JobStatusUpdate
): Promise<void> {
  try {
    const key = `notifications:${userId}:${Date.now()}`
    const message = JSON.stringify(notification)
    
    // Store with 1 hour TTL
    await (redis as any).setex(key, 3600, message)
    
    // Also add to a user's notification list (for batch retrieval)
    const listKey = `notifications:${userId}:list`
    await (redis as any).lpush(listKey, key)
    await (redis as any).expire(listKey, 3600)
    
    console.log('💾 Stored notification for polling:', {
      user_id: userId,
      key
    })
  } catch (error) {
    console.error('❌ Failed to store notification for polling:', error)
  }
}

// Retrieve notifications for polling
export async function getNotificationsForPolling(userId: string): Promise<(UserNotification | JobStatusUpdate)[]> {
  try {
    const listKey = `notifications:${userId}:list`
    const keys = await (redis as any).lrange(listKey, 0, -1)
    
    if (!keys || keys.length === 0) {
      return []
    }
    
    const notifications: (UserNotification | JobStatusUpdate)[] = []
    
    for (const key of keys) {
      try {
        const message = await (redis as any).get(key)
        if (message) {
          const notification = JSON.parse(message)
          notifications.push(notification)
          
          // Remove the notification after reading
          await (redis as any).del(key)
        }
      } catch (error) {
        console.error('❌ Failed to parse notification:', error)
      }
    }
    
    // Clear the list
    await (redis as any).del(listKey)
    
    return notifications
  } catch (error) {
    console.error('❌ Failed to get notifications for polling:', error)
    return []
  }
}
