import { NextRequest, NextResponse } from 'next/server';

// Vercel Cron Job for Data Retention Monitoring
// This endpoint is called by Vercel's cron scheduler
export async function GET(request: NextRequest) {
  try {
    // Verify this is a legitimate cron request from Vercel
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      console.error('Unauthorized cron request');
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    console.log('🔍 Starting scheduled data retention monitoring...');

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

    // Call the Supabase Edge Function
    const response = await fetch(`${supabaseUrl}/functions/v1/data-retention-monitor`, {
      method: 'POST',
      headers: {
        'Authorization': `<PERSON><PERSON> ${supabaseServiceKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        source: 'vercel-cron',
        timestamp: new Date().toISOString(),
        environment: process.env.VERCEL_ENV || 'development'
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Monitor function failed: ${result.error || 'Unknown error'}`);
    }

    console.log('✅ Data retention monitoring completed successfully');
    console.log('📊 Results:', {
      totalUsers: result.totalUsers,
      usersNeedingCleanup: result.usersNeedingCleanup,
      totalExpiredReceipts: result.totalExpiredReceipts
    });

    // Send notification if there are users needing cleanup
    if (result.usersNeedingCleanup > 0) {
      console.log(`⚠️ ${result.usersNeedingCleanup} users have expired receipts`);
      
      // You could send an email notification here
      // await sendAdminNotification(result);
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      source: 'vercel-cron',
      result: {
        totalUsers: result.totalUsers,
        usersNeedingCleanup: result.usersNeedingCleanup,
        totalExpiredReceipts: result.totalExpiredReceipts,
        executionTime: result.executionTime
      }
    });

  } catch (error) {
    console.error('❌ Data retention monitoring failed:', error);
    
    // You could send an error notification here
    // await sendErrorNotification(error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      source: 'vercel-cron'
    }, { status: 500 });
  }
}

// Also support POST for manual triggers
export async function POST(request: NextRequest) {
  return GET(request);
}
