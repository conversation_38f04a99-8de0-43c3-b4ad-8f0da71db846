import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { Upload, FileText, BarChart3, Zap, CheckCircle, ArrowRight, Play, Star, Users, Shield, Clock, Mail } from 'lucide-react'
import MobileNav from '@/components/homepage/MobileNav'
import FloatingCTA from '@/components/homepage/FloatingCTA'
import FooterLinks from '@/components/homepage/FooterLinks'

export default async function HomePage() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  // If user is logged in, redirect to dashboard
  if (user) {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-4 sm:py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
          </div>

          <div className="flex items-center space-x-4">
            <nav className="hidden md:flex items-center space-x-6">
              <a href="#features" className="text-gray-300 hover:text-white transition-colors">Features</a>
              <a href="#how-it-works" className="text-gray-300 hover:text-white transition-colors">How it Works</a>
              <a href="#pricing" className="text-gray-300 hover:text-white transition-colors">Pricing</a>
            </nav>
            <Link
              href="/login"
              className="hidden md:block bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
            >
              Sign In
            </Link>
            <MobileNav />
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="relative z-10 px-4 py-12 sm:py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16">
            {/* Trust Badge */}
            <div className="inline-flex items-center gap-2 bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-full px-3 sm:px-4 py-2 mb-6 sm:mb-8 animate-fade-in-up">
              <div className="flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-3 sm:w-4 h-3 sm:h-4 text-yellow-400 fill-current" />
                ))}
              </div>
              <span className="text-gray-300 text-xs sm:text-sm">Trusted by 1000+ businesses</span>
            </div>

            <h1 className="text-3xl sm:text-5xl md:text-7xl font-bold text-white mb-4 sm:mb-6 leading-tight animate-fade-in-up animate-delay-100 px-2">
              Stop Manual Data Entry.
              <br />
              <span className="bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
                Start Smart Processing.
              </span>
            </h1>

            <p className="text-base sm:text-xl md:text-2xl text-gray-300 mb-6 sm:mb-8 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animate-delay-200 px-2">
              <strong>Receipt</strong>Labs uses advanced technology to instantly extract data from receipts and automatically organize everything into Google Sheets.
              Transform hours of manual work into seconds of automation.
            </p>

            {/* Key Benefits */}
            <div className="flex flex-wrap justify-center gap-2 sm:gap-4 md:gap-6 mb-8 sm:mb-12 animate-fade-in-up animate-delay-300 px-2">
              <div className="flex items-center gap-2 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg px-2 sm:px-3 md:px-4 py-2 hover:border-green-500/50 transition-colors">
                <CheckCircle className="w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 text-green-400 flex-shrink-0" />
                <span className="text-gray-300 text-xs sm:text-sm md:text-base">99% Accuracy</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg px-2 sm:px-3 md:px-4 py-2 hover:border-blue-500/50 transition-colors">
                <Clock className="w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 text-blue-400 flex-shrink-0" />
                <span className="text-gray-300 text-xs sm:text-sm md:text-base">Instant Processing</span>
              </div>
              <div className="flex items-center gap-2 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg px-2 sm:px-3 md:px-4 py-2 hover:border-purple-500/50 transition-colors">
                <Shield className="w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 text-purple-400 flex-shrink-0" />
                <span className="text-gray-300 text-xs sm:text-sm md:text-base">Bank-Level Security</span>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-16 animate-fade-in-up animate-delay-400 px-4">
              <Link
                href="/login"
                className="group bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-3 sm:py-4 px-6 sm:px-8 rounded-lg text-base sm:text-lg transition-all duration-200 transform hover:scale-105 flex items-center gap-2 w-full sm:w-auto justify-center"
              >
                Start Free Trial
                <ArrowRight className="w-4 sm:w-5 h-4 sm:h-5 group-hover:translate-x-1 transition-transform" />
              </Link>

              <button className="group border border-gray-600 hover:border-gray-500 text-white font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-lg text-base sm:text-lg transition-all duration-200 flex items-center gap-2 w-full sm:w-auto justify-center">
                <Play className="w-4 sm:w-5 h-4 sm:h-5" />
                Watch Demo
              </button>
            </div>

            {/* Live Demo Preview */}
            <div className="relative max-w-5xl mx-auto animate-fade-in-up animate-delay-500">
              <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8 shadow-2xl">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 sm:gap-6 md:gap-8 items-center">
                  {/* Step 1: Upload */}
                  <div className="text-center md:col-span-2 animate-fade-in-left">
                    <div className="w-12 sm:w-16 h-12 sm:h-16 bg-pink-500/20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 animate-float">
                      <Upload className="w-6 sm:w-8 h-6 sm:h-8 text-pink-400" />
                    </div>
                    <h3 className="text-base sm:text-lg font-semibold text-white mb-2">1. Upload Receipt</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">Drag & drop or select your receipt image/PDF</p>
                  </div>

                  {/* Arrow */}
                  <div className="hidden md:flex justify-center">
                    <ArrowRight className="w-6 h-6 text-gray-500 animate-pulse-slow" />
                  </div>

                  {/* Step 2: AI Processing */}
                  <div className="text-center md:col-span-2 animate-fade-in-up animate-delay-200">
                    <div className="w-12 sm:w-16 h-12 sm:h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 animate-float animate-delay-200">
                      <Zap className="w-6 sm:w-8 h-6 sm:h-8 text-purple-400" />
                    </div>
                    <h3 className="text-base sm:text-lg font-semibold text-white mb-2">2. Engine Extracts Data</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">Advanced Engine reads vendor, items, totals, dates</p>
                  </div>

                  {/* Mobile Arrow */}
                  <div className="flex md:hidden justify-center">
                    <ArrowRight className="w-5 sm:w-6 h-5 sm:h-6 text-gray-500 rotate-90 animate-pulse-slow" />
                  </div>

                  {/* Desktop Arrow */}
                  <div className="hidden md:flex justify-center">
                    <ArrowRight className="w-6 h-6 text-gray-500 animate-pulse-slow" />
                  </div>

                  {/* Step 3: Google Sheets */}
                  <div className="text-center md:col-span-2 animate-fade-in-right">
                    <div className="w-12 sm:w-16 h-12 sm:h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 animate-float animate-delay-400">
                      <BarChart3 className="w-6 sm:w-8 h-6 sm:h-8 text-green-400" />
                    </div>
                    <h3 className="text-base sm:text-lg font-semibold text-white mb-2">3. Auto-Export</h3>
                    <p className="text-gray-400 text-xs sm:text-sm">Data appears instantly in your Google Sheet</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Features Section */}
      <section id="features" className="relative z-10 px-4 py-12 sm:py-20 bg-gray-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6 px-2">
              Powerful Features That Save You Time
            </h2>
            <p className="text-base sm:text-xl text-gray-300 max-w-3xl mx-auto px-2">
              Everything you need to transform your receipt management from tedious manual work to automated intelligence.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16">
            {/* Gmail Auto-Processing - COMING SOON */}
            <div className="group bg-gray-800/50 backdrop-blur-md border border-blue-500/30 rounded-xl sm:rounded-2xl p-4 sm:p-6 lg:p-8 hover:border-blue-500/50 transition-all duration-300 relative">
              {/* COMING SOON Badge */}
              <div className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-xs font-bold px-2 sm:px-3 py-1 rounded-full">
                COMING SOON
              </div>
              <div className="w-12 sm:w-16 h-12 sm:h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:bg-blue-500/30 transition-colors">
                <Mail className="w-6 sm:w-8 h-6 sm:h-8 text-blue-400" />
              </div>
              <h3 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-4 text-center">Gmail Auto-Processing</h3>
              <p className="text-gray-400 text-center mb-3 sm:mb-4 text-sm sm:text-base">
                Connect your Gmail and let AI automatically find, download, and process receipts from your emails daily. Zero manual work required.
              </p>
              <ul className="text-xs sm:text-sm text-gray-500 space-y-2">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-3 sm:w-4 h-3 sm:h-4 text-blue-400 flex-shrink-0" />
                  Daily automatic scanning
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-3 sm:w-4 h-3 sm:h-4 text-blue-400 flex-shrink-0" />
                  Smart receipt detection
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-3 sm:w-4 h-3 sm:h-4 text-blue-400 flex-shrink-0" />
                  Business plan exclusive
                </li>
              </ul>
            </div>

            {/* AI-Powered Extraction */}
            <div className="group bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 hover:border-pink-500/50 transition-all duration-300">
              <div className="w-16 h-16 bg-pink-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-pink-500/30 transition-colors">
                <Zap className="w-8 h-8 text-pink-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">AI-Powered Extraction</h3>
              <p className="text-gray-400 text-center mb-4">
                Our Advanced Vision technology extracts vendor, date, items, totals, and tax information with 99% accuracy.
              </p>
              <ul className="text-sm text-gray-500 space-y-2">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Supports images
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Multi-language receipts
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Handles poor quality scans
                </li>
              </ul>
            </div>

            {/* Google Sheets Integration */}
            <div className="group bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
              <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-500/30 transition-colors">
                <FileText className="w-8 h-8 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">Google Sheets Integration</h3>
              <p className="text-gray-400 text-center mb-4">
                Automatically creates and updates Google Sheets with structured data, organized by year with smart categorization.
              </p>
              <ul className="text-sm text-gray-500 space-y-2">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Auto-creates yearly sheets
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Smart categorization
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Real-time sync
                </li>
              </ul>
            </div>

            {/* Analytics & Insights */}
            <div className="group bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 hover:border-orange-500/50 transition-all duration-300">
              <div className="w-16 h-16 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-orange-500/30 transition-colors">
                <BarChart3 className="w-8 h-8 text-orange-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">Analytics & Insights</h3>
              <p className="text-gray-400 text-center mb-4">
                Get detailed spending analytics, category breakdowns, vendor insights, and trend analysis to understand your expenses.
              </p>
              <ul className="text-sm text-gray-500 space-y-2">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Monthly spending trends
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Category breakdowns
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Vendor analysis
                </li>
              </ul>
            </div>

            {/* Bulk Processing */}
            <div className="group bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 hover:border-blue-500/50 transition-all duration-300">
              <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-blue-500/30 transition-colors">
                <Upload className="w-8 h-8 text-blue-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">Bulk Processing</h3>
              <p className="text-gray-400 text-center mb-4">
                Upload multiple receipts at once and let our background processing handle everything automatically.
              </p>
              <ul className="text-sm text-gray-500 space-y-2">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Process up to 50 receipts
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Background processing
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Real-time status updates
                </li>
              </ul>
            </div>

            {/* Google Drive Integration */}
            <div className="group bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 hover:border-green-500/50 transition-all duration-300">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-green-500/30 transition-colors">
                <Users className="w-8 h-8 text-green-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">Google Drive Import</h3>
              <p className="text-gray-400 text-center mb-4">
                Connect your Google Drive to automatically import receipts from a designated folder.
              </p>
              <ul className="text-sm text-gray-500 space-y-2">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Auto-import from Drive
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Secure OAuth integration
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  Multi-account support
                </li>
              </ul>
            </div>

            {/* Security & Privacy */}
            <div className="group bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 hover:border-purple-500/50 transition-all duration-300">
              <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-purple-500/30 transition-colors">
                <Shield className="w-8 h-8 text-purple-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4 text-center">Security & Privacy</h3>
              <p className="text-gray-400 text-center mb-4">
                Bank-level security with encrypted storage and secure API connections to protect your sensitive data.
              </p>
              <ul className="text-sm text-gray-500 space-y-2">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  End-to-end encryption
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  SOC 2 compliant
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  GDPR compliant
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section id="how-it-works" className="relative z-10 px-4 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              How It Works
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              From receipt upload to organized data in just three simple steps. No technical knowledge required.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12 mb-16">
            {/* Step 1 */}
            <div className="text-center">
              <div className="relative mb-8">
                <div className="w-24 h-24 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Upload className="w-12 h-12 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-pink-500 rounded-full flex items-center justify-center text-white font-bold">
                  1
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Upload Your Receipts</h3>
              <p className="text-gray-400 mb-6">
                Drag and drop receipt images or PDFs, or connect your Google Drive for automatic imports.
                Supports all common formats and even poor-quality scans.
              </p>
              <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                <p className="text-sm text-gray-500">Supported formats: JPG, PNG, PDF, WebP</p>
              </div>
            </div>

            {/* Step 2 */}
            <div className="text-center">
              <div className="relative mb-8">
                <div className="w-24 h-24 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-12 h-12 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                  2
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Our Engine Processes Everything</h3>
              <p className="text-gray-400 mb-6">
                Our advanced Engine reads and extracts all relevant data: vendor name, date, items, prices, tax,
                and payment method. Processing takes just seconds.
              </p>
              <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                <p className="text-sm text-gray-500">Average processing time: 3-5 seconds</p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="text-center">
              <div className="relative mb-8">
                <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="w-12 h-12 text-white" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                  3
                </div>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Data Appears in Sheets</h3>
              <p className="text-gray-400 mb-6">
                Extracted data automatically appears in your Google Sheets, organized by year with smart categorization.
                View analytics and insights in your dashboard.
              </p>
              <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                <p className="text-sm text-gray-500">Real-time sync with Google Sheets</p>
              </div>
            </div>
          </div>

          {/* CTA */}
          <div className="text-center">
            <Link
              href="/login"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-200 transform hover:scale-105"
            >
              Try It Free Now
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="relative z-10 px-4 py-20 bg-gray-900/50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Trusted by Businesses Worldwide
            </h2>
            <p className="text-xl text-gray-300">
              Join thousands of businesses that have transformed their receipt management
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">1000+</div>
              <div className="text-gray-400">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">50K+</div>
              <div className="text-gray-400">Receipts Processed</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">99%</div>
              <div className="text-gray-400">Accuracy Rate</div>
            </div>
            <div className="text-center">
              <div className="text-4xl md:text-5xl font-bold text-white mb-2">24/7</div>
              <div className="text-gray-400">Processing</div>
            </div>
          </div>

          {/* Testimonials */}
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <div className="flex items-center gap-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-300 mb-6">
                "ReceiptLabs has saved our accounting team hours every week. The accuracy is incredible and the Google Sheets integration is seamless."
              </p>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                  S
                </div>
                <div>
                  <div className="text-white font-semibold">Sarah Akinyi</div>
                  <div className="text-gray-400 text-sm">CFO, TechStart Inc.</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <div className="flex items-center gap-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-300 mb-6">
                "The bulk processing feature is a game-changer. I can upload a month's worth of receipts and have everything organized in minutes."
              </p>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold">
                  M
                </div>
                <div>
                  <div className="text-white font-semibold">Michael Chen</div>
                  <div className="text-gray-400 text-sm">Certified Accountant</div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <div className="flex items-center gap-1 mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-300 mb-6">
                "Finally, a solution that actually works! The AI is incredibly accurate and the analytics help us track our spending patterns."
              </p>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center text-white font-bold">
                  A
                </div>
                <div>
                  <div className="text-white font-semibold">Amanda Rodriguez</div>
                  <div className="text-gray-400 text-sm">Operations Manager</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="relative z-10 px-4 py-20">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Start free and scale as you grow. No hidden fees, no long-term contracts.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Free Plan */}
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-white mb-4">Free</h3>
              <div className="text-4xl font-bold text-white mb-2">KES 0</div>
              <div className="text-gray-400 mb-8">per month</div>

              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">10 receipts per month</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Google Drive integration</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Advanced data extraction</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Enhanced Google Sheets with analytics</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">30-day data retention</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Receipt categorization and tagging</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Search and filter capabilities</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">No analytics page access</span>
                </li>
              </ul>

              <Link
                href="/login"
                className="w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 block text-center"
              >
                Start Free - No Credit Card
              </Link>
            </div>

            {/* Professional Plan */}
            <div className="bg-gradient-to-b from-pink-500/10 to-purple-600/10 backdrop-blur-md border-2 border-pink-500/50 rounded-2xl p-8 text-center relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                  Most Popular
                </div>
              </div>

              <h3 className="text-2xl font-bold text-white mb-4">Professional</h3>
              <div className="text-4xl font-bold text-white mb-2">KES 4,999</div>
              <div className="text-gray-400 mb-8">per month</div>

              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">500 receipts per month</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Google Drive integration</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Advanced data extraction</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Enhanced Google Sheets with analytics</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">6-month data retention</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Receipt categorization and tagging</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Search and filter capabilities</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Full analytics access</span>
                </li>
              </ul>

              <Link
                href="/login"
                className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 block text-center"
              >
                Start Professional Plan
              </Link>
            </div>

            {/* Business Plan */}
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-white mb-4">Business</h3>
              <div className="text-4xl font-bold text-white mb-2">KES 6,999</div>
              <div className="text-gray-400 mb-8">per month</div>

              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Unlimited receipts</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">1-year data retention (per Google Sheet year)</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Google Drive integration</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Advanced data extraction</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Enhanced Google Sheets with analytics</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Receipt categorization and tagging</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Search and filter capabilities</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Full analytics access</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-blue-400" />
                  <span className="text-gray-300">📧 Gmail auto-processing (Coming Soon)</span>
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  <span className="text-gray-300">Priority support</span>
                </li>
              </ul>

              <Link
                href="/login"
                className="w-full bg-gray-700 hover:bg-gray-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 block text-center"
              >
                Start Business Plan
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="relative z-10 px-4 py-20 bg-gray-900/50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-300">
              Everything you need to know about ReceiptLabs
            </p>
          </div>

          <div className="space-y-6">
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-white mb-4">How accurate is the <strong>Receipt</strong>Labs data extraction?</h3>
              <p className="text-gray-400">
                Our platform achieves 99% accuracy on clear receipts using advanced Vision technology.
                Even with poor quality scans or handwritten receipts, we maintain high accuracy rates.
                You can always review and edit extracted data before it's exported to Google Sheets.
              </p>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-white mb-4">What file formats are supported?</h3>
              <p className="text-gray-400">
                We support all common image formats (JPG, PNG, WebP) and PDF files.
                Maximum file size is 5MB per receipt. The AI can handle receipts in multiple languages
                and various layouts from different countries.
              </p>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-white mb-4">Is my data secure?</h3>
              <p className="text-gray-400">
                Absolutely. We use bank-level encryption for all data transmission and storage.
                Your receipts are processed securely and we never store sensitive payment information.
                We're SOC 2 and GDPR compliant, ensuring your data privacy is protected.
              </p>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-white mb-4">How does the Google Sheets integration work?</h3>
              <p className="text-gray-400">
                ReceiptLabs automatically creates a new Google Sheet for each calendar year and exports
                extracted receipt data in real-time. Each receipt becomes a row with columns for
                vendor, date, items, totals, and categories. You maintain full control of your sheets.
              </p>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-white mb-4">Can I process receipts in bulk?</h3>
              <p className="text-gray-400">
                Yes! Pro and Enterprise plans support bulk processing. Upload multiple receipts at once
                and our background processing system handles them automatically. You'll receive real-time
                status updates and notifications when processing is complete.
              </p>
            </div>

            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-white mb-4">What happens if I exceed my monthly limit?</h3>
              <p className="text-gray-400">
                If you reach your monthly receipt limit, you can either upgrade your plan or wait until
                the next billing cycle. We'll notify you when you're approaching your limit so you can
                make an informed decision about upgrading.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="relative z-10 px-4 py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Ready to Transform Your Receipt Management?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of businesses saving time with Engine-powered receipt processing.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              href="/login"
              className="group bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-lg text-lg transition-all duration-200 transform hover:scale-105 flex items-center gap-2"
            >
              Start Your Free Trial
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Link>

            <p className="text-gray-400 text-sm">
              No credit card required • 10 free receipts • Cancel anytime
            </p>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 px-4 py-12 border-t border-gray-800">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            {/* Brand */}
            <div className="md:col-span-1">
              <div className="flex items-center mb-4">
                <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
              </div>
              <p className="text-gray-400 text-sm mb-4">
                AI-powered receipt processing and data extraction for modern businesses.
              </p>
              {/* Social Media Icons */}
              <div className="flex space-x-4">
                <a
                  href="#"
                  className="text-gray-400 hover:text-pink-500 transition-colors"
                  aria-label="Twitter"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
                <a
                  href="#"
                  className="text-gray-400 hover:text-pink-500 transition-colors"
                  aria-label="LinkedIn"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a
                  href="#"
                  className="text-gray-400 hover:text-pink-500 transition-colors"
                  aria-label="GitHub"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                  </svg>
                </a>
                <a
                  href="#"
                  className="text-gray-400 hover:text-pink-500 transition-colors"
                  aria-label="Facebook"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a
                  href="#"
                  className="text-gray-400 hover:text-pink-500 transition-colors"
                  aria-label="Instagram"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.897-.875-1.387-2.026-1.387-3.323s.49-2.448 1.297-3.323c.875-.897 2.026-1.387 3.323-1.387s2.448.49 3.323 1.297c.897.875 1.387 2.026 1.387 3.323s-.49 2.448-1.297 3.323c-.875.897-2.026 1.387-3.323 1.387z"/>
                  </svg>
                </a>
              </div>
            </div>

            {/* Product */}
            <div>
              <h3 className="text-white font-semibold mb-4">Product</h3>
              <ul className="space-y-2">
                <li><a href="#features" className="text-gray-400 hover:text-white transition-colors text-sm">Features</a></li>
                <li><a href="#pricing" className="text-gray-400 hover:text-white transition-colors text-sm">Pricing</a></li>
                <li><a href="#how-it-works" className="text-gray-400 hover:text-white transition-colors text-sm">How it Works</a></li>
                <li><Link href="/dashboard" className="text-gray-400 hover:text-white transition-colors text-sm">Dashboard</Link></li>
              </ul>
            </div>

            {/* Free Tools */}
            <div>
              <h3 className="text-white font-semibold mb-4">Free Tools</h3>
              <ul className="space-y-2">
                <li><Link href="/receipt-maker" className="text-gray-400 hover:text-white transition-colors text-sm">Receipt Generator</Link></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Invoice Template</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Expense Tracker</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Tax Calculator</a></li>
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-white font-semibold mb-4">Support</h3>
              <ul className="space-y-2">
                <li><a href="/blog" className="text-gray-400 hover:text-white transition-colors text-sm">Blog</a></li>
                <li><a href="/contact" className="text-gray-400 hover:text-white transition-colors text-sm">Help Center</a></li>
                <li><a href="/contact" className="text-gray-400 hover:text-white transition-colors text-sm">Contact Us</a></li>
                <li><a href="/contact" className="text-gray-400 hover:text-white transition-colors text-sm">API Documentation</a></li>
                <li><a href="/privacy" className="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a></li>
                <li><a href="/terms" className="text-gray-400 hover:text-white transition-colors text-sm">Terms of Service</a></li>
              </ul>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2025 <strong>Receipt</strong>Labs. All rights reserved.
            </p>
            <FooterLinks />
          </div>
        </div>
      </footer>

      {/* Floating CTA */}
      <FloatingCTA />
    </div>
  )
}
