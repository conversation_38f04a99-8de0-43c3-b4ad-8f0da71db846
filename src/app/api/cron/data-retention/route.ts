import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { dataRetentionCronManager, DATA_RETENTION_CRON_JOBS } from '@/lib/cron/data-retention-jobs';

// GET /api/cron/data-retention - Get cron job statuses
export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Check if user is authenticated and has admin privileges
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    // Get job statuses with next run times
    const jobStatuses = dataRetentionCronManager.getJobStatuses();

    return NextResponse.json({
      success: true,
      jobs: jobStatuses,
      totalJobs: jobStatuses.length,
      enabledJobs: jobStatuses.filter(job => job.enabled).length
    });

  } catch (error) {
    console.error('Error fetching cron job statuses:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

// POST /api/cron/data-retention - Execute cron jobs
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Check if user is authenticated and has admin privileges
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    const body = await request.json();
    const { jobName, executeAll = false } = body;

    console.log('🔧 Cron job execution request:', { jobName, executeAll, userId: user.id });

    let results;

    if (executeAll) {
      console.log('🚀 Executing all enabled data retention cron jobs...');
      results = await dataRetentionCronManager.executeAllJobs();
    } else if (jobName) {
      console.log(`🚀 Executing specific cron job: ${jobName}`);
      const result = await dataRetentionCronManager.executeCronJob(jobName);
      results = [result];
    } else {
      return NextResponse.json({ 
        success: false, 
        error: 'Either jobName or executeAll must be specified' 
      }, { status: 400 });
    }

    // Log execution results
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    console.log(`📊 Cron job execution completed: ${successCount} successful, ${failureCount} failed`);

    // Store execution log in database (optional)
    try {
      const executionLog = {
        user_id: user.id,
        execution_type: executeAll ? 'all' : 'single',
        job_name: jobName || 'all',
        results: results,
        success_count: successCount,
        failure_count: failureCount,
        executed_at: new Date().toISOString()
      };

      // You could store this in a cron_execution_logs table if needed
      console.log('📝 Execution log:', executionLog);
    } catch (logError) {
      console.warn('⚠️ Failed to store execution log:', logError);
    }

    return NextResponse.json({
      success: true,
      results,
      summary: {
        totalJobs: results.length,
        successful: successCount,
        failed: failureCount,
        totalExecutionTime: results.reduce((sum, r) => sum + r.executionTime, 0)
      }
    });

  } catch (error) {
    console.error('Error executing cron jobs:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

// PUT /api/cron/data-retention - Update cron job configuration
export async function PUT(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Check if user is authenticated and has admin privileges
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    const body = await request.json();
    const { jobName, enabled, schedule } = body;

    if (!jobName) {
      return NextResponse.json({ 
        success: false, 
        error: 'jobName is required' 
      }, { status: 400 });
    }

    // Find the job
    const jobIndex = DATA_RETENTION_CRON_JOBS.findIndex(job => job.name === jobName);
    if (jobIndex === -1) {
      return NextResponse.json({ 
        success: false, 
        error: `Job '${jobName}' not found` 
      }, { status: 404 });
    }

    // Validate schedule if provided
    if (schedule && !dataRetentionCronManager.validateCronExpression(schedule)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid cron expression' 
      }, { status: 400 });
    }

    // Update job configuration
    if (enabled !== undefined) {
      DATA_RETENTION_CRON_JOBS[jobIndex].enabled = enabled;
    }
    if (schedule) {
      DATA_RETENTION_CRON_JOBS[jobIndex].schedule = schedule;
    }

    console.log(`⚙️ Updated cron job '${jobName}':`, {
      enabled: DATA_RETENTION_CRON_JOBS[jobIndex].enabled,
      schedule: DATA_RETENTION_CRON_JOBS[jobIndex].schedule
    });

    return NextResponse.json({
      success: true,
      job: DATA_RETENTION_CRON_JOBS[jobIndex],
      message: `Job '${jobName}' updated successfully`
    });

  } catch (error) {
    console.error('Error updating cron job:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}
