import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { processGmailForUser } from '@/lib/gmail/processor';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user has Business tier access
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status, gmail_connected')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (userData.current_tier !== 'business') {
      return NextResponse.json({ error: 'Business plan required' }, { status: 403 });
    }

    if (!userData.gmail_connected) {
      return NextResponse.json({ error: 'Gmail not connected' }, { status: 400 });
    }

    // Parse request body for options
    const body = await request.json().catch(() => ({}));
    const { 
      dateAfter, 
      dateBefore, 
      maxEmails = 20, // Limit for manual processing
      keywords,
      minConfidence = 60 
    } = body;

    // Validate date inputs
    let dateAfterObj: Date | undefined;
    let dateBeforeObj: Date | undefined;

    if (dateAfter) {
      dateAfterObj = new Date(dateAfter);
      if (isNaN(dateAfterObj.getTime())) {
        return NextResponse.json({ error: 'Invalid dateAfter format' }, { status: 400 });
      }
    } else {
      // Default to last 24 hours
      dateAfterObj = new Date();
      dateAfterObj.setDate(dateAfterObj.getDate() - 1);
    }

    if (dateBefore) {
      dateBeforeObj = new Date(dateBefore);
      if (isNaN(dateBeforeObj.getTime())) {
        return NextResponse.json({ error: 'Invalid dateBefore format' }, { status: 400 });
      }
    }

    // Validate other inputs
    if (maxEmails && (typeof maxEmails !== 'number' || maxEmails < 1 || maxEmails > 100)) {
      return NextResponse.json({ error: 'maxEmails must be between 1 and 100' }, { status: 400 });
    }

    if (minConfidence && (typeof minConfidence !== 'number' || minConfidence < 0 || minConfidence > 100)) {
      return NextResponse.json({ error: 'minConfidence must be between 0 and 100' }, { status: 400 });
    }

    console.log(`🚀 Manual Gmail processing requested by user ${user.id}`);

    // Process Gmail for the user
    const result = await processGmailForUser({
      userId: user.id,
      dateAfter: dateAfterObj,
      dateBefore: dateBeforeObj,
      maxEmails,
      keywords: keywords ? keywords.split(',').map((k: string) => k.trim()) : undefined,
      minConfidence
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          processedEmails: result.processedEmails,
          receiptsFound: result.receiptsFound,
          receiptsCreated: result.receiptsCreated.length,
          processingTime: result.processingTime,
          errors: result.errors
        },
        message: `Processed ${result.processedEmails} emails, found ${result.receiptsFound} receipts, created ${result.receiptsCreated.length} receipt records`
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Gmail processing failed',
        details: result.errors
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in Gmail processing API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process Gmail',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
