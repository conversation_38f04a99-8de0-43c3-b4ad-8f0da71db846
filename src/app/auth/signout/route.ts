import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('Signout route called')
    const supabase = await createClient()

    // Check if a user's logged in
    const {
      data: { user },
    } = await supabase.auth.getUser()

    console.log('User found:', !!user)

    if (user) {
      console.log('Signing out user:', user.id)
      await supabase.auth.signOut()
      console.log('User signed out successfully')
    }

    console.log('Redirecting to homepage')
    return NextResponse.redirect(new URL('/', request.url))
  } catch (error) {
    console.error('Error in signout route:', error)
    return NextResponse.redirect(new URL('/', request.url))
  }
}