import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('🔍 Test process-receipt function started');
    
    // Validate environment variables
    const requiredEnvVars = [
      'SUPABASE_URL',
      'SUPABASE_SERVICE_ROLE_KEY',
      'GOOGLE_CLIENT_ID',
      'GOOGLE_CLIENT_SECRET'
    ];

    const missingVars = [];
    for (const envVar of requiredEnvVars) {
      if (!Deno.env.get(envVar)) {
        missingVars.push(envVar);
      }
    }

    if (missingVars.length > 0) {
      console.error('❌ Missing environment variables:', missingVars);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `Missing environment variables: ${missingVars.join(', ')}` 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('✅ Environment variables validated');

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false
        }
      }
    );

    console.log('✅ Supabase client initialized');

    // Parse request body
    const body = await req.json();
    console.log('📝 Request body received:', { hasJob: !!body.job });

    if (!body.job) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing job data in request body' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    const job = body.job;
    console.log('📋 Job details:', {
      id: job.id,
      receiptId: job.receiptId,
      userId: job.userId,
      hasImageUrl: !!job.data?.imageUrl
    });

    // Test database connection
    const { data: testData, error: testError } = await supabase
      .from('receipts')
      .select('id')
      .eq('id', job.receiptId)
      .single();

    if (testError) {
      console.error('❌ Database test failed:', testError);
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: `Database connection failed: ${testError.message}` 
        }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log('✅ Database connection successful');

    // Test Google Sheets connection
    try {
      const { data: user } = await supabase
        .from('users')
        .select('google_access_token, google_refresh_token')
        .eq('id', job.userId)
        .single();

      console.log('👤 User Google auth status:', {
        hasAccessToken: !!user?.google_access_token,
        hasRefreshToken: !!user?.google_refresh_token
      });

      if (user?.google_access_token) {
        // Test Google Sheets API
        const testResponse = await fetch('https://sheets.googleapis.com/v4/spreadsheets', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${user.google_access_token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            properties: {
              title: 'Test Sheet - Delete Me'
            }
          })
        });

        if (testResponse.ok) {
          console.log('✅ Google Sheets API test successful');
          const testSheet = await testResponse.json();
          
          // Clean up test sheet
          try {
            await fetch(`https://www.googleapis.com/drive/v3/files/${testSheet.spreadsheetId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${user.google_access_token}`
              }
            });
            console.log('🗑️ Test sheet cleaned up');
          } catch (cleanupError) {
            console.log('⚠️ Failed to cleanup test sheet:', cleanupError);
          }
        } else {
          const errorText = await testResponse.text();
          console.error('❌ Google Sheets API test failed:', {
            status: testResponse.status,
            statusText: testResponse.statusText,
            error: errorText
          });
        }
      }
    } catch (googleError) {
      console.error('❌ Google Sheets test error:', googleError);
    }

    console.log('🎉 Test completed successfully');

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Test process-receipt function completed successfully',
        jobId: job.id,
        receiptId: job.receiptId
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('❌ Test function error:', error);
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
