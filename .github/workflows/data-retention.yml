name: Data Retention Cron Jobs

on:
  schedule:
    # Monitor daily at 2 AM UTC
    - cron: '0 2 * * *'
    # Cleanup weekly on Sundays at 3 AM UTC
    - cron: '0 3 * * 0'
  workflow_dispatch: # Allow manual triggers

jobs:
  data-retention:
    runs-on: ubuntu-latest
    
    steps:
      - name: Determine job type
        id: job-type
        run: |
          if [[ "${{ github.event.schedule }}" == "0 2 * * *" ]]; then
            echo "job=monitor" >> $GITHUB_OUTPUT
          elif [[ "${{ github.event.schedule }}" == "0 3 * * 0" ]]; then
            echo "job=cleanup" 
          else
            echo "job=monitor" >> $GITHUB_OUTPUT  # Default for manual runs
          fi

      - name: Run Data Retention Monitor
        if: steps.job-type.outputs.job == 'monitor'
        run: |
          curl -X POST \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -H "Content-Type: application/json" \
            -d '{"source":"github-actions","timestamp":"'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"}' \
            "${{ secrets.VERCEL_URL }}/api/cron/data-retention-monitor"

      - name: Run Data Retention Cleanup
        if: steps.job-type.outputs.job == 'cleanup'
        run: |
          curl -X POST \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -H "Content-Type: application/json" \
            -d '{"source":"github-actions","timestamp":"'$(date -u +%Y-%m-%dT%H:%M:%S.%3NZ)'"}' \
            "${{ secrets.VERCEL_URL }}/api/cron/data-retention-cleanup"
