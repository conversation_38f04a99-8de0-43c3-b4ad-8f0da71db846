# ReceiptLabs Video Script for Google Console App Verification

**Video Title:** ReceiptLabs - AI-Powered Receipt Management Platform Demo  
**Duration:** 3-4 minutes  
**Purpose:** Google Console App Verification  
**Date:** January 2, 2025  

---

## Video Overview
This video demonstrates ReceiptLabs' core functionality and justifies the use of Google OAuth scopes for Google Console app verification. The demo shows real-world usage scenarios and explains how each Google API integration benefits users.

---

## SCENE 1: Introduction and Overview (0:00 - 0:30)

### Visual: ReceiptLabs Homepage
**Narrator:** "Welcome to ReceiptLabs, an AI-powered receipt management platform that transforms how businesses and individuals handle their expense tracking and accounting."

### Visual: Show logo and key features
**Narrator:** "ReceiptLabs eliminates manual data entry by automatically extracting information from receipts and organizing it into Google Sheets for seamless accounting workflows."

### Visual: Quick montage of receipt processing
**Narrator:** "Today, I'll demonstrate our core features and show exactly how we use Google APIs to provide valuable automation services to our users."

---

## SCENE 2: User Authentication and Google OAuth (0:30 - 1:00)

### Visual: Login page with Google OAuth button
**Narrator:** "Users sign in securely using Google OAuth. This ensures data security while enabling integration with Google services."

### Action: Click "Sign in with Google"
**Narrator:** "When users first sign in, they're presented with clear permission requests for the Google services we need."

### Visual: Google OAuth consent screen
**Narrator:** "We request three specific permissions: Google Sheets for data organization, Google Drive for batch receipt import, and Gmail for automated receipt processing on our Business plan."

### Visual: Show permission explanations
**Narrator:** "Each permission serves a specific purpose that directly benefits the user, and users maintain full control over their data."

---

## SCENE 3: Core Feature - Receipt Upload and Processing (1:00 - 1:45)

### Visual: Dashboard upload interface
**Narrator:** "Let me show you our core functionality. Users can upload receipt images directly through our dashboard."

### Action: Upload a sample receipt image
**Narrator:** "I'm uploading a receipt from a local restaurant. Our AI engine immediately begins processing the image."

### Visual: Processing status and progress
**Narrator:** "Our AI extracts key information: vendor name, date, total amount, tax details, and individual line items. This typically takes just 3-5 seconds."

### Visual: Show extracted data preview
**Narrator:** "Here's the extracted data. Users can review and edit any information before it's exported to their Google Sheet."

### Action: Click "Export to Google Sheets"
**Narrator:** "With one click, this data is automatically organized into a structured spreadsheet."

---

## SCENE 4: Google Sheets Integration Demo (1:45 - 2:15)

### Visual: Google Sheets being created/updated
**Narrator:** "This is why we need Google Sheets API access. ReceiptLabs creates properly formatted spreadsheets with headers for Date, Vendor, Amount, Tax, Category, and more."

### Visual: Show the populated spreadsheet
**Narrator:** "Each receipt becomes a new row with all extracted data organized for accounting purposes. This eliminates hours of manual data entry for businesses."

### Visual: Multiple receipts in the sheet
**Narrator:** "As users process more receipts, their spreadsheet grows into a comprehensive expense tracking system that accountants and bookkeepers can immediately use."

---

## SCENE 5: Google Drive Integration Demo (2:15 - 2:45)

### Visual: Google Drive folder with receipt images
**Narrator:** "For businesses with many receipts, our Google Drive integration enables batch processing. Users simply upload receipts to a designated Drive folder."

### Action: Show Drive folder selection
**Narrator:** "Users choose which folder to monitor, giving them complete control over what we access."

### Visual: Batch processing interface
**Narrator:** "ReceiptLabs automatically detects new receipt images and processes them in batches. This is particularly valuable for businesses that collect receipts throughout the day."

### Visual: Multiple receipts being processed
**Narrator:** "Each receipt is processed individually, with extracted data automatically added to the user's Google Sheet. The original images remain in their Drive folder."

---

## SCENE 6: Gmail Integration Demo (Business Plan) (2:45 - 3:15)

### Visual: Gmail inbox with receipt attachments
**Narrator:** "Our Business plan includes Gmail integration for the ultimate automation. Many businesses receive receipts via email attachments."

### Visual: Gmail processing settings
**Narrator:** "Users can enable automated scanning of their Gmail for receipt attachments. This runs on a schedule they control - daily, weekly, or on-demand."

### Action: Show email with receipt attachment being processed
**Narrator:** "When ReceiptLabs detects a receipt attachment, it processes the image and adds the data to the user's spreadsheet automatically. We only access attachments, never email content, and everything is logged transparently."

### Visual: Processing log and results
**Narrator:** "Users see exactly what was processed and can review all extracted data. This feature saves businesses hours of manual work each week."

---

## SCENE 7: Privacy and Security Features (3:15 - 3:45)

### Visual: Privacy policy and settings page
**Narrator:** "Privacy and security are paramount. Users can revoke Google permissions at any time, delete their data, or export everything in standard formats."

### Visual: Data retention settings
**Narrator:** "We offer transparent data retention policies based on subscription tiers, and users can delete individual receipts or their entire account whenever they choose."

### Visual: Security features
**Narrator:** "All data is encrypted in transit and at rest, and we never store Google service data beyond what's necessary for processing."

---

## SCENE 8: Real-World Impact and Conclusion (3:45 - 4:00)

### Visual: Before/after comparison of manual vs automated workflow
**Narrator:** "ReceiptLabs transforms tedious manual processes into automated workflows. What used to take hours of data entry now happens in seconds."

### Visual: Happy users and testimonials
**Narrator:** "Small businesses, freelancers, and accounting professionals use ReceiptLabs to streamline their expense management and focus on growing their business."

### Visual: ReceiptLabs logo and website
**Narrator:** "ReceiptLabs - where AI meets accounting. Visit receiptlabs.com to start your free trial today."

---

## Technical Notes for Video Production

### Screen Recording Requirements
- **Resolution:** 1920x1080 minimum
- **Frame Rate:** 30 FPS
- **Audio:** Clear narration with background music at low volume
- **Duration:** 3-4 minutes total

### Demo Data Requirements
- Use realistic but anonymized receipt data
- Show various receipt types (restaurant, office supplies, gas station)
- Demonstrate both successful processing and error handling
- Include multiple currencies if applicable

### Visual Elements to Include
- ReceiptLabs branding and logo
- Clear UI navigation
- Real-time processing indicators
- Before/after data comparisons
- Google service integrations
- Privacy and security features

### Key Messages to Emphasize
1. **Legitimate Business Purpose:** Every Google API usage serves a clear user benefit
2. **User Control:** Users maintain full control over their data and permissions
3. **Privacy First:** Transparent data handling with strong security measures
4. **Real Value:** Significant time savings and workflow improvements
5. **Professional Use:** Designed for legitimate business and accounting purposes

### Compliance Considerations
- Show actual consent screens and permission requests
- Demonstrate data deletion and export capabilities
- Highlight privacy policy and terms of service
- Show transparent logging and user notifications
- Emphasize read-only access where applicable

---

## Post-Production Checklist

- [ ] Verify all Google API interactions are clearly shown
- [ ] Confirm privacy features are prominently displayed
- [ ] Ensure realistic demo data is used throughout
- [ ] Add captions for accessibility
- [ ] Include ReceiptLabs contact information
- [ ] Verify video meets Google's verification requirements
- [ ] Test video playback on multiple devices
- [ ] Prepare video description and metadata

---

**Contact for Questions:**
- **Technical:** <EMAIL>
- **Legal:** <EMAIL>
- **Support:** <EMAIL>
