// Dynamic import to avoid SSR issues
let pdfjsLib: any = null;

// Initialize PDF.js only on client side
const initPdfJs = async () => {
  if (typeof window === 'undefined') return null;

  if (!pdfjsLib) {
    // Use dynamic import with proper error handling for SSR
    try {
      pdfjsLib = await import('pdfjs-dist');
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
    } catch (error) {
      console.error('Failed to load PDF.js:', error);
      return null;
    }
  }

  return pdfjsLib;
};

export interface PdfProcessingOptions {
  scale?: number;
  quality?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface PdfProcessingResult {
  success: boolean;
  imageFile?: File;
  error?: string;
  metadata?: {
    originalSize: number;
    convertedSize: number;
    pages: number;
    processingTime: number;
  };
}

/**
 * Check if PDF processing is enabled and supported in the current environment
 */
export function isPdfProcessingSupported(): boolean {
  // Always return false during SSR to prevent any browser API access
  if (typeof window === 'undefined') return false;

  try {
    // Check if PDF processing is enabled via environment variable
    const isEnabled = process.env.NEXT_PUBLIC_ENABLE_PDF_PROCESSING !== 'false';
    if (!isEnabled) return false;

    // Check for required browser features
    return !!(
      window.Worker &&
      window.CanvasRenderingContext2D &&
      window.Blob &&
      window.File &&
      document.createElement &&
      document.createElement('canvas').getContext('2d')
    );
  } catch {
    return false;
  }
}

/**
 * Convert PDF file to image using PDF.js
 */
export async function convertPdfToImage(
  pdfFile: File,
  options: PdfProcessingOptions = {}
): Promise<PdfProcessingResult> {
  const startTime = Date.now();

  try {
    // Validate environment
    if (!isPdfProcessingSupported()) {
      return {
        success: false,
        error: 'PDF processing not supported in this browser. Please use a modern browser or convert to image first.'
      };
    }

    // Initialize PDF.js
    const pdfjs = await initPdfJs();
    if (!pdfjs) {
      return {
        success: false,
        error: 'PDF processing not available on server side.'
      };
    }

    // Validate file
    if (!pdfFile || pdfFile.type !== 'application/pdf') {
      return {
        success: false,
        error: 'Invalid PDF file provided.'
      };
    }

    // Check file size (5MB limit to match existing system)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (pdfFile.size > maxSize) {
      return {
        success: false,
        error: 'PDF file too large. Maximum size is 5MB.'
      };
    }

    // Default options
    const {
      scale = 2.0,
      quality = 0.95,
      maxWidth = 2000,
      maxHeight = 2000
    } = options;

    // Load PDF
    const arrayBuffer = await pdfFile.arrayBuffer();
    const loadingTask = pdfjs.getDocument({
      data: arrayBuffer,
      cMapUrl: `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/cmaps/`,
      cMapPacked: true,
    });

    const pdf = await loadingTask.promise;
    
    // Get first page (receipts are typically single page)
    const page = await pdf.getPage(1);
    
    // Calculate viewport
    const viewport = page.getViewport({ scale });
    
    // Adjust scale if image would be too large
    let finalScale = scale;
    if (viewport.width > maxWidth || viewport.height > maxHeight) {
      const scaleX = maxWidth / viewport.width;
      const scaleY = maxHeight / viewport.height;
      finalScale = Math.min(scaleX, scaleY) * scale;
    }
    
    const finalViewport = page.getViewport({ scale: finalScale });
    
    // Create canvas
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    if (!context) {
      return {
        success: false,
        error: 'Failed to create canvas context for PDF rendering.'
      };
    }
    
    canvas.height = finalViewport.height;
    canvas.width = finalViewport.width;
    
    // Render PDF page to canvas
    const renderContext = {
      canvasContext: context,
      viewport: finalViewport,
    };
    
    await page.render(renderContext).promise;
    
    // Convert canvas to blob
    const blob = await new Promise<Blob>((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to convert canvas to blob'));
          }
        },
        'image/png',
        quality
      );
    });
    
    // Create File object with original filename
    const originalName = pdfFile.name.replace(/\.pdf$/i, '');
    const imageFile = new File([blob], `${originalName}.png`, {
      type: 'image/png',
      lastModified: Date.now(),
    });
    
    const processingTime = Date.now() - startTime;
    
    return {
      success: true,
      imageFile,
      metadata: {
        originalSize: pdfFile.size,
        convertedSize: imageFile.size,
        pages: pdf.numPages,
        processingTime,
      },
    };
    
  } catch (error) {
    console.error('PDF processing error:', error);
    
    const processingTime = Date.now() - startTime;
    
    return {
      success: false,
      error: error instanceof Error 
        ? `Failed to process PDF: ${error.message}`
        : 'Unknown error occurred while processing PDF.',
      metadata: {
        originalSize: pdfFile.size,
        convertedSize: 0,
        pages: 0,
        processingTime,
      },
    };
  }
}

/**
 * Validate if a file is a supported PDF
 */
export function isValidPdf(file: File): boolean {
  return file.type === 'application/pdf' && file.size > 0;
}

/**
 * Get PDF metadata without full conversion
 */
export async function getPdfMetadata(pdfFile: File): Promise<{
  pages: number;
  size: number;
  title?: string;
} | null> {
  try {
    if (!isValidPdf(pdfFile)) return null;

    // Initialize PDF.js
    const pdfjs = await initPdfJs();
    if (!pdfjs) return null;

    const arrayBuffer = await pdfFile.arrayBuffer();
    const pdf = await pdfjs.getDocument(arrayBuffer).promise;

    const metadata = await pdf.getMetadata();

    return {
      pages: pdf.numPages,
      size: pdfFile.size,
      title: metadata.info?.Title || undefined,
    };
  } catch {
    return null;
  }
}
