import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { subscriptionManager } from '@/lib/subscription/manager';
import { getTierById, isValidTierChange } from '@/lib/subscription/tiers';

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Subscription initialization started');

    const supabase = await createClient();

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log('❌ User not authenticated');
      return NextResponse.json({
        success: false,
        error: 'Not authenticated'
      }, { status: 401 });
    }

    console.log('✅ User authenticated:', user.id);

    const body = await request.json();
    const { targetTier } = body;

    console.log('📝 Request body:', { targetTier });

    if (!targetTier || !['professional', 'business'].includes(targetTier)) {
      console.log('❌ Invalid target tier:', targetTier);
      return NextResponse.json({
        success: false,
        error: 'Invalid target tier. Must be professional or business.'
      }, { status: 400 });
    }

    // Get current user data
    console.log('📊 Fetching user data...');
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status, email, full_name')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      console.log('❌ User data error:', userError);
      return NextResponse.json({
        success: false,
        error: 'User not found',
        details: userError?.message
      }, { status: 404 });
    }

    console.log('✅ User data retrieved:', userData);

    // Validate tier change
    if (!isValidTierChange(userData.current_tier, targetTier)) {
      return NextResponse.json({ 
        success: false, 
        error: `Cannot change from ${userData.current_tier} to ${targetTier}` 
      }, { status: 400 });
    }

    // Check if user is already on this tier
    if (userData.current_tier === targetTier) {
      return NextResponse.json({ 
        success: false, 
        error: 'User is already on this tier' 
      }, { status: 400 });
    }

    // Initialize payment
    console.log('💳 Initializing payment...');
    const paymentResult = await subscriptionManager.initializeSubscriptionPayment({
      userId: user.id,
      targetTier,
      userEmail: userData.email,
      userName: userData.full_name || undefined
    });

    console.log('✅ Payment initialized:', {
      reference: paymentResult.reference,
      hasAuthUrl: !!paymentResult.authorization_url
    });

    const tier = getTierById(targetTier);

    return NextResponse.json({
      success: true,
      data: {
        authorization_url: paymentResult.authorization_url,
        reference: paymentResult.reference,
        access_code: paymentResult.access_code,
        tier: {
          id: targetTier,
          name: tier?.name,
          price: tier?.price_kes,
          price_display: tier?.price_display
        }
      }
    });

  } catch (error) {
    console.error('💥 Error initializing subscription payment:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
