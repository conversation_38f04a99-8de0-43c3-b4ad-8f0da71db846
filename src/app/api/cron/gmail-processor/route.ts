import { NextRequest, NextResponse } from 'next/server';

// Vercel Cron Job for Gmail Processing
// This endpoint is called by Vercel's cron scheduler every hour
export async function GET(request: NextRequest) {
  try {
    // Verify this is a legitimate cron request from Vercel
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      console.error('Unauthorized Gmail cron request');
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    // Get current hour (0-23)
    const currentHour = new Date().getHours();
    
    console.log(`📧 Starting scheduled Gmail processing for hour ${currentHour}...`);

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

    // Call the Supabase Edge Function
    const response = await fetch(`${supabaseUrl}/functions/v1/gmail-processor`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        targetHour: currentHour,
        source: 'vercel-cron',
        timestamp: new Date().toISOString(),
        environment: process.env.VERCEL_ENV || 'development'
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Gmail processor function failed: ${result.error || 'Unknown error'}`);
    }

    console.log('✅ Gmail processing completed:', {
      processedUsers: result.processedUsers,
      totalReceipts: result.totalReceipts,
      totalEmails: result.totalEmails,
      executionTime: result.executionTime,
      errorCount: result.errors?.length || 0
    });

    // Log any errors for monitoring
    if (result.errors && result.errors.length > 0) {
      console.warn('⚠️ Gmail processing errors:', result.errors);
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      source: 'vercel-cron',
      hour: currentHour,
      result: {
        processedUsers: result.processedUsers,
        totalReceipts: result.totalReceipts,
        totalEmails: result.totalEmails,
        executionTime: result.executionTime,
        errors: result.errors
      }
    });

  } catch (error) {
    console.error('❌ Gmail cron processing failed:', error);
    
    // TODO: Send error notification to admin
    // await sendErrorNotification('Gmail Processing', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      source: 'vercel-cron'
    }, { status: 500 });
  }
}
