// Performance monitoring and caching utilities for Reco Engine

interface PerformanceMetrics {
  operation: string;
  duration: number;
  timestamp: number;
  metadata?: Record<string, any>;
}

interface CacheEntry<T> {
  data: T;
  expires: number;
  hits: number;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private cache = new Map<string, CacheEntry<any>>();
  private maxMetrics = 1000; // Keep last 1000 metrics
  private maxCacheSize = 500; // Maximum cache entries

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Record performance metric
  recordMetric(operation: string, duration: number, metadata?: Record<string, any>): void {
    const metric: PerformanceMetrics = {
      operation,
      duration,
      timestamp: Date.now(),
      metadata
    };

    this.metrics.push(metric);

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log slow operations
    if (duration > 5000) { // 5 seconds
      console.warn(`🐌 Slow operation detected: ${operation} took ${duration}ms`, metadata);
    }
  }

  // Get performance statistics
  getStats(operation?: string): {
    count: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
    recentMetrics: PerformanceMetrics[];
  } {
    const filteredMetrics = operation 
      ? this.metrics.filter(m => m.operation === operation)
      : this.metrics;

    if (filteredMetrics.length === 0) {
      return {
        count: 0,
        avgDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        recentMetrics: []
      };
    }

    const durations = filteredMetrics.map(m => m.duration);
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);

    return {
      count: filteredMetrics.length,
      avgDuration: Math.round(avgDuration),
      minDuration,
      maxDuration,
      recentMetrics: filteredMetrics.slice(-10) // Last 10 metrics
    };
  }

  // Cache operations
  setCache<T>(key: string, data: T, ttlMs: number = 300000): void { // 5 minutes default
    // Clean up expired entries if cache is getting large
    if (this.cache.size >= this.maxCacheSize) {
      this.cleanupCache();
    }

    this.cache.set(key, {
      data,
      expires: Date.now() + ttlMs,
      hits: 0
    });
  }

  getCache<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) {
      return null;
    }

    if (entry.expires <= Date.now()) {
      this.cache.delete(key);
      return null;
    }

    entry.hits++;
    return entry.data;
  }

  // Clean up expired cache entries
  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expires <= now) {
        this.cache.delete(key);
      }
    }

    // If still too large, remove least used entries
    if (this.cache.size >= this.maxCacheSize) {
      const entries = Array.from(this.cache.entries())
        .sort((a, b) => a[1].hits - b[1].hits)
        .slice(0, Math.floor(this.maxCacheSize * 0.2)); // Remove 20% least used

      entries.forEach(([key]) => this.cache.delete(key));
    }
  }

  // Get cache statistics
  getCacheStats(): {
    size: number;
    hitRate: number;
    entries: Array<{ key: string; hits: number; expires: number }>;
  } {
    const entries = Array.from(this.cache.entries()).map(([key, entry]) => ({
      key,
      hits: entry.hits,
      expires: entry.expires
    }));

    const totalHits = entries.reduce((sum, entry) => sum + entry.hits, 0);
    const hitRate = entries.length > 0 ? totalHits / entries.length : 0;

    return {
      size: this.cache.size,
      hitRate: Math.round(hitRate * 100) / 100,
      entries: entries.sort((a, b) => b.hits - a.hits).slice(0, 10) // Top 10 by hits
    };
  }

  // Clear all metrics and cache
  clear(): void {
    this.metrics = [];
    this.cache.clear();
  }
}

// Performance timer utility
export class PerformanceTimer {
  private startTime: number;
  private operation: string;
  private metadata?: Record<string, any>;
  private monitor = PerformanceMonitor.getInstance();

  constructor(operation: string, metadata?: Record<string, any>) {
    this.operation = operation;
    this.metadata = metadata;
    this.startTime = Date.now();
  }

  end(): number {
    const duration = Date.now() - this.startTime;
    this.monitor.recordMetric(this.operation, duration, this.metadata);
    return duration;
  }
}

// Cached function wrapper
export function withCache<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  keyGenerator: (...args: Parameters<T>) => string,
  ttlMs: number = 300000 // 5 minutes default
): T {
  const monitor = PerformanceMonitor.getInstance();

  return (async (...args: Parameters<T>) => {
    const cacheKey = keyGenerator(...args);
    
    // Try cache first
    const cached = monitor.getCache(cacheKey);
    if (cached !== null) {
      console.log(`Cache hit for: ${cacheKey}`);
      return cached;
    }

    // Execute function and cache result
    const result = await fn(...args);
    monitor.setCache(cacheKey, result, ttlMs);
    console.log(`Cache miss, stored: ${cacheKey}`);
    
    return result;
  }) as T;
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Helper function to get performance report
export function getPerformanceReport(): {
  operations: Record<string, any>;
  cache: any;
  summary: {
    totalOperations: number;
    avgDuration: number;
    slowOperations: number;
  };
} {
  const monitor = PerformanceMonitor.getInstance();
  const allStats = monitor.getStats();
  const cacheStats = monitor.getCacheStats();

  // Get stats for key operations
  const operations = {
    'AI Data Extraction': monitor.getStats('AI Data Extraction'),
    'Google Sheets Export': monitor.getStats('Google Sheets Export'),
    'Database Save Operation': monitor.getStats('Database Save Operation'),
    'Generate Signed URL': monitor.getStats('Generate Signed URL')
  };

  const slowOperations = allStats.recentMetrics.filter(m => m.duration > 5000).length;

  return {
    operations,
    cache: cacheStats,
    summary: {
      totalOperations: allStats.count,
      avgDuration: allStats.avgDuration,
      slowOperations
    }
  };
}
