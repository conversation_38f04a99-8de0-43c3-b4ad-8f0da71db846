import { NextRequest, NextResponse } from 'next/server';
import { paystackClient } from '@/lib/paystack/client';
import { subscriptionManager } from '@/lib/subscription/manager';

export async function POST(request: NextRequest) {
  console.log('🔔 Paystack webhook received');

  try {
    // Get the raw body and signature
    const body = await request.text();
    const signature = request.headers.get('x-paystack-signature');

    console.log('📝 Webhook details:', {
      hasBody: !!body,
      bodyLength: body.length,
      hasSignature: !!signature,
      headers: Object.fromEntries(request.headers.entries())
    });

    if (!signature) {
      console.error('❌ Missing Paystack signature');
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }

    // For development/testing, you can temporarily skip signature verification
    const skipSignatureVerification = process.env.NODE_ENV === 'development' && process.env.SKIP_PAYSTACK_SIGNATURE === 'true';

    if (!skipSignatureVerification) {
      // Verify webhook signature
      if (!paystackClient.verifyWebhookSignature(body, signature)) {
        console.error('❌ Invalid Paystack signature');
        console.log('Expected signature verification failed');
        return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
      }
      console.log('✅ Signature verified');
    } else {
      console.log('⚠️ Skipping signature verification (development mode)');
    }

    const event = JSON.parse(body);
    console.log('📦 Paystack webhook event:', {
      event: event.event,
      reference: event.data?.reference,
      status: event.data?.status,
      amount: event.data?.amount,
      customer: event.data?.customer?.email
    });

    // Handle different event types
    switch (event.event) {
      case 'charge.success':
        console.log('💰 Processing charge success');
        await handleChargeSuccess(event.data);
        break;

      case 'charge.failed':
        console.log('❌ Processing charge failed');
        await handleChargeFailed(event.data);
        break;

      case 'subscription.create':
        console.log('📋 Processing subscription create');
        await handleSubscriptionCreate(event.data);
        break;

      case 'subscription.disable':
        console.log('🚫 Processing subscription disable');
        await handleSubscriptionDisable(event.data);
        break;

      default:
        console.log('❓ Unhandled Paystack event:', event.event);
    }

    console.log('✅ Webhook processed successfully');
    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('💥 Error processing Paystack webhook:', error);
    return NextResponse.json({
      error: 'Webhook processing failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function handleChargeSuccess(data: any) {
  try {
    const reference = data.reference;
    const metadata = data.metadata;

    // Check if this is a subscription payment
    if (metadata?.type === 'subscription_upgrade') {
      console.log('Processing subscription upgrade payment:', reference);
      
      const result = await subscriptionManager.verifyAndUpgradeSubscription(reference);
      
      if (result.success) {
        console.log('Subscription upgraded successfully via webhook:', reference);
        
        // TODO: Send confirmation email to user
        // await sendSubscriptionConfirmationEmail(result.user);
        
      } else {
        console.error('Failed to upgrade subscription via webhook:', result.error);
      }
    }

  } catch (error) {
    console.error('Error handling charge success:', error);
  }
}

async function handleChargeFailed(data: any) {
  try {
    const reference = data.reference;
    console.log('Payment failed for reference:', reference);

    // Update transaction status in database
    // The subscription manager will handle this when verification is called
    
  } catch (error) {
    console.error('Error handling charge failed:', error);
  }
}

async function handleSubscriptionCreate(data: any) {
  try {
    console.log('Subscription created:', data.subscription_code);
    // Handle subscription creation if using Paystack subscriptions
    
  } catch (error) {
    console.error('Error handling subscription create:', error);
  }
}

async function handleSubscriptionDisable(data: any) {
  try {
    console.log('Subscription disabled:', data.subscription_code);
    // Handle subscription cancellation if using Paystack subscriptions
    
  } catch (error) {
    console.error('Error handling subscription disable:', error);
  }
}

// Disable body parsing for webhooks
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
