export interface BusinessInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  email: string;
  logo: File | null;
}

export interface CustomerInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  zip: string;
}

export interface ReceiptInfo {
  number: string;
  date: string;
  paymentMethod: string;
}

export interface ReceiptItem {
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

export interface Tax {
  rate: number;
  amount: number;
}

export interface Discount {
  amount: number;
  type: 'amount' | 'percentage';
}

export interface Totals {
  subtotal: number;
  total: number;
}

export interface ReceiptData {
  business: BusinessInfo;
  customer: CustomerInfo;
  receipt: ReceiptInfo;
  items: ReceiptItem[];
  tax: Tax;
  discount: Discount;
  notes: string;
  totals: Totals;
  currency: string;
}

export const PAYMENT_METHODS = [
  'Cash',
  'M-Pesa',
  'Credit Card',
  'Debit Card',
  'Check',
  'Bank Transfer',
  'PayPal',
  'Venmo',
  'Apple Pay',
  'Google Pay',
  'Other'
];

export const CURRENCIES = [
  { code: 'USD', symbol: '$', name: 'US Dollar' },
  { code: 'KES', symbol: 'KSh', name: 'Kenyan Shilling' },
  { code: 'EUR', symbol: '€', name: 'Euro' },
  { code: 'GBP', symbol: '£', name: 'British Pound' },
  { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
];
