import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft, Calendar, Clock, User } from 'lucide-react'

export const metadata: Metadata = {
  title: "Blog - Small Business Tips & Receipt Management | ReceiptLabs",
  description: "Expert tips and insights for small business owners on expense management, receipt processing, tax preparation, and financial organization.",
  keywords: "small business blog, expense management tips, receipt processing, tax preparation, financial organization, business accounting",
}

export default function BlogPage() {
  // Placeholder blog posts - in a real implementation, these would come from a CMS or database
  const blogPosts = [
    {
      id: 1,
      title: "5 Essential Expense Management Tips for Small Business Owners",
      excerpt: "Learn how to streamline your expense tracking and save hours every month with these proven strategies.",
      author: "ReceiptLabs Team",
      date: "2025-01-15",
      readTime: "5 min read",
      category: "Expense Management",
      slug: "expense-management-tips-small-business"
    },
    {
      id: 2,
      title: "How to Prepare for Tax Season: A Small Business Guide",
      excerpt: "Get your business ready for tax season with our comprehensive checklist and organization tips.",
      author: "ReceiptLabs Team", 
      date: "2025-01-10",
      readTime: "8 min read",
      category: "Tax Preparation",
      slug: "tax-season-preparation-guide"
    },
    {
      id: 3,
      title: "The Complete Guide to Receipt Organization for Small Businesses",
      excerpt: "Discover the best practices for organizing and storing business receipts to ensure compliance and easy retrieval.",
      author: "ReceiptLabs Team",
      date: "2025-01-05",
      readTime: "6 min read", 
      category: "Organization",
      slug: "receipt-organization-guide"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-4 sm:py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/">
              <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link
              href="/"
              className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </Link>
            <Link
              href="/login"
              className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
            >
              Sign In
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative z-10 px-4 py-12 sm:py-20">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-3xl sm:text-5xl md:text-6xl font-bold text-white mb-4 sm:mb-6">
            Small Business 
            <span className="bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
              {' '}Insights & Tips
            </span>
          </h1>
          <p className="text-lg sm:text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Expert advice on expense management, receipt processing, tax preparation, and financial organization for small business owners.
          </p>
        </div>
      </section>

      {/* Blog Posts */}
      <section className="relative z-10 px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-8">
            {blogPosts.map((post) => (
              <article key={post.id} className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-6 sm:p-8 hover:border-gray-600 transition-colors duration-300">
                <div className="flex flex-wrap items-center gap-4 mb-4 text-sm text-gray-400">
                  <span className="bg-pink-500/20 text-pink-300 px-3 py-1 rounded-full">
                    {post.category}
                  </span>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    {new Date(post.date).toLocaleDateString('en-US', { 
                      year: 'numeric', 
                      month: 'long', 
                      day: 'numeric' 
                    })}
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    {post.readTime}
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    {post.author}
                  </div>
                </div>
                
                <h2 className="text-xl sm:text-2xl font-bold text-white mb-3 hover:text-pink-400 transition-colors">
                  <Link href={`/blog/${post.slug}`}>
                    {post.title}
                  </Link>
                </h2>
                
                <p className="text-gray-400 mb-4 leading-relaxed">
                  {post.excerpt}
                </p>
                
                <Link 
                  href={`/blog/${post.slug}`}
                  className="inline-flex items-center gap-2 text-pink-400 hover:text-pink-300 font-medium transition-colors"
                >
                  Read More
                  <ArrowLeft className="w-4 h-4 rotate-180" />
                </Link>
              </article>
            ))}
          </div>

          {/* Coming Soon Notice */}
          <div className="mt-12 bg-gradient-to-r from-blue-500/10 to-purple-600/10 backdrop-blur-md border border-blue-500/20 rounded-2xl p-6 sm:p-8 text-center">
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-4">
              More Content Coming Soon!
            </h3>
            <p className="text-gray-300 mb-6">
              We're working on bringing you more valuable insights and tips for small business success. 
              In the meantime, check out our expense management solution.
            </p>
            <Link
              href="/expense-management-small-business"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200"
            >
              Explore Expense Management
              <ArrowLeft className="w-4 h-4 rotate-180" />
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="relative z-10 px-4 py-12 border-t border-gray-800 mt-20">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-gray-400 text-sm">
            © 2025 <strong>Receipt</strong>Labs. All rights reserved.
          </p>
          <div className="flex justify-center space-x-6 mt-4">
            <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors text-sm">
              Privacy Policy
            </Link>
            <Link href="/terms" className="text-gray-400 hover:text-white transition-colors text-sm">
              Terms of Service
            </Link>
            <Link href="/contact" className="text-gray-400 hover:text-white transition-colors text-sm">
              Contact Us
            </Link>
          </div>
        </div>
      </footer>
    </div>
  )
}