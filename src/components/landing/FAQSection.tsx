'use client'

import { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { FAQ } from '@/lib/types/landing-page'

interface FAQSectionProps {
  faqs: FAQ[];
}

export default function FAQSection({ faqs }: FAQSectionProps) {
  const [openFAQ, setOpenFAQ] = useState<string | null>(null)

  const toggleFAQ = (id: string) => {
    setOpenFAQ(openFAQ === id ? null : id)
  }

  // Generate structured data for FAQ schema
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  }

  return (
    <section className="relative z-10 px-4 py-12 sm:py-20">
      {/* Structured Data for FAQ */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqStructuredData)
        }}
      />
      
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6 px-2">
            Frequently Asked Questions
          </h2>
          <p className="text-base sm:text-xl text-gray-300 px-2">
            Everything small business owners need to know about ReceiptLabs
          </p>
        </div>

        <div className="space-y-4 sm:space-y-6">
          {faqs.map((faq) => (
            <div 
              key={faq.id} 
              className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl sm:rounded-2xl overflow-hidden hover:border-gray-600 transition-colors duration-300"
            >
              <button
                onClick={() => toggleFAQ(faq.id)}
                className="w-full text-left p-6 sm:p-8 flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-pink-500/50 focus:ring-inset"
                aria-expanded={openFAQ === faq.id}
              >
                <h3 className="text-lg sm:text-xl font-bold text-white pr-4">
                  {faq.question}
                </h3>
                <div className="flex-shrink-0">
                  {openFAQ === faq.id ? (
                    <ChevronUp className="w-5 sm:w-6 h-5 sm:h-6 text-gray-400" />
                  ) : (
                    <ChevronDown className="w-5 sm:w-6 h-5 sm:h-6 text-gray-400" />
                  )}
                </div>
              </button>
              
              {openFAQ === faq.id && (
                <div className="px-6 sm:px-8 pb-6 sm:pb-8">
                  <div className="border-t border-gray-700 pt-4 sm:pt-6">
                    <p className="text-gray-400 text-sm sm:text-base leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Additional Help Section */}
        <div className="mt-12 sm:mt-16 bg-gradient-to-r from-blue-500/10 to-purple-600/10 backdrop-blur-md border border-blue-500/20 rounded-2xl p-6 sm:p-8 text-center">
          <h3 className="text-xl sm:text-2xl font-bold text-white mb-4">
            Still Have Questions?
          </h3>
          <p className="text-gray-300 text-sm sm:text-base mb-6 max-w-2xl mx-auto">
            Our small business support team is here to help you get started with ReceiptLabs. 
            Get personalized assistance for your specific business needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center">
            <a
              href="/contact"
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 sm:py-3 px-4 sm:px-6 rounded-lg transition-colors duration-200 text-sm sm:text-base"
            >
              Contact Support
            </a>
            <a
              href="#how-it-works"
              className="border border-gray-600 hover:border-gray-500 text-white font-medium py-2 sm:py-3 px-4 sm:px-6 rounded-lg transition-colors duration-200 text-sm sm:text-base"
            >
              Watch Demo
            </a>
          </div>
        </div>

        {/* Small Business Resources */}
        <div className="mt-8 sm:mt-12 grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
          <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-4 sm:p-6 text-center">
            <h4 className="text-white font-semibold mb-2 text-sm sm:text-base">Setup Guide</h4>
            <p className="text-gray-400 text-xs sm:text-sm mb-3">Step-by-step setup for small businesses</p>
            <a href="#" className="text-pink-400 hover:text-pink-300 text-xs sm:text-sm font-medium">
              View Guide →
            </a>
          </div>
          
          <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-4 sm:p-6 text-center">
            <h4 className="text-white font-semibold mb-2 text-sm sm:text-base">Best Practices</h4>
            <p className="text-gray-400 text-xs sm:text-sm mb-3">Tips for small business expense tracking</p>
            <a href="#" className="text-pink-400 hover:text-pink-300 text-xs sm:text-sm font-medium">
              Learn More →
            </a>
          </div>
          
          <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg p-4 sm:p-6 text-center">
            <h4 className="text-white font-semibold mb-2 text-sm sm:text-base">Tax Preparation</h4>
            <p className="text-gray-400 text-xs sm:text-sm mb-3">How to prepare for tax season</p>
            <a href="#" className="text-pink-400 hover:text-pink-300 text-xs sm:text-sm font-medium">
              Read Tips →
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}