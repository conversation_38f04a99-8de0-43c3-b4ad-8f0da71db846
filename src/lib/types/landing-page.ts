export interface CTAButton {
  text: string;
  href: string;
  variant: 'primary' | 'secondary';
}

export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
  benefits: string[];
  isNew?: boolean;
  planRequirement?: 'free' | 'professional' | 'business';
}

export interface Benefit {
  id: string;
  title: string;
  description: string;
  icon: string;
}

export interface Testimonial {
  id: string;
  quote: string;
  author: {
    name: string;
    title: string;
    company: string;
    avatar?: string;
  };
  rating: number;
  context: 'small-business' | 'accounting' | 'general';
}

export interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: 'accuracy' | 'integration' | 'pricing' | 'security' | 'general';
}

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  period: string;
  features: string[];
  isPopular?: boolean;
  ctaText: string;
  ctaHref: string;
}

export interface ExpenseManagementPageContent {
  meta: {
    title: string;
    description: string;
    keywords: string[];
    canonicalUrl: string;
  };
  hero: {
    headline: string;
    subheadline: string;
    trustIndicators: string[];
    primaryCTA: CTAButton;
    secondaryCTA: CTAButton;
  };
  features: Feature[];
  benefits: Benefit[];
  testimonials: Testimonial[];
  pricing: PricingPlan[];
  faqs: FAQ[];
}