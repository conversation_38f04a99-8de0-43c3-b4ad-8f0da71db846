import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    // Get all receipts for the user with detailed information
    const { data: receipts, error } = await supabase
      .from('receipts')
      .select(`
        id,
        user_id,
        original_file_name,
        file_path,
        file_size,
        mime_type,
        vendor,
        vendor_tax_id,
        receipt_date,
        currency,
        payment_method,
        subtotal,
        tax_rate_percent,
        tax_amount,
        total_amount,
        paid_amount,
        processing_status,
        confidence_score,
        extraction_method,
        error_message,
        google_sheet_row_number,
        redis_job_id,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching receipts:', error);
      return NextResponse.json({ 
        success: false, 
        error: error.message 
      }, { status: 500 });
    }

    // Calculate summary statistics
    const total = receipts?.length || 0;
    const processed = receipts?.filter(r => r.processing_status === 'completed').length || 0;
    const pending = receipts?.filter(r => ['pending', 'processing', 'queued'].includes(r.processing_status)).length || 0;
    const failed = receipts?.filter(r => r.processing_status === 'failed').length || 0;

    // Calculate status breakdown
    const statusBreakdown = receipts?.reduce((acc, receipt) => {
      acc[receipt.processing_status] = (acc[receipt.processing_status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    // Calculate total value
    const totalValue = receipts?.reduce((sum, receipt) => {
      return sum + (receipt.total_amount || 0);
    }, 0) || 0;

    return NextResponse.json({
      success: true,
      receipts: receipts || [],
      count: total,
      stats: {
        total,
        processed,
        pending,
        failed
      },
      statusBreakdown,
      totalValue,
      user_id: user.id
    });

  } catch (error) {
    console.error('Detailed receipts API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
