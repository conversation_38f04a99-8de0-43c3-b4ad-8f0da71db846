-- Add Google Drive integration support to users table
-- This migration adds separate Google Drive authentication and configuration

-- Add Google Drive specific columns to users table
ALTER TABLE users 
ADD COLUMN google_drive_connected BOOLEAN DEFAULT FALSE,
ADD COLUMN google_drive_email VARCHAR(255),
ADD COLUMN google_drive_access_token TEXT,
ADD COLUMN google_drive_refresh_token TEXT,
ADD COLUMN google_drive_folder_id VARCHAR(255),
ADD COLUMN google_drive_folder_name VARCHAR(255) DEFAULT 'RecoAI Receipts';

-- Add index for faster lookups
CREATE INDEX idx_users_google_drive_connected ON users(google_drive_connected);
CREATE INDEX idx_users_google_drive_email ON users(google_drive_email);

-- Update the updated_at timestamp when Google Drive fields change
CREATE OR REPLACE FUNCTION update_users_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON> trigger if it doesn't exist
DROP TRIGGER IF EXISTS update_users_updated_at_trigger ON users;
CREATE TRIGGER update_users_updated_at_trigger
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_users_updated_at();

-- Add comment explaining the new columns
COMMENT ON COLUMN users.google_drive_connected IS 'Whether user has connected Google Drive (separate from Sheets)';
COMMENT ON COLUMN users.google_drive_email IS 'Email of the Google account used for Drive (may differ from login email)';
COMMENT ON COLUMN users.google_drive_access_token IS 'Google Drive API access token (separate from Sheets token)';
COMMENT ON COLUMN users.google_drive_refresh_token IS 'Google Drive API refresh token (separate from Sheets token)';
COMMENT ON COLUMN users.google_drive_folder_id IS 'ID of the RecoAI folder in user\'s Google Drive';
COMMENT ON COLUMN users.google_drive_folder_name IS 'Name of the RecoAI folder in user\'s Google Drive';
