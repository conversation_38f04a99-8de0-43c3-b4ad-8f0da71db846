// Redis key patterns
export const QUEUES = {
  HIGH_PRIORITY: 'queue:receipts:high',
  NORMAL_PRIORITY: 'queue:receipts:normal',
  LOW_PRIORITY: 'queue:receipts:low',
  PROCESSING: 'queue:receipts:processing',
  FAILED: 'queue:receipts:failed'
};

// Job data structure
export interface QueueJob {
  id: string;
  receiptId: string;
  userId: string;
  priority: 'high' | 'normal' | 'low';
  attempts: number;
  maxAttempts: number;
  createdAt: string;
  processingStartedAt?: string;
  data: {
    imageUrl: string;
    fileName: string;
    fileSize: number;
    mimeType?: string;
  };
}

// Extracted receipt data structure
export interface ExtractedReceiptData {
  vendor: string;
  vendor_tax_id?: string | null;
  date: string;
  currency?: string;
  payment_method?: string;
  items?: Array<{
    description: string;
    total: number;
    category?: string;
    quantity?: number;
    unit_price?: number;
  }>;
  subtotal?: number;
  tax_rate_percent?: number;
  tax_amount?: number;
  total_amount: number;
  paid_amount?: number;
} 