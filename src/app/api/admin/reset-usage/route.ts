import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Get request body
    const body = await request.json()
    const { userId, resetType = 'current_user' } = body

    let targetUserId = user.id
    if (resetType === 'specific_user' && userId) {
      targetUserId = userId
    }

    console.log(`Resetting usage for user: ${targetUserId}`)

    // Reset the user's receipt usage counter
    const { error: resetError } = await supabase
      .from('users')
      .update({ 
        receipts_used_this_period: 0,
        current_period_start: new Date().toISOString(),
        current_period_end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
      })
      .eq('id', targetUserId)

    if (resetError) {
      console.error('Failed to reset usage:', resetError)
      return NextResponse.json(
        { success: false, error: `Failed to reset usage: ${resetError.message}` },
        { status: 500 }
      )
    }

    // Get updated user data
    const { data: updatedUser, error: fetchError } = await supabase
      .from('users')
      .select('current_tier, monthly_receipt_limit, receipts_used_this_period, current_period_start, current_period_end')
      .eq('id', targetUserId)
      .single()

    if (fetchError) {
      console.error('Failed to fetch updated user data:', fetchError)
      return NextResponse.json(
        { success: false, error: `Failed to fetch updated data: ${fetchError.message}` },
        { status: 500 }
      )
    }

    console.log('Usage reset successfully:', updatedUser)

    return NextResponse.json({
      success: true,
      message: 'Usage counter reset successfully',
      data: {
        userId: targetUserId,
        tier: updatedUser.current_tier,
        monthlyLimit: updatedUser.monthly_receipt_limit,
        receiptsUsed: updatedUser.receipts_used_this_period,
        periodStart: updatedUser.current_period_start,
        periodEnd: updatedUser.current_period_end
      }
    })

  } catch (error) {
    console.error('Error resetting usage:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}
