export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          google_access_token: string | null
          google_refresh_token: string | null
          google_sheets_connected?: boolean
          current_tier: string
          receipts_processed: number
          subscription_status: string
          subscription_end_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          google_access_token?: string | null
          google_refresh_token?: string | null
          google_sheets_connected?: boolean
          current_tier?: string
          receipts_processed?: number
          subscription_status?: string
          subscription_end_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          google_access_token?: string | null
          google_refresh_token?: string | null
          google_sheets_connected?: boolean
          current_tier?: string
          receipts_processed?: number
          subscription_status?: string
          subscription_end_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      receipts: {
        Row: {
          id: string
          user_id: string
          original_file_name: string
          file_path: string
          file_size: number | null
          mime_type: string | null
          vendor: string | null
          vendor_tax_id: string | null
          receipt_date: string | null
          currency: string
          payment_method: string | null
          subtotal: number | null
          tax_rate_percent: number | null
          tax_amount: number | null
          total_amount: number | null
          paid_amount: number | null
          processing_status: string
          confidence_score: number | null
          extraction_method: string | null
          error_message: string | null
          google_sheet_row_number: number | null
          redis_job_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          original_file_name: string
          file_path: string
          file_size?: number | null
          mime_type?: string | null
          vendor?: string | null
          vendor_tax_id?: string | null
          receipt_date?: string | null
          currency?: string
          payment_method?: string | null
          subtotal?: number | null
          tax_rate_percent?: number | null
          tax_amount?: number | null
          total_amount?: number | null
          paid_amount?: number | null
          processing_status?: string
          confidence_score?: number | null
          extraction_method?: string | null
          error_message?: string | null
          google_sheet_row_number?: number | null
          redis_job_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          original_file_name?: string
          file_path?: string
          file_size?: number | null
          mime_type?: string | null
          vendor?: string | null
          vendor_tax_id?: string | null
          receipt_date?: string | null
          currency?: string
          payment_method?: string | null
          subtotal?: number | null
          tax_rate_percent?: number | null
          tax_amount?: number | null
          total_amount?: number | null
          paid_amount?: number | null
          processing_status?: string
          confidence_score?: number | null
          extraction_method?: string | null
          error_message?: string | null
          google_sheet_row_number?: number | null
          redis_job_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      receipt_items: {
        Row: {
          id: string
          receipt_id: string
          description: string
          quantity: number
          unit_price: number | null
          total_price: number
          category: string | null
          created_at: string
        }
        Insert: {
          id?: string
          receipt_id: string
          description: string
          quantity?: number
          unit_price?: number | null
          total_price: number
          category?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          receipt_id?: string
          description?: string
          quantity?: number
          unit_price?: number | null
          total_price?: number
          category?: string | null
          created_at?: string
        }
      }
      google_sheets: {
        Row: {
          id: string
          user_id: string
          year: number
          sheet_id: string
          sheet_url: string
          sheet_name: string
          last_row_number: number
          total_receipts: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          year: number
          sheet_id: string
          sheet_url: string
          sheet_name: string
          last_row_number?: number
          total_receipts?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          year?: number
          sheet_id?: string
          sheet_url?: string
          sheet_name?: string
          last_row_number?: number
          total_receipts?: number
          created_at?: string
          updated_at?: string
        }
      }
      payment_transactions: {
        Row: {
          id: string
          user_id: string
          paystack_reference: string
          amount: number
          currency: string
          status: string
          tier: string
          receipts_allowance: number
          paystack_response: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          paystack_reference: string
          amount: number
          currency?: string
          status: string
          tier: string
          receipts_allowance: number
          paystack_response?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          paystack_reference?: string
          amount?: number
          currency?: string
          status?: string
          tier?: string
          receipts_allowance?: number
          paystack_response?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      user_analytics: {
        Row: {
          id: string
          user_id: string
          month_year: string
          total_receipts: number
          total_spent: number
          top_vendor: string | null
          top_category: string | null
          average_receipt_amount: number
          category_breakdown: Json | null
          vendor_breakdown: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          month_year: string
          total_receipts?: number
          total_spent?: number
          top_vendor?: string | null
          top_category?: string | null
          average_receipt_amount?: number
          category_breakdown?: Json | null
          vendor_breakdown?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          month_year?: string
          total_receipts?: number
          total_spent?: number
          top_vendor?: string | null
          top_category?: string | null
          average_receipt_amount?: number
          category_breakdown?: Json | null
          vendor_breakdown?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
} 