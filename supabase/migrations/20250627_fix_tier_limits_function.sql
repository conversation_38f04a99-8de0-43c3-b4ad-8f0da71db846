-- Fix the check_tier_limits function to resolve column ambiguity
-- Date: 2025-06-27

CREATE OR REPLACE FUNCTION check_tier_limits(user_id_param UUID)
RETURNS TABLE(
  can_process_receipt BOOLEAN,
  receipts_remaining INTEGER,
  has_analytics_access BOOLEAN,
  has_google_drive_access BOOLEAN,
  data_retention_months INTEGER
) AS $$
DECLARE
  user_tier TEXT;
  user_limit INTEGER;
  user_used INTEGER;
  user_status TEXT;
  user_retention INTEGER;
  remaining_receipts INTEGER;
  can_process BOOLEAN;
  has_analytics BOOLEAN;
  has_drive BOOLEAN;
BEGIN
  -- Get user data
  SELECT 
    current_tier,
    monthly_receipt_limit,
    COALESCE(receipts_used_this_period, 0),
    subscription_status,
    COALESCE(data_retention_months, 1)
  INTO user_tier, user_limit, user_used, user_status, user_retention
  FROM users 
  WHERE id = user_id_param;

  -- If user not found, return default values
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 0, false, false, 1;
    RETURN;
  END IF;

  -- Calculate remaining receipts
  remaining_receipts := GREATEST(0, user_limit - user_used);
  
  -- Check if user can process receipts
  can_process := remaining_receipts > 0 AND user_status IN ('active', 'inactive');
  
  -- Check tier-based features
  has_analytics := user_tier IN ('professional', 'business');
  has_drive := user_tier = 'business';

  -- Return results
  RETURN QUERY SELECT 
    can_process,
    remaining_receipts,
    has_analytics,
    has_drive,
    user_retention;
END;
$$ LANGUAGE plpgsql;
