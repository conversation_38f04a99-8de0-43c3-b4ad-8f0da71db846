'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { JobStatusUpdate, UserNotification } from '@/lib/pubsub'

export type NotificationMessage = JobStatusUpdate | UserNotification

interface UseNotificationsOptions {
  enableSSE?: boolean
  enablePolling?: boolean
  pollingInterval?: number
}

interface NotificationState {
  notifications: NotificationMessage[]
  isConnected: boolean
  lastUpdate: Date | null
  error: string | null
}

export function useNotifications(options: UseNotificationsOptions = {}) {
  const {
    enableSSE = true,
    enablePolling = true,
    pollingInterval = 15000 // 15 seconds for polling fallback (reduced frequency)
  } = options

  const [state, setState] = useState<NotificationState>({
    notifications: [],
    isConnected: false,
    lastUpdate: null,
    error: null
  })

  const eventSourceRef = useRef<EventSource | null>(null)
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const mountedRef = useRef(true)

  // Add notification to state
  const addNotification = useCallback((notification: NotificationMessage) => {
    if (!mountedRef.current) return

    setState(prev => ({
      ...prev,
      notifications: [notification, ...prev.notifications.slice(0, 49)], // Keep last 50
      lastUpdate: new Date(),
      error: null
    }))
  }, [])

  // Clear notifications
  const clearNotifications = useCallback(() => {
    setState(prev => ({
      ...prev,
      notifications: []
    }))
  }, [])

  // Remove specific notification
  const removeNotification = useCallback((index: number) => {
    setState(prev => ({
      ...prev,
      notifications: prev.notifications.filter((_, i) => i !== index)
    }))
  }, [])

  // Setup Server-Sent Events
  const setupSSE = useCallback(() => {
    if (!enableSSE || eventSourceRef.current) return

    console.log('🔌 Setting up SSE connection...')

    try {
      const eventSource = new EventSource('/api/notifications/stream')
      eventSourceRef.current = eventSource

      eventSource.onopen = () => {
        console.log('✅ SSE connection opened')
        setState(prev => ({ ...prev, isConnected: true, error: null }))
      }

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          if (data.type === 'notification') {
            console.log('📨 Received SSE notification:', data.data)
            addNotification(data.data)
          } else if (data.type === 'heartbeat') {
            console.log('💓 SSE heartbeat received')
          } else if (data.type === 'connection') {
            console.log('🔗 SSE connection established:', data.message)
          }
        } catch (error) {
          console.error('❌ Failed to parse SSE message:', error)
        }
      }

      eventSource.onerror = (error) => {
        console.error('❌ SSE error:', error)
        setState(prev => ({ 
          ...prev, 
          isConnected: false, 
          error: 'Connection error' 
        }))
        
        // Cleanup and retry after delay
        eventSource.close()
        eventSourceRef.current = null
        
        setTimeout(() => {
          if (mountedRef.current) {
            setupSSE()
          }
        }, 5000)
      }

    } catch (error) {
      console.error('❌ Failed to setup SSE:', error)
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to setup real-time connection' 
      }))
    }
  }, [enableSSE, addNotification])

  // Setup polling fallback
  const setupPolling = useCallback(() => {
    if (!enablePolling || pollingIntervalRef.current) return

    console.log('🔄 Setting up notification polling...')

    const poll = async () => {
      try {
        const response = await fetch('/api/notifications/poll')
        
        if (response.ok) {
          const data = await response.json()
          
          if (data.success && data.notifications.length > 0) {
            console.log('📨 Received polled notifications:', data.notifications.length)
            
            data.notifications.forEach((notification: NotificationMessage) => {
              addNotification(notification)
            })
          }
        } else {
          console.error('❌ Polling failed:', response.status)
        }
      } catch (error) {
        console.error('❌ Polling error:', error)
      }
    }

    // Initial poll
    poll()

    // Setup interval
    pollingIntervalRef.current = setInterval(poll, pollingInterval)
  }, [enablePolling, pollingInterval, addNotification])

  // Cleanup function
  const cleanup = useCallback(() => {
    console.log('🧹 Cleaning up notifications...')

    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }

    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current)
      pollingIntervalRef.current = null
    }

    setState(prev => ({ ...prev, isConnected: false }))
  }, [])

  // Setup connections on mount
  useEffect(() => {
    mountedRef.current = true

    // Try SSE first, fallback to polling
    if (enableSSE) {
      setupSSE()
    } else if (enablePolling) {
      setupPolling()
    }

    return () => {
      mountedRef.current = false
      cleanup()
    }
  }, [enableSSE, enablePolling, setupSSE, setupPolling, cleanup])

  // Reconnect function
  const reconnect = useCallback(() => {
    cleanup()
    
    setTimeout(() => {
      if (mountedRef.current) {
        if (enableSSE) {
          setupSSE()
        } else if (enablePolling) {
          setupPolling()
        }
      }
    }, 1000)
  }, [cleanup, enableSSE, enablePolling, setupSSE, setupPolling])

  return {
    notifications: state.notifications,
    isConnected: state.isConnected,
    lastUpdate: state.lastUpdate,
    error: state.error,
    clearNotifications,
    removeNotification,
    reconnect
  }
}
