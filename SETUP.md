# ReceiptLabs Setup Guide

This guide will walk you through setting up the ReceiptLabs application with Supabase and Google OAuth authentication.

## Prerequisites

- Node.js 18+ installed
- A Google account for Google Developer Console
- A Supabase account

## 1. Supabase Project Setup

### Step 1: Create a New Supabase Project

1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Click "New Project"
3. Choose your organization
4. Fill in project details:
   - **Name**: `ReceiptLabs` (or your preferred name)
   - **Database Password**: Generate a strong password and save it securely
   - **Region**: Choose the region closest to your users
5. Click "Create new project"
6. Wait for the project to be created (this may take a few minutes)

### Step 2: Run Database Migrations

1. In your Supabase dashboard, go to the **SQL Editor**
2. Copy the contents of `supabase/migrations/20250103000001_initial_schema.sql`
3. Paste it into the SQL Editor and click "Run"
4. Verify that all tables were created successfully

### Step 3: Get Supabase Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy the following values:
   - **Project URL** (starts with `https://`)
   - **Project API keys** → **anon public** key
   - **Project API keys** → **service_role** key (keep this secret!)

## 2. Google OAuth Setup

### Step 1: Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click "Select a project" → "New Project"
3. Enter project name: `ReceiptLabs` (or your preferred name)
4. Click "Create"

### Step 2: Enable Google+ API

1. In the Google Cloud Console, go to **APIs & Services** → **Library**
2. Search for "Google+ API"
3. Click on it and click "Enable"

### Step 3: Configure OAuth Consent Screen

1. Go to **APIs & Services** → **OAuth consent screen**
2. Choose **External** user type (unless you have a Google Workspace account)
3. Click "Create"
4. Fill in the required information:
   - **App name**: `Reco Engine`
   - **User support email**: Your email address
   - **Developer contact information**: Your email address
5. Click "Save and Continue"
6. On the Scopes page, click "Save and Continue" (we'll use default scopes)
7. On the Test users page, add your email address for testing
8. Click "Save and Continue"

### Step 4: Create OAuth Credentials

1. Go to **APIs & Services** → **Credentials**
2. Click "Create Credentials" → "OAuth client ID"
3. Choose **Web application**
4. Enter name: `RecoAI Web Client`
5. Add Authorized redirect URIs:
   - `https://YOUR_SUPABASE_PROJECT_REF.supabase.co/auth/v1/callback`
   - Replace `YOUR_SUPABASE_PROJECT_REF` with your actual Supabase project reference
6. Click "Create"
7. Copy the **Client ID** and **Client Secret**

## 3. Configure Supabase Authentication

### Step 1: Enable Google OAuth in Supabase

1. In your Supabase dashboard, go to **Authentication** → **Providers**
2. Find **Google** and click the toggle to enable it
3. Enter your Google OAuth credentials:
   - **Client ID**: From Google Cloud Console
   - **Client Secret**: From Google Cloud Console
4. Click "Save"

### Step 2: Configure Additional Settings

1. In **Authentication** → **Settings**
2. Set **Site URL** to your application URL:
   - For development: `http://localhost:3000`
   - For production: Your deployed app URL
3. Add redirect URLs:
   - `http://localhost:3000/auth/callback` (for development)
   - `https://yourdomain.com/auth/callback` (for production)

## 4. Environment Variables Setup

### Step 1: Create Environment File

1. Copy `env.example` to `.env.local`:
   ```bash
   cp env.example .env.local
   ```

2. Fill in your environment variables in `.env.local`:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=https://YOUR_PROJECT_REF.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # Google OAuth Configuration (Optional - handled by Supabase)
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret

   # OpenAI Configuration (for future AI features)
   OPENAI_API_KEY=your_openai_api_key

   # Upstash Redis Configuration (for future queue features)
   UPSTASH_REDIS_REST_URL=your_upstash_redis_url
   UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token

   # PayStack Configuration (for future payment features)
   PAYSTACK_SECRET_KEY=your_paystack_secret_key
   PAYSTACK_PUBLIC_KEY=your_paystack_public_key

   # Application Configuration
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ```

## 5. Running the Application

### Step 1: Install Dependencies

```bash
npm install
```

### Step 2: Start Development Server

```bash
npm run dev
```

### Step 3: Test Authentication

1. Open your browser to `http://localhost:3000`
2. Click "Sign In" or "Get Started Free"
3. Click "Continue with Google"
4. Complete the Google OAuth flow
5. You should be redirected to the dashboard

## 6. Verification Checklist

- [ ] Supabase project created and database schema applied
- [ ] Google Cloud project created with OAuth credentials
- [ ] Google OAuth consent screen configured
- [ ] Supabase authentication provider enabled with Google credentials
- [ ] Environment variables configured
- [ ] Application runs without errors
- [ ] Google OAuth login flow works end-to-end
- [ ] User is redirected to dashboard after successful login
- [ ] User can sign out successfully

## Troubleshooting

### Common Issues

1. **"Invalid redirect URI" error**
   - Ensure the redirect URI in Google Cloud Console matches exactly: `https://YOUR_PROJECT_REF.supabase.co/auth/v1/callback`
   - Check that there are no trailing slashes or extra characters

2. **"OAuth consent screen not configured" error**
   - Complete the OAuth consent screen setup in Google Cloud Console
   - Add your email as a test user if using external user type

3. **Environment variables not loading**
   - Ensure `.env.local` file is in the project root
   - Restart the development server after changing environment variables
   - Check that variable names match exactly (including `NEXT_PUBLIC_` prefix where needed)

4. **Database connection issues**
   - Verify Supabase URL and keys are correct
   - Ensure database migrations were applied successfully
   - Check Supabase project status in the dashboard

### Getting Help

If you encounter issues:
1. Check the browser console for error messages
2. Check the Supabase dashboard logs
3. Verify all configuration steps were completed
4. Ensure all environment variables are set correctly

## Next Steps

Once authentication is working, you can proceed with implementing the core receipt processing features:
- File upload functionality
- AI-powered receipt extraction
- Google Sheets integration
- Payment processing
- Analytics dashboard

## 7. Upstash Redis Setup

1.  **Create an Upstash Account**: If you don't have one, sign up at [Upstash](https://upstash.com/).
2.  **Create a Redis Database**:
    *   From your Upstash Console, click **Redis** > **Create Database**.
    *   **Database Name**: `receiptlabs-queue` (or a name of your choice).
    *   **Type**: Select **Global** for the lowest latency from all Supabase Edge Function regions.
    *   **TLS (SSL)**: Enabled (default).
    *   Click **Create**.
3.  **Get Credentials**:
    *   After the database is created, you'll land on its details page.
    *   Under the **REST API** section, click the **.env** button.
    *   Copy the `UPSTASH_REDIS_REST_URL` and `UPSTASH_REDIS_REST_TOKEN`.
4.  **Update Environment Variables**:
    *   Add the copied credentials to your `.env.local` file.
        ```env
        # ... other variables
        UPSTASH_REDIS_REST_URL="YOUR_URL_HERE"
        UPSTASH_REDIS_REST_TOKEN="YOUR_TOKEN_HERE"
        ```
5.  **Verify Connection (Optional but Recommended)**:
    *   You can use a tool like `curl` to test the connection from your terminal:
        ```bash
        curl -H "Authorization: Bearer YOUR_UPSTASH_REDIS_REST_TOKEN" YOUR_UPSTASH_REDIS_REST_URL/info
        ```
    *   You should receive a JSON response with information about your Redis instance.

## 8. PayStack Setup

1.  **Create a PayStack Account**: If you don't have one, sign up at [PayStack](https://paystack.com/).
2.  **Complete Business Profile**:
    *   Fill out your business details to activate your account. You might need to provide company registration documents depending on your country.
    *   You can start with a "Test Mode" account to get API keys for development.
3.  **Get API Keys**:
    *   In your PayStack Dashboard, go to **Settings** → **API Keys & Webhooks**.
    *   Copy your **Test Secret Key** and **Test Public Key**.
4.  **Update Environment Variables**:
    *   Add the copied keys to your `.env.local` file.
        ```env
        # ... other variables
        PAYSTACK_SECRET_KEY=your_test_secret_key
        PAYSTACK_PUBLIC_KEY=your_test_public_key
        ```
5.  **Configure Webhooks**:
    *   In the **API Keys & Webhooks** settings, add a webhook URL.
    *   The URL will point to the Edge Function you will create. For local development, you can use a tool like `ngrok` to expose your local server. For production, it will be `https://YOUR_APP_DOMAIN/api/paystack/webhook`.
    *   You will need to handle events like `charge.success` to update user subscriptions.