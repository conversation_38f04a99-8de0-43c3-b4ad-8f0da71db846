import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface DataRetentionResult {
  success: boolean;
  deletedReceipts: number;
  deletedItems: number;
  deletedFiles: number;
  processedUsers: number;
  errors: string[];
  executionTime: number;
}

interface TierRetentionPolicy {
  tier: string;
  retentionMonths: number;
  description: string;
}

const TIER_RETENTION_POLICIES: TierRetentionPolicy[] = [
  { tier: 'free', retentionMonths: 1, description: '30 days retention' },
  { tier: 'professional', retentionMonths: 6, description: '6 months retention' },
  { tier: 'business', retentionMonths: 12, description: '1 year retention' }
];

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const startTime = Date.now();
    
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('🧹 Starting data retention cleanup process...')

    const result: DataRetentionResult = {
      success: true,
      deletedReceipts: 0,
      deletedItems: 0,
      deletedFiles: 0,
      processedUsers: 0,
      errors: [],
      executionTime: 0
    };

    // Get all users with their current tiers and retention settings
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, current_tier, data_retention_months, created_at')
      .order('created_at', { ascending: true });

    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`);
    }

    if (!users || users.length === 0) {
      console.log('No users found for data retention cleanup');
      return new Response(JSON.stringify({
        ...result,
        executionTime: Date.now() - startTime
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    console.log(`📊 Processing ${users.length} users for data retention cleanup`);

    // Process each user
    for (const user of users) {
      try {
        result.processedUsers++;
        
        // Determine retention period for this user
        const retentionMonths = user.data_retention_months || getDefaultRetentionMonths(user.current_tier);
        
        // Skip if unlimited retention (business tier default)
        if (retentionMonths === -1) {
          console.log(`⏭️ Skipping user ${user.email} - unlimited retention`);
          continue;
        }

        // Calculate cutoff date
        const cutoffDate = new Date();
        cutoffDate.setMonth(cutoffDate.getMonth() - retentionMonths);

        console.log(`🔍 Processing user ${user.email} (${user.current_tier} tier, ${retentionMonths} months retention)`);

        // Find expired receipts for this user
        const { data: expiredReceipts, error: receiptsError } = await supabase
          .from('receipts')
          .select('id, file_path, original_file_name')
          .eq('user_id', user.id)
          .lt('created_at', cutoffDate.toISOString());

        if (receiptsError) {
          const errorMsg = `Failed to fetch expired receipts for user ${user.email}: ${receiptsError.message}`;
          result.errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
          continue;
        }

        if (!expiredReceipts || expiredReceipts.length === 0) {
          console.log(`✅ No expired receipts found for user ${user.email}`);
          continue;
        }

        console.log(`🗑️ Found ${expiredReceipts.length} expired receipts for user ${user.email}`);

        // Delete receipt items first (foreign key constraint)
        const receiptIds = expiredReceipts.map(r => r.id);
        const { error: itemsError, count: deletedItemsCount } = await supabase
          .from('receipt_items')
          .delete()
          .in('receipt_id', receiptIds);

        if (itemsError) {
          const errorMsg = `Failed to delete receipt items for user ${user.email}: ${itemsError.message}`;
          result.errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
          continue;
        }

        result.deletedItems += deletedItemsCount || 0;

        // Delete files from storage
        let deletedFilesCount = 0;
        for (const receipt of expiredReceipts) {
          try {
            if (receipt.file_path) {
              const { error: storageError } = await supabase.storage
                .from('receipts')
                .remove([receipt.file_path]);

              if (storageError) {
                console.warn(`⚠️ Failed to delete file ${receipt.file_path}: ${storageError.message}`);
              } else {
                deletedFilesCount++;
              }
            }
          } catch (error) {
            console.warn(`⚠️ Error deleting file ${receipt.file_path}: ${error}`);
          }
        }

        result.deletedFiles += deletedFilesCount;

        // Delete receipts
        const { error: receiptsDeleteError, count: deletedReceiptsCount } = await supabase
          .from('receipts')
          .delete()
          .in('id', receiptIds);

        if (receiptsDeleteError) {
          const errorMsg = `Failed to delete receipts for user ${user.email}: ${receiptsDeleteError.message}`;
          result.errors.push(errorMsg);
          console.error(`❌ ${errorMsg}`);
          continue;
        }

        result.deletedReceipts += deletedReceiptsCount || 0;

        console.log(`✅ Cleaned up ${deletedReceiptsCount} receipts, ${deletedItemsCount} items, ${deletedFilesCount} files for user ${user.email}`);

      } catch (error) {
        const errorMsg = `Unexpected error processing user ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        result.success = false;
        console.error(`❌ ${errorMsg}`);
      }
    }

    result.executionTime = Date.now() - startTime;

    // Log final results
    console.log('🎯 Data retention cleanup completed:', {
      processedUsers: result.processedUsers,
      deletedReceipts: result.deletedReceipts,
      deletedItems: result.deletedItems,
      deletedFiles: result.deletedFiles,
      errors: result.errors.length,
      executionTimeMs: result.executionTime,
      success: result.success
    });

    // Return results
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: result.success ? 200 : 207 // 207 Multi-Status for partial success
    });

  } catch (error) {
    console.error('💥 Fatal error in data retention cleanup:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      deletedReceipts: 0,
      deletedItems: 0,
      deletedFiles: 0,
      processedUsers: 0,
      errors: [error instanceof Error ? error.message : 'Unknown error'],
      executionTime: 0
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
})

function getDefaultRetentionMonths(tier: string): number {
  switch (tier) {
    case 'free':
      return 1; // 30 days
    case 'professional':
      return 6; // 6 months
    case 'business':
      return 12; // 1 year
    default:
      return 1; // Default to free tier
  }
}
