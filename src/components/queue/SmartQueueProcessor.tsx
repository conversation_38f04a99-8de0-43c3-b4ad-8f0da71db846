'use client'

import { useEffect, useRef } from 'react'
import { useNotifications } from '@/hooks/useNotifications'

interface SmartQueueProcessorProps {
  enabled?: boolean
}

// Constants
const INTENSIVE_INTERVAL = 3000 // 3 seconds
const INTENSIVE_DURATION = 60 * 1000 // 60 seconds (changed from 2 minutes)
const NORMAL_INTERVAL = 30000 // 30 seconds (conservative mode)
const MAX_INTERVAL = 300000 // 5 minutes maximum interval
const BACKOFF_MULTIPLIER = 1.5 // Exponential backoff multiplier

export default function SmartQueueProcessor({
  enabled = true
}: SmartQueueProcessorProps) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isProcessingRef = useRef(false)
  const intensivePollingRef = useRef(false)
  const intensiveStartTimeRef = useRef<number>(0)
  const lastWebhookTimeRef = useRef<number>(0)
  const consecutiveEmptyChecks = useRef(0)
  const currentInterval = useRef(NORMAL_INTERVAL)

  // Use notifications to trigger intensive polling - OPTIMIZED
  const { notifications } = useNotifications({
    enableSSE: enabled, // Only enable if processor is enabled
    enablePolling: false, // Disable automatic polling
    pollingInterval: 10000 // Check for notifications every 10 seconds (reduced frequency)
  })

  // Define processQueue function
  const processQueue = async () => {
    // Prevent overlapping calls
    if (isProcessingRef.current) {
      console.log('Queue processing already in progress, skipping')
      return
    }

    isProcessingRef.current = true

    try {
      const now = Date.now()
      
      // Check if we should stop intensive polling
      if (intensivePollingRef.current) {
        const timeSinceLastWebhook = now - lastWebhookTimeRef.current
        if (timeSinceLastWebhook > INTENSIVE_DURATION) {
          console.log('⏰ Switching to conservative polling (30s intervals)')
          intensivePollingRef.current = false
        }
      }

      const baseInterval = intensivePollingRef.current ? INTENSIVE_INTERVAL : currentInterval.current

      console.log(`QueueProcessor: Checking for jobs... (${intensivePollingRef.current ? 'intensive' : 'conservative'} mode, interval: ${baseInterval}ms)`)

      const response = await fetch('/api/pipeline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': intensivePollingRef.current ? 'Intensive-Processor/1.0' : 'Conservative-Processor/1.0'
        },
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          console.log('QueueProcessor: Jobs processed successfully:', data.batchResults || { successful: 1 })

          // Reset backoff when jobs are found
          consecutiveEmptyChecks.current = 0
          currentInterval.current = NORMAL_INTERVAL

          // If we processed jobs during intensive polling, continue intensive mode
          if (intensivePollingRef.current) {
            // Process again quickly if there might be more jobs
            setTimeout(() => processQueue(), 1000)
            return
          }
        } else {
          console.log('QueueProcessor: No jobs in queue or processing failed')

          // Implement exponential backoff for empty queue
          consecutiveEmptyChecks.current++
          if (consecutiveEmptyChecks.current >= 3 && !intensivePollingRef.current) {
            currentInterval.current = Math.min(
              currentInterval.current * BACKOFF_MULTIPLIER,
              MAX_INTERVAL
            )
            console.log(`QueueProcessor: Backing off to ${currentInterval.current}ms after ${consecutiveEmptyChecks.current} empty checks`)
          }
        }
      } else if (response.status === 200) {
        // Check response body even if status is 200 but not success
        const data = await response.json()
        if (data.message?.includes('No jobs in queue')) {
          console.log('QueueProcessor: No jobs in queue')

          // Implement exponential backoff for empty queue
          consecutiveEmptyChecks.current++
          if (consecutiveEmptyChecks.current >= 3 && !intensivePollingRef.current) {
            currentInterval.current = Math.min(
              currentInterval.current * BACKOFF_MULTIPLIER,
              MAX_INTERVAL
            )
            console.log(`QueueProcessor: Backing off to ${currentInterval.current}ms after ${consecutiveEmptyChecks.current} empty checks`)
          }
        }
      } else {
        console.error('QueueProcessor: Pipeline processing failed:', response.status, await response.text())
      }

      // Schedule next check with current interval (including backoff)
      if (intervalRef.current) {
        clearTimeout(intervalRef.current)
      }
      const nextInterval = intensivePollingRef.current ? INTENSIVE_INTERVAL : currentInterval.current
      intervalRef.current = setTimeout(() => processQueue(), nextInterval)

    } catch (error) {
      console.error('QueueProcessor: Error calling pipeline:', error)
      
      // Schedule next check with current interval (including backoff)
      const nextInterval = intensivePollingRef.current ? INTENSIVE_INTERVAL : currentInterval.current
      if (intervalRef.current) {
        clearTimeout(intervalRef.current)
      }
      intervalRef.current = setTimeout(() => processQueue(), nextInterval)
    } finally {
      isProcessingRef.current = false
    }
  }

  const startIntensivePolling = () => {
    const wasAlreadyIntensive = intensivePollingRef.current
    console.log(`🚀 ${wasAlreadyIntensive ? 'Resetting' : 'Starting'} intensive polling (3s intervals for 60 seconds)`)

    intensivePollingRef.current = true
    intensiveStartTimeRef.current = Date.now()
    lastWebhookTimeRef.current = Date.now()

    // Reset backoff when starting intensive polling
    consecutiveEmptyChecks.current = 0
    currentInterval.current = NORMAL_INTERVAL

    // Start processing immediately (only if not already in intensive mode)
    if (!wasAlreadyIntensive) {
      processQueue()
    }
  }

  // Start conservative polling on mount - OPTIMIZED WITH BACKOFF
  useEffect(() => {
    if (!enabled) return

    console.log('SmartQueueProcessor: Starting with optimized polling and exponential backoff')

    // Reset state
    consecutiveEmptyChecks.current = 0
    currentInterval.current = NORMAL_INTERVAL

    // Start with a small delay to avoid immediate polling
    setTimeout(() => processQueue(), 5000) // 5 second initial delay

    return () => {
      if (intervalRef.current) {
        clearTimeout(intervalRef.current)
      }
    }
  }, [enabled])

  // Watch for webhook notifications to trigger intensive polling
  useEffect(() => {
    if (!enabled || !notifications.length) return

    // Check the latest notification for webhook triggers
    const latestNotification = notifications[0]
    if (latestNotification &&
        'event_type' in latestNotification &&
        (latestNotification.event_type === 'webhook.triggered' ||
         latestNotification.event_type === 'receipt.uploaded')) {
      console.log('📡 Webhook notification received, starting intensive polling')
      startIntensivePolling()
    }
  }, [notifications, enabled, startIntensivePolling])

  // Listen for webhook events to trigger intensive polling
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return

    const handleWebhookEvent = () => {
      console.log('📡 Webhook event detected, starting intensive polling')
      startIntensivePolling()
    }

    // Safely add event listener
    try {
      window.addEventListener('receipt-webhook', handleWebhookEvent)
    } catch (error) {
      console.warn('Failed to add webhook event listener:', error)
    }

    // Also expose global method for manual triggering - with safety checks
    try {
      if (typeof window !== 'undefined') {
        (window as any).triggerIntensivePolling = startIntensivePolling
        (window as any).triggerQueueProcessing = () => processQueue()
      }
    } catch (error) {
      console.warn('Failed to set global functions:', error)
    }

    return () => {
      try {
        if (typeof window !== 'undefined') {
          window.removeEventListener('receipt-webhook', handleWebhookEvent)

          // Only delete if we own these functions
          if ((window as any).triggerIntensivePolling === startIntensivePolling) {
            delete (window as any).triggerIntensivePolling
          }
          if ((window as any).triggerQueueProcessing) {
            delete (window as any).triggerQueueProcessing
          }
        }
      } catch (error) {
        console.warn('Failed to cleanup global functions:', error)
      }
    }
  }, [enabled, startIntensivePolling, processQueue])

  // This component doesn't render anything visible
  return null
}
