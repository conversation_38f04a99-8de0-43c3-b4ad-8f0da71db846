import Link from 'next/link'
import FooterLinks from '@/components/homepage/FooterLinks'

export default function Footer() {
  return (
    <footer className="relative z-10 px-4 py-12 border-t border-gray-800">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-5 gap-4 md:gap-6 lg:gap-8 mb-8">
          {/* Brand */}
          <div className="sm:col-span-2 md:col-span-1">
            <div className="flex items-center mb-4">
              <Link href="/">
                <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
              </Link>
            </div>
            <p className="text-gray-400 text-sm mb-4">
              AI-powered expense management specifically designed for small businesses. 
              Transform your receipt processing with 99% accuracy.
            </p>
            {/* Social Media Icons */}
            <div className="flex space-x-4">
              <a
                href="#"
                className="text-gray-400 hover:text-pink-500 transition-colors"
                aria-label="Twitter"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-pink-500 transition-colors"
                aria-label="LinkedIn"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>

          {/* Product */}
          <div>
            <h3 className="text-white font-semibold mb-4">Product</h3>
            <ul className="space-y-2">
              <li><a href="#features" className="text-gray-400 hover:text-white transition-colors text-sm">Features</a></li>
              <li><a href="#pricing" className="text-gray-400 hover:text-white transition-colors text-sm">Pricing</a></li>
              <li><a href="#how-it-works" className="text-gray-400 hover:text-white transition-colors text-sm">How it Works</a></li>
              <li><Link href="/dashboard" className="text-gray-400 hover:text-white transition-colors text-sm">Dashboard</Link></li>
            </ul>
          </div>

          {/* Free Tools */}
          <div>
            <h3 className="text-white font-semibold mb-4">Free Tools</h3>
            <ul className="space-y-2">
              <li><Link href="/receipt-maker" className="text-gray-400 hover:text-white transition-colors text-sm">Receipt Generator</Link></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Invoice Template</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Expense Tracker</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Tax Calculator</a></li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="text-white font-semibold mb-4">Resources</h3>
            <ul className="space-y-2">
              <li><a href="/blog" className="text-gray-400 hover:text-white transition-colors text-sm">Blog</a></li>
              <li><Link href="/expense-management-small-business" className="text-gray-400 hover:text-white transition-colors text-sm">Small Business Guide</Link></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">Tax Preparation Tips</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors text-sm">API Documentation</a></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-white font-semibold mb-4">Support</h3>
            <ul className="space-y-2">
              <li><a href="/contact" className="text-gray-400 hover:text-white transition-colors text-sm">Help Center</a></li>
              <li><a href="/contact" className="text-gray-400 hover:text-white transition-colors text-sm">Contact Us</a></li>
              <li><a href="/privacy" className="text-gray-400 hover:text-white transition-colors text-sm">Privacy Policy</a></li>
              <li><a href="/terms" className="text-gray-400 hover:text-white transition-colors text-sm">Terms of Service</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2025 <strong>Receipt</strong>Labs. All rights reserved.
          </p>
          <FooterLinks />
        </div>
      </div>
    </footer>
  )
}