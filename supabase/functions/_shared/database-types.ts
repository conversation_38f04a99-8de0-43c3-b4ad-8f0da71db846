// Database table type definitions for RecoAI
// These match the schema defined in migrations/20250612_initial_schema.sql

export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  google_access_token?: string;
  google_refresh_token?: string;
  google_sheets_connected?: boolean;
  current_tier: 'free' | 'professional' | 'business';
  receipts_processed: number;
  subscription_status: 'inactive' | 'active' | 'cancelled' | 'expired' | 'past_due';
  subscription_end_date?: string;
  paystack_customer_code?: string;
  paystack_authorization_code?: string;
  subscription_plan_code?: string;
  monthly_receipt_limit: number;
  current_period_start?: string;
  current_period_end?: string;
  receipts_used_this_period: number;
  last_payment_date?: string;
  next_billing_date?: string;
  data_retention_months: number;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionTransaction {
  id: string;
  user_id: string;
  paystack_reference: string;
  paystack_transaction_id?: number;
  amount: number;
  currency: string;
  status: 'pending' | 'success' | 'failed' | 'abandoned';
  tier: 'free' | 'professional' | 'business';
  payment_method?: string;
  gateway_response?: string;
  metadata?: Record<string, any>;
  paid_at?: string;
  created_at: string;
  updated_at: string;
}

export interface TierLimits {
  can_process_receipt: boolean;
  receipts_remaining: number;
  has_analytics_access: boolean;
  has_google_drive_access: boolean;
  has_gmail_auto_processing: boolean;
  data_retention_months: number;
}

export interface Receipt {
  id: string;
  user_id: string;
  original_file_name: string;
  file_path: string;
  file_size?: number;
  mime_type?: string;
  
  // Extracted data fields
  vendor?: string;
  vendor_tax_id?: string;
  receipt_date?: string; // YYYY-MM-DD format
  currency: string;
  payment_method?: string;
  subtotal?: number;
  tax_rate_percent?: number;
  tax_amount?: number;
  total_amount?: number;
  paid_amount?: number;
  
  // Processing metadata
  processing_status: 'pending' | 'processing' | 'completed' | 'failed';
  confidence_score?: number;
  extraction_method?: 'openai' | 'manual';
  error_message?: string;
  google_sheet_row_number?: number;
  redis_job_id?: string;
  
  created_at: string;
  updated_at: string;
}

export interface ReceiptItem {
  id: string;
  receipt_id: string;
  description: string;
  quantity: number;
  unit_price?: number;
  total_price: number;
  category?: string;
  created_at: string;
}

export interface GoogleSheet {
  id: string;
  user_id: string;
  year: number;
  sheet_id: string;
  sheet_url: string;
  sheet_name: string;
  last_row_number: number;
  total_receipts: number;
  created_at: string;
  updated_at: string;
}

export interface PaymentTransaction {
  id: string;
  user_id: string;
  paystack_reference: string;
  amount: number;
  currency: string;
  status: 'pending' | 'successful' | 'failed';
  tier: 'tier1' | 'tier2';
  receipts_allowance: number;
  paystack_response?: any; // JSONB
  created_at: string;
  updated_at: string;
}

export interface UserAnalytics {
  id: string;
  user_id: string;
  month_year: string; // Format: "2025-06"
  total_receipts: number;
  total_spent: number;
  top_vendor?: string;
  top_category?: string;
  average_receipt_amount: number;
  category_breakdown?: Record<string, number>; // JSONB
  vendor_breakdown?: Record<string, number>; // JSONB
  created_at: string;
  updated_at: string;
}

// Insert types (without id, created_at, updated_at for auto-generated fields)
export interface InsertReceipt {
  user_id: string;
  original_file_name: string;
  file_path: string;
  file_size?: number;
  mime_type?: string;
  vendor?: string;
  vendor_tax_id?: string;
  receipt_date?: string;
  currency?: string;
  payment_method?: string;
  subtotal?: number;
  tax_rate_percent?: number;
  tax_amount?: number;
  total_amount?: number;
  paid_amount?: number;
  processing_status?: 'pending' | 'processing' | 'completed' | 'failed';
  confidence_score?: number;
  extraction_method?: 'openai' | 'manual';
  error_message?: string;
  google_sheet_row_number?: number;
  redis_job_id?: string;
}

export interface InsertReceiptItem {
  receipt_id: string;
  description: string;
  quantity?: number;
  unit_price?: number;
  total_price: number;
  category?: string;
}

// Update types (partial updates)
export interface UpdateReceipt {
  vendor?: string;
  vendor_tax_id?: string;
  receipt_date?: string;
  currency?: string;
  payment_method?: string;
  subtotal?: number;
  tax_rate_percent?: number;
  tax_amount?: number;
  total_amount?: number;
  paid_amount?: number;
  processing_status?: 'pending' | 'processing' | 'completed' | 'failed';
  confidence_score?: number;
  extraction_method?: 'openai' | 'manual';
  error_message?: string;
  google_sheet_row_number?: number;
} 