'use client';

import { useState, useEffect } from 'react';
import { CreditCard, Crown, TrendingUp, Calendar, ExternalLink, AlertCircle, CheckCircle, X, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { UserData } from './SettingsClient';
import { SUBSCRIPTION_TIERS, getTierById, formatReceiptLimit } from '@/lib/subscription/tiers';
import UsageDashboard from '@/components/billing/UsageDashboard';

interface BillingSettingsProps {
  userData: UserData;
  onUserDataUpdate: () => void;
}

interface SubscriptionTransaction {
  id: string;
  paystack_reference: string;
  amount: number;
  currency: string;
  status: string;
  tier: string;
  paid_at: string | null;
  created_at: string;
}

const plans = [
  {
    id: 'free',
    name: 'Free',
    price: 0,
    period: 'month',
    receipts: 10,
    features: [
      '10 receipts per month',
      'Google Drive integration',
      'Advanced data extraction',
      'Enhanced Google Sheets with analytics',
      '30-day data retention',
      'Receipt categorization and tagging',
      'Search and filter capabilities',
      'No analytics page access'
    ],
    color: 'bg-gray-500/20 text-gray-400',
    popular: false
  },
  {
    id: 'professional',
    name: 'Professional',
    price: 4999,
    period: 'month',
    receipts: 500,
    features: [
      '500 receipts per month',
      'Google Drive integration',
      'Advanced data extraction',
      'Enhanced Google Sheets with analytics',
      '6-month data retention',
      'Receipt categorization and tagging',
      'Search and filter capabilities',
      'Full analytics access'
    ],
    color: 'bg-pink-500/20 text-pink-400',
    popular: true
  },
  {
    id: 'business',
    name: 'Business',
    price: 6999,
    period: 'month',
    receipts: -1, // Unlimited
    features: [
      'Unlimited receipts',
      '1-year data retention (per Google Sheet year)',
      'Google Drive integration',
      'Advanced data extraction',
      'Enhanced Google Sheets with analytics',
      'Receipt categorization and tagging',
      'Search and filter capabilities',
      'Full analytics access',
      '📧 Gmail auto-processing (Coming Soon)',
      'Priority support'
    ],
    color: 'bg-purple-500/20 text-purple-400',
    popular: false
  }
];

export default function BillingSettings({ userData, onUserDataUpdate }: BillingSettingsProps) {
  const [loading, setLoading] = useState<string | null>(null);
  const [billingHistory, setBillingHistory] = useState<SubscriptionTransaction[]>([]);
  const [billingLoading, setBillingLoading] = useState(true);

  const currentPlan = plans.find(plan => plan.id === userData.current_tier) || plans[0];

  // Fetch billing history
  useEffect(() => {
    const fetchBillingHistory = async () => {
      try {
        setBillingLoading(true);
        const response = await fetch('/api/subscription/transactions');
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setBillingHistory(data.transactions || []);
          }
        }
      } catch (error) {
        console.error('Error fetching billing history:', error);
      } finally {
        setBillingLoading(false);
      }
    };

    fetchBillingHistory();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getUsagePercentage = () => {
    if (currentPlan.receipts === -1) return 0; // Unlimited
    const receiptsUsed = userData.receipts_used_this_period || userData.receipts_processed || 0;
    return Math.min((receiptsUsed / currentPlan.receipts) * 100, 100);
  };

  const handlePlanChange = async (planId: string) => {
    try {
      setLoading(planId);

      // Only allow upgrades to paid tiers
      if (!['professional', 'business'].includes(planId)) {
        throw new Error('Invalid plan selection');
      }

      console.log('Initializing payment for tier:', planId);

      // Initialize Paystack payment
      const response = await fetch('/api/subscription/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ targetTier: planId }),
      });

      console.log('Payment initialization response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Payment initialization failed:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      console.log('Payment initialization result:', result);

      if (result.success && result.data?.authorization_url) {
        console.log('Redirecting to payment URL:', result.data.authorization_url);
        // Redirect to Paystack payment page
        window.location.href = result.data.authorization_url;
      } else {
        throw new Error(result.error || 'Failed to get payment URL');
      }
    } catch (error) {
      console.error('Plan change error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to change plan. Please try again.';
      alert(`Plan change failed: ${errorMessage}`);
    } finally {
      setLoading(null);
    }
  };

  const handleManageSubscription = () => {
    // In a real app, this would redirect to Stripe customer portal
    window.open('/api/billing/customer-portal', '_blank');
  };

  return (
    <div className="space-y-8">
      {/* Usage Dashboard */}
      <UsageDashboard userData={userData} />

      {/* Current Plan */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-pink-600/20 rounded-lg flex items-center justify-center">
            <Crown className="w-5 h-5 text-pink-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Current Plan</h3>
            <p className="text-gray-400 text-sm">Your subscription details and usage</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <Badge className={currentPlan.color}>
                  {currentPlan.name}
                </Badge>
                {userData.subscription_status === 'active' && (
                  <Badge className="bg-green-500/20 text-green-400">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Active
                  </Badge>
                )}
                {userData.subscription_status === 'cancelled' && (
                  <Badge className="bg-yellow-500/20 text-yellow-400">
                    <AlertCircle className="w-3 h-3 mr-1" />
                    Cancelled
                  </Badge>
                )}
              </div>
              <p className="text-2xl font-bold text-white">
                KES {currentPlan.price.toLocaleString()}
                <span className="text-gray-400 text-base font-normal">/{currentPlan.period}</span>
              </p>
            </div>

            {userData.subscription_end_date && (
              <div>
                <label className="block text-sm font-medium text-gray-400 mb-1">
                  {userData.subscription_status === 'active' ? 'Next Billing Date' : 'Expires On'}
                </label>
                <p className="text-white">{formatDate(userData.subscription_end_date)}</p>
              </div>
            )}

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-1">
                Payment Method
              </label>
              <div className="flex items-center gap-2">
                <CreditCard className="w-4 h-4 text-gray-400" />
                <span className="text-white">•••• •••• •••• 4242</span>
                <span className="text-gray-400 text-sm">Expires 12/25</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm font-medium text-gray-400">
                  Receipts Used This Month
                </label>
                <span className="text-white">
                  {userData.receipts_used_this_period || userData.receipts_processed || 0} / {currentPlan.receipts === -1 ? '∞' : currentPlan.receipts}
                </span>
              </div>
              {currentPlan.receipts !== -1 && (
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${
                      getUsagePercentage() > 80 ? 'bg-red-500' : 
                      getUsagePercentage() > 60 ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${getUsagePercentage()}%` }}
                  />
                </div>
              )}
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleManageSubscription}
                className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                <ExternalLink className="w-4 h-4" />
                Manage
              </button>
              {userData.current_tier !== 'business' && (
                <button
                  onClick={() => handlePlanChange(userData.current_tier === 'free' ? 'professional' : 'business')}
                  disabled={loading !== null}
                  className="flex items-center gap-2 bg-pink-600 hover:bg-pink-700 disabled:bg-pink-600/50 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  <Crown className="w-4 h-4" />
                  {loading !== null ? 'Processing...' : 'Upgrade'}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Available Plans */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <h3 className="text-xl font-semibold text-white mb-6">Available Plans</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-gray-700/50 border rounded-xl p-6 transition-all duration-200 ${
                plan.id === userData.current_tier
                  ? 'border-pink-500 ring-2 ring-pink-500/20'
                  : 'border-gray-600 hover:border-gray-500'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-pink-600 text-white">Most Popular</Badge>
                </div>
              )}

              <div className="text-center mb-6">
                <h4 className="text-xl font-bold text-white mb-2">{plan.name}</h4>
                <div className="text-3xl font-bold text-white mb-1">
                  KES {plan.price.toLocaleString()}
                  <span className="text-gray-400 text-base font-normal">/{plan.period}</span>
                </div>
                <p className="text-gray-400 text-sm">
                  {plan.receipts === -1 ? 'Unlimited' : `Up to ${plan.receipts}`} receipts
                </p>
              </div>

              <ul className="space-y-3 mb-6">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 text-sm">
                    <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                onClick={() => handlePlanChange(plan.id)}
                disabled={plan.id === userData.current_tier || plan.id === 'free' || loading === plan.id}
                className={`w-full px-4 py-2 rounded-lg transition-colors ${
                  plan.id === userData.current_tier
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : plan.id === 'free'
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : plan.popular
                    ? 'bg-pink-600 hover:bg-pink-700 text-white'
                    : 'bg-gray-600 hover:bg-gray-700 text-white'
                } ${loading === plan.id ? 'opacity-50' : ''}`}
              >
                {loading === plan.id ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Processing...
                  </div>
                ) : plan.id === userData.current_tier ? (
                  'Current Plan'
                ) : plan.id === 'free' ? (
                  'Downgrade Not Available'
                ) : (
                  `Upgrade to ${plan.name}`
                )}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Usage Statistics */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-5 h-5 text-blue-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Usage Statistics</h3>
            <p className="text-gray-400 text-sm">Your usage patterns and trends</p>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">{userData.receipts_processed}</div>
            <div className="text-gray-400 text-sm">Total Processed</div>
          </div>
          
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">
              {Math.round(userData.receipts_processed / Math.max(1, Math.ceil((Date.now() - new Date(userData.created_at).getTime()) / (1000 * 60 * 60 * 24 * 30))))}
            </div>
            <div className="text-gray-400 text-sm">Per Month Avg</div>
          </div>
          
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">
              {currentPlan.receipts === -1 ? '∞' : Math.max(0, currentPlan.receipts - (userData.receipts_used_this_period || userData.receipts_processed || 0))}
            </div>
            <div className="text-gray-400 text-sm">Remaining</div>
          </div>
          
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">
              {currentPlan.receipts === -1 ? '0' : Math.round(getUsagePercentage())}%
            </div>
            <div className="text-gray-400 text-sm">Used</div>
          </div>
        </div>
      </div>

      {/* Billing History */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-600/20 rounded-lg flex items-center justify-center">
              <Calendar className="w-5 h-5 text-green-400" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white">Billing History</h3>
              <p className="text-gray-400 text-sm">Your recent transactions</p>
            </div>
          </div>
          
          <button
            onClick={() => window.location.href = '/dashboard/billing-history'}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
          >
            <ExternalLink className="w-4 h-4" />
            View All
          </button>
        </div>

        <div className="space-y-3">
          {billingLoading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-400">Loading billing history...</span>
            </div>
          ) : billingHistory.length === 0 ? (
            <div className="text-center p-8">
              <Calendar className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400">No billing history yet</p>
              <p className="text-gray-500 text-sm">Your transactions will appear here after you make a purchase</p>
            </div>
          ) : (
            billingHistory.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 bg-gray-700/50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    transaction.status === 'success'
                      ? 'bg-green-600/20'
                      : transaction.status === 'failed'
                      ? 'bg-red-600/20'
                      : 'bg-yellow-600/20'
                  }`}>
                    {transaction.status === 'success' ? (
                      <CheckCircle className="w-4 h-4 text-green-400" />
                    ) : transaction.status === 'failed' ? (
                      <X className="w-4 h-4 text-red-400" />
                    ) : (
                      <Loader2 className="w-4 h-4 text-yellow-400" />
                    )}
                  </div>
                  <div>
                    <p className="text-white font-medium">
                      {transaction.tier.charAt(0).toUpperCase() + transaction.tier.slice(1)} Plan
                    </p>
                    <p className="text-gray-400 text-sm">
                      {transaction.status === 'success' && transaction.paid_at
                        ? `Paid on ${formatDate(transaction.paid_at)}`
                        : `${transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)} on ${formatDate(transaction.created_at)}`
                      }
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-medium">
                    {transaction.currency} {transaction.amount.toLocaleString()}
                  </p>
                  {transaction.status === 'success' && (
                    <button className="text-gray-400 hover:text-white text-sm">
                      Receipt
                    </button>
                  )}
                </div>
              </div>
            ))
          )}

          <div className="text-center py-4">
            <p className="text-gray-400 text-sm">
              No more transactions to show
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
