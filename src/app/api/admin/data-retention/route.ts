import { NextRequest, NextResponse } from 'next/server';
import { dataRetentionService } from '@/lib/data-retention/service';

// This endpoint should be called by a cron job or scheduled task
// It's protected by a simple API key for security
export async function POST(request: NextRequest) {
  try {
    // Simple API key protection
    const authHeader = request.headers.get('authorization');
    const expectedKey = process.env.DATA_RETENTION_API_KEY || 'default-key';
    
    if (!authHeader || authHeader !== `Bearer ${expectedKey}`) {
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    console.log('Starting data retention cleanup...');
    
    const result = await dataRetentionService.cleanupExpiredData();
    
    return NextResponse.json({
      success: result.success,
      data: {
        deletedReceipts: result.deletedReceipts,
        deletedItems: result.deletedItems,
        errors: result.errors
      },
      message: result.success 
        ? `Successfully cleaned up ${result.deletedReceipts} receipts`
        : 'Data retention cleanup completed with errors'
    });

  } catch (error) {
    console.error('Data retention cleanup error:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}

// Get retention info for current user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const preview = searchParams.get('preview') === 'true';

    if (!userId) {
      return NextResponse.json({ 
        success: false, 
        error: 'User ID is required' 
      }, { status: 400 });
    }

    if (preview) {
      const previewData = await dataRetentionService.previewCleanup(userId);
      return NextResponse.json({
        success: true,
        data: previewData
      });
    } else {
      const retentionInfo = await dataRetentionService.getUserRetentionInfo(userId);
      return NextResponse.json({
        success: true,
        data: retentionInfo
      });
    }

  } catch (error) {
    console.error('Error getting retention info:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}
