import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { validateGoogleDriveTokens } from '@/lib/google-drive/auth';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Test Google Drive connection
    const validation = await validateGoogleDriveTokens(user.id);

    return NextResponse.json({
      success: true,
      data: {
        valid: validation.valid,
        error: validation.error
      }
    });
  } catch (error) {
    console.error('Error testing Google Drive connection:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to test Google Drive connection' 
      }, 
      { status: 500 }
    );
  }
}
