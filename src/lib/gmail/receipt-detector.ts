import { GoogleGenerativeAI } from '@google/generative-ai';
import { GmailMessage, GmailAttachment } from './client';

export interface ReceiptDetectionResult {
  isReceipt: boolean;
  confidence: number;
  reason: string;
  attachments: string[]; // Attachment IDs that are likely receipts
  extractedInfo?: {
    vendor?: string;
    amount?: string;
    date?: string;
    currency?: string;
  };
}

export interface EmailAnalysisResult {
  messageId: string;
  isReceiptEmail: boolean;
  confidence: number;
  reason: string;
  receiptAttachments: GmailAttachment[];
  extractedInfo?: {
    vendor?: string;
    amount?: string;
    date?: string;
    currency?: string;
  };
}

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

/**
 * Accountant-grade prompt for Gemini to analyze emails for receipt content
 */
const GMAIL_RECEIPT_DETECTION_PROMPT = `
You are a professional accountant with 20+ years of experience in financial record-keeping and expense management. Your task is to analyze emails with the precision required for accounting and tax compliance purposes.

CRITICAL ACCOUNTING REQUIREMENTS:
- Only classify emails that contain VERIFIABLE financial transactions
- Must have clear evidence of money changing hands
- Should be suitable for expense reporting and tax deduction purposes
- Attachments don't need specific naming conventions (could be "screenshot.png", "image.jpg", etc.)

POSITIVE INDICATORS (Mark as Receipt):
✅ Purchase confirmations with transaction amounts and dates
✅ Invoices from suppliers, vendors, or service providers
✅ Payment confirmations showing completed transactions
✅ Digital receipts from online purchases (Amazon, eBay, etc.)
✅ Restaurant/food delivery receipts with itemized costs
✅ Subscription payments and recurring billing confirmations
✅ Utility bills, phone bills, internet bills with payment amounts
✅ Travel expenses: flights, hotels, car rentals with payment details
✅ Professional services: legal, consulting, medical with fees
✅ Software licenses, SaaS subscriptions with payment confirmations
✅ Bank/credit card transaction notifications with merchant details
✅ Insurance premium payments and policy confirmations
✅ Tax payments and government fee confirmations
✅ Equipment purchases, office supplies with transaction details
✅ Fuel receipts, parking fees, toll payments
✅ Any email with attachments (PDF, images) that could contain receipt data

ATTACHMENT ANALYSIS:
- ANY attachment (regardless of filename) could contain receipt information
- Screenshots, photos, scanned documents are common receipt formats
- Don't rely on filename - "IMG_001.jpg" could be a receipt photo
- PDFs are highly likely to contain invoices or receipts
- Even generic filenames like "document.pdf" or "image.png" should be considered

NEGATIVE INDICATORS (NOT Receipts):
❌ Marketing emails, newsletters, promotional content
❌ Shipping/tracking notifications WITHOUT payment details
❌ Account creation, welcome emails, password resets
❌ Appointment confirmations WITHOUT payment amounts
❌ Product announcements, feature updates
❌ Survey requests, feedback forms
❌ Social media notifications
❌ Security alerts, login notifications
❌ Warranty information WITHOUT purchase details
❌ Return/refund processing emails (unless showing refund amounts)

CONFIDENCE SCORING:
- 90-100: Clear transaction with amount, date, vendor (e.g., "Payment of $50.00 to ABC Corp")
- 80-89: Strong indicators with most transaction details present
- 70-79: Good indicators but missing some details (amount or date)
- 60-69: Likely receipt but requires manual verification
- 50-59: Uncertain - could be receipt-related
- Below 50: Probably not a receipt

SPECIAL CONSIDERATIONS:
- Business emails from known vendors/suppliers are highly likely receipts
- Government/tax authority emails with payment references are receipts
- Bank notifications about transactions are valuable for accounting
- Even informal payment confirmations (Venmo, PayPal, etc.) count as receipts
- Subscription renewals and auto-payments are legitimate business expenses

Return ONLY valid JSON:
{
  "isReceipt": boolean,
  "confidence": number (0-100),
  "reason": "Detailed accounting-focused explanation",
  "attachments": ["ALL attachment filenames that could contain receipt data"],
  "extractedInfo": {
    "vendor": "Business/vendor name if clearly identifiable",
    "amount": "Transaction amount with currency if mentioned",
    "date": "Transaction/purchase date if mentioned",
    "currency": "Currency code (KES, USD, EUR, etc.)"
  }
}

REMEMBER: Accountants need comprehensive records. When in doubt about attachments, include them. Better to have false positives than miss legitimate business expenses.
`;

/**
 * Analyze a Gmail message to determine if it contains receipt information
 */
export async function analyzeEmailForReceipt(message: GmailMessage): Promise<EmailAnalysisResult> {
  try {
    console.log(`🔍 Analyzing email for receipt: ${message.subject}`);
    
    // Prepare email content for analysis
    const emailContent = `
Subject: ${message.subject}
From: ${message.sender}
Date: ${message.receivedDate.toISOString()}
Snippet: ${message.snippet}
Attachments: ${message.attachments.map(a => `${a.filename} (${a.mimeType})`).join(', ')}
`;

    console.log('📧 Email content for analysis:', emailContent.substring(0, 200) + '...');

    // Use Gemini 2.5 Flash for analysis
    const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });
    
    const result = await Promise.race([
      model.generateContent([
        GMAIL_RECEIPT_DETECTION_PROMPT,
        `\nEmail to analyze:\n${emailContent}`
      ]),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Gemini analysis timeout after 15 seconds')), 15000)
      )
    ]);

    const response = await result.response;
    const text = response.text();
    
    console.log('🤖 Gemini response:', text);

    // Parse JSON response
    let analysisResult: ReceiptDetectionResult;
    try {
      // Extract JSON from response (handle potential markdown formatting)
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      analysisResult = JSON.parse(jsonMatch[0]);
    } catch (parseError) {
      console.error('❌ Failed to parse Gemini response:', parseError);
      // Fallback analysis based on keywords
      return fallbackReceiptDetection(message);
    }

    // Validate response structure
    if (typeof analysisResult.isReceipt !== 'boolean' || 
        typeof analysisResult.confidence !== 'number') {
      console.warn('⚠️ Invalid Gemini response structure, using fallback');
      return fallbackReceiptDetection(message);
    }

    // Filter attachments that are likely receipts
    const receiptAttachments = message.attachments.filter(attachment => {
      // Include PDF and image attachments
      if (!attachment.mimeType.startsWith('image/') && attachment.mimeType !== 'application/pdf') {
        return false;
      }
      
      // If Gemini specified attachment names, use those
      if (analysisResult.attachments && analysisResult.attachments.length > 0) {
        return analysisResult.attachments.some(name => 
          attachment.filename.toLowerCase().includes(name.toLowerCase()) ||
          name.toLowerCase().includes(attachment.filename.toLowerCase())
        );
      }
      
      // Otherwise include all PDF/image attachments if email is classified as receipt
      return analysisResult.isReceipt;
    });

    console.log(`✅ Analysis complete: isReceipt=${analysisResult.isReceipt}, confidence=${analysisResult.confidence}, attachments=${receiptAttachments.length}`);

    return {
      messageId: message.id,
      isReceiptEmail: analysisResult.isReceipt,
      confidence: analysisResult.confidence,
      reason: analysisResult.reason,
      receiptAttachments,
      extractedInfo: analysisResult.extractedInfo
    };

  } catch (error) {
    console.error('❌ Error analyzing email with Gemini:', error);
    // Fallback to keyword-based detection
    return fallbackReceiptDetection(message);
  }
}

/**
 * Accountant-grade fallback receipt detection using comprehensive keyword matching
 */
function fallbackReceiptDetection(message: GmailMessage): EmailAnalysisResult {
  console.log('🔄 Using accountant-grade fallback receipt detection for:', message.subject);

  // Comprehensive accounting-focused keywords
  const receiptKeywords = [
    // Direct payment terms
    'receipt', 'invoice', 'bill', 'payment', 'paid', 'transaction', 'purchase', 'order',
    'confirmation', 'statement', 'charge', 'fee', 'cost', 'amount', 'total', 'subtotal',

    // Business transaction terms
    'subscription', 'renewal', 'license', 'service', 'consulting', 'professional',
    'maintenance', 'support', 'hosting', 'domain', 'software', 'saas',

    // Vendor/supplier terms
    'supplier', 'vendor', 'contractor', 'freelancer', 'consultant', 'provider',

    // Expense categories
    'travel', 'hotel', 'flight', 'car rental', 'taxi', 'uber', 'lyft', 'fuel', 'gas',
    'restaurant', 'meal', 'food', 'catering', 'office supplies', 'equipment',
    'utilities', 'phone', 'internet', 'electricity', 'water', 'insurance',

    // Financial terms
    'debit', 'credit', 'bank', 'card', 'paypal', 'stripe', 'square', 'venmo',
    'wire transfer', 'ach', 'direct deposit', 'refund', 'chargeback',

    // Government/tax terms
    'tax', 'irs', 'kra', 'government', 'license fee', 'permit', 'registration',

    // Common business phrases
    'thank you for your purchase', 'payment received', 'order summary',
    'payment successful', 'transaction complete', 'billing statement'
  ];

  const excludeKeywords = [
    'newsletter', 'unsubscribe', 'marketing', 'promotion', 'advertisement', 'promo',
    'sale announcement', 'new product', 'feature update', 'blog post', 'webinar',
    'survey', 'feedback', 'review request', 'social media', 'follow us',
    'password', 'account created', 'welcome', 'verify email', 'login attempt',
    'security alert', 'suspicious activity', 'privacy policy', 'terms of service'
  ];
  
  const emailText = `${message.subject} ${message.snippet} ${message.sender}`.toLowerCase();

  // Check for exclude keywords first (but be less aggressive for accounting purposes)
  const excludeMatches = excludeKeywords.filter(keyword => emailText.includes(keyword));
  const hasStrongExcludeKeyword = excludeMatches.some(keyword =>
    ['newsletter', 'unsubscribe', 'marketing', 'promotion', 'password', 'welcome'].includes(keyword)
  );

  if (hasStrongExcludeKeyword) {
    return {
      messageId: message.id,
      isReceiptEmail: false,
      confidence: 15,
      reason: `Contains strong exclude keywords (${excludeMatches.join(', ')}) - likely not a business expense`,
      receiptAttachments: []
    };
  }

  // Check for receipt keywords with weighted scoring
  const matchedKeywords = receiptKeywords.filter(keyword => emailText.includes(keyword));
  const hasReceiptKeywords = matchedKeywords.length > 0;

  // Check for attachments (ANY attachment could be a receipt)
  const hasRelevantAttachments = message.attachments.some(att =>
    att.mimeType.startsWith('image/') ||
    att.mimeType === 'application/pdf' ||
    att.mimeType.startsWith('application/') // Include other document types
  );

  // Enhanced confidence scoring for accounting purposes
  let confidence = 0;
  let isReceipt = false;
  let reason = '';

  // High-value keywords get bonus points
  const highValueKeywords = ['invoice', 'receipt', 'payment', 'bill', 'transaction', 'purchase'];
  const highValueMatches = matchedKeywords.filter(kw => highValueKeywords.includes(kw));

  // Business sender domains get bonus points
  const businessDomains = ['@amazon.', '@paypal.', '@stripe.', '@square.', '@uber.', '@lyft.',
                          '@booking.', '@expedia.', '@airbnb.', '@microsoft.', '@google.',
                          '@apple.', '@adobe.', '@salesforce.', '@shopify.'];
  const isBusinessSender = businessDomains.some(domain => message.sender.toLowerCase().includes(domain));

  // Calculate base confidence
  if (hasReceiptKeywords && hasRelevantAttachments) {
    confidence = Math.min(85, 50 + (matchedKeywords.length * 8) + (highValueMatches.length * 10));
    if (isBusinessSender) confidence += 10;
    isReceipt = true;
    reason = `Strong receipt indicators: keywords (${matchedKeywords.slice(0,3).join(', ')}) + attachments`;
  } else if (hasReceiptKeywords) {
    confidence = Math.min(75, 35 + (matchedKeywords.length * 10) + (highValueMatches.length * 15));
    if (isBusinessSender) confidence += 15;
    isReceipt = confidence > 55; // Lower threshold for accounting
    reason = `Receipt keywords found (${matchedKeywords.slice(0,3).join(', ')}) - ${isReceipt ? 'likely' : 'possibly'} business expense`;
  } else if (hasRelevantAttachments && isBusinessSender) {
    confidence = 45;
    isReceipt = true; // Trust business senders with attachments
    reason = 'Business sender with attachments - likely contains receipt data';
  } else if (hasRelevantAttachments) {
    confidence = 35;
    isReceipt = false;
    reason = 'Has attachments but unclear if receipt-related';
  } else {
    confidence = 10;
    isReceipt = false;
    reason = 'No clear receipt indicators found';
  }

  // Reduce confidence if exclude keywords present (but don't eliminate completely)
  if (excludeMatches.length > 0 && !hasStrongExcludeKeyword) {
    confidence = Math.max(confidence - (excludeMatches.length * 10), 20);
    reason += ` (reduced confidence due to: ${excludeMatches.join(', ')})`;
  }
  
  const receiptAttachments = isReceipt ? message.attachments.filter(att => 
    att.mimeType.startsWith('image/') || att.mimeType === 'application/pdf'
  ) : [];
  
  console.log(`📊 Fallback analysis: isReceipt=${isReceipt}, confidence=${confidence}, reason=${reason}`);
  
  return {
    messageId: message.id,
    isReceiptEmail: isReceipt,
    confidence,
    reason,
    receiptAttachments
  };
}

/**
 * Batch analyze multiple emails for receipt content
 */
export async function batchAnalyzeEmailsForReceipts(
  messages: GmailMessage[],
  options: {
    minConfidence?: number;
    maxConcurrent?: number;
  } = {}
): Promise<EmailAnalysisResult[]> {
  const { minConfidence = 50, maxConcurrent = 3 } = options;
  
  console.log(`🔍 Batch analyzing ${messages.length} emails for receipts`);
  
  const results: EmailAnalysisResult[] = [];
  
  // Process in batches to avoid overwhelming Gemini API
  for (let i = 0; i < messages.length; i += maxConcurrent) {
    const batch = messages.slice(i, i + maxConcurrent);
    
    const batchPromises = batch.map(message => analyzeEmailForReceipt(message));
    const batchResults = await Promise.allSettled(batchPromises);
    
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        const analysis = result.value;
        // Only include results that meet confidence threshold
        if (analysis.isReceiptEmail && analysis.confidence >= minConfidence) {
          results.push(analysis);
        }
      } else {
        console.error('❌ Failed to analyze email:', result.reason);
      }
    }
    
    // Small delay between batches to be respectful to API
    if (i + maxConcurrent < messages.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  console.log(`✅ Batch analysis complete: ${results.length} receipt emails found from ${messages.length} total`);
  
  return results;
}
