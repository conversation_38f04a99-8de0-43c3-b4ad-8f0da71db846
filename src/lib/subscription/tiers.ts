// Subscription tier definitions and utilities for Reco Engine

export interface TierFeatures {
  receipts_per_month: number;
  google_drive_integration: boolean;
  advanced_data_extraction: boolean;
  enhanced_google_sheets: boolean;
  data_retention_months: number;
  receipt_categorization: boolean;
  search_and_filter: boolean;
  analytics_access: boolean;
  priority_support: boolean;
  gmail_auto_processing: boolean;
}

export interface SubscriptionTier {
  id: 'free' | 'professional' | 'business';
  name: string;
  price_kes: number;
  price_display: string;
  features: TierFeatures;
  popular?: boolean;
  description: string;
  billing_cycle: 'monthly';
}

export const SUBSCRIPTION_TIERS: Record<string, SubscriptionTier> = {
  free: {
    id: 'free',
    name: 'Free',
    price_kes: 0,
    price_display: 'Free',
    description: 'Perfect for getting started with receipt management',
    features: {
      receipts_per_month: 10,
      google_drive_integration: true,
      advanced_data_extraction: true,
      enhanced_google_sheets: true,
      data_retention_months: 1, // 30 days
      receipt_categorization: true,
      search_and_filter: true,
      analytics_access: false,
      priority_support: false,
      gmail_auto_processing: false,
    },
    billing_cycle: 'monthly',
  },
  professional: {
    id: 'professional',
    name: 'Professional',
    price_kes: 4999,
    price_display: 'KES 4,999/month',
    description: 'Ideal for small businesses and freelancers',
    popular: true,
    features: {
      receipts_per_month: 500,
      google_drive_integration: true,
      advanced_data_extraction: true,
      enhanced_google_sheets: true,
      data_retention_months: 6,
      receipt_categorization: true,
      search_and_filter: true,
      analytics_access: true,
      priority_support: true,
      gmail_auto_processing: false,
    },
    billing_cycle: 'monthly',
  },
  business: {
    id: 'business',
    name: 'Business',
    price_kes: 6999,
    price_display: 'KES 6,999/month',
    description: 'Perfect for growing businesses with high volume needs',
    features: {
      receipts_per_month: -1, // unlimited
      google_drive_integration: true,
      advanced_data_extraction: true,
      enhanced_google_sheets: true,
      data_retention_months: 12, // 1 year per Google Sheet year
      receipt_categorization: true,
      search_and_filter: true,
      analytics_access: true,
      priority_support: true,
      gmail_auto_processing: true, // Business tier exclusive feature
    },
    billing_cycle: 'monthly',
  },
};

export function getTierById(tierId: string): SubscriptionTier | null {
  return SUBSCRIPTION_TIERS[tierId] || null;
}

export function getTierFeatures(tierId: string): TierFeatures | null {
  const tier = getTierById(tierId);
  return tier ? tier.features : null;
}

export function canProcessReceipt(
  currentTier: string,
  receiptsUsedThisPeriod: number,
  subscriptionStatus: string,
  marketingOverride?: boolean
): boolean {
  // Marketing override allows unlimited processing
  if (marketingOverride) return true;

  const tier = getTierById(currentTier);
  if (!tier) return false;

  // Check subscription status
  if (currentTier !== 'free' && subscriptionStatus !== 'active') {
    return false;
  }

  // Check receipt limits
  if (tier.features.receipts_per_month === -1) {
    return true; // unlimited
  }

  return receiptsUsedThisPeriod < tier.features.receipts_per_month;
}

export function getRemainingReceipts(
  currentTier: string,
  receiptsUsedThisPeriod: number
): number {
  const tier = getTierById(currentTier);
  if (!tier) return 0;

  if (tier.features.receipts_per_month === -1) {
    return -1; // unlimited
  }

  return Math.max(0, tier.features.receipts_per_month - receiptsUsedThisPeriod);
}

export function hasAnalyticsAccess(currentTier: string, marketingOverride?: boolean): boolean {
  // Marketing override gives access to all features
  if (marketingOverride) return true;

  const tier = getTierById(currentTier);
  return tier ? tier.features.analytics_access : false;
}

export function hasGoogleDriveAccess(currentTier: string, marketingOverride?: boolean): boolean {
  // Marketing override gives access to all features
  if (marketingOverride) return true;

  const tier = getTierById(currentTier);
  return tier ? tier.features.google_drive_integration : false;
}

export function hasGmailAutoProcessingAccess(currentTier: string, marketingOverride?: boolean): boolean {
  // Marketing override gives access to all features
  if (marketingOverride) return true;

  const tier = getTierById(currentTier);
  return tier ? tier.features.gmail_auto_processing : false;
}

export function getDataRetentionMonths(currentTier: string): number {
  const tier = getTierById(currentTier);
  return tier ? tier.features.data_retention_months : 1;
}

export function formatReceiptLimit(limit: number): string {
  if (limit === -1) return 'Unlimited';
  return limit.toString();
}

export function formatPrice(priceKes: number): string {
  if (priceKes === 0) return 'Free';
  return `KES ${priceKes.toLocaleString()}`;
}

export function getTierColor(tierId: string): string {
  switch (tierId) {
    case 'free':
      return 'bg-gray-500/20 text-gray-400';
    case 'professional':
      return 'bg-pink-500/20 text-pink-400';
    case 'business':
      return 'bg-purple-500/20 text-purple-400';
    default:
      return 'bg-gray-500/20 text-gray-400';
  }
}

export function getUpgradeRecommendation(
  currentTier: string,
  receiptsUsedThisPeriod: number,
  totalReceipts: number
): {
  shouldUpgrade: boolean;
  recommendedTier?: string;
  reason?: string;
} {
  const tier = getTierById(currentTier);
  if (!tier) return { shouldUpgrade: false };

  // If unlimited, no upgrade needed
  if (tier.features.receipts_per_month === -1) {
    return { shouldUpgrade: false };
  }

  // Check if approaching limit (80% usage)
  const usagePercentage = receiptsUsedThisPeriod / tier.features.receipts_per_month;
  
  if (usagePercentage >= 0.8) {
    const nextTier = currentTier === 'free' ? 'professional' : 'business';
    return {
      shouldUpgrade: true,
      recommendedTier: nextTier,
      reason: `You've used ${Math.round(usagePercentage * 100)}% of your monthly receipt limit.`
    };
  }

  // Check if consistently hitting limits based on total receipts
  if (totalReceipts > tier.features.receipts_per_month * 2) {
    const nextTier = currentTier === 'free' ? 'professional' : 'business';
    return {
      shouldUpgrade: true,
      recommendedTier: nextTier,
      reason: 'Based on your usage patterns, a higher tier would better suit your needs.'
    };
  }

  return { shouldUpgrade: false };
}

// Utility to check if a tier change is valid
export function isValidTierChange(fromTier: string, toTier: string): boolean {
  const tiers = ['free', 'professional', 'business'];
  const fromIndex = tiers.indexOf(fromTier);
  const toIndex = tiers.indexOf(toTier);
  
  // Can't downgrade to free from paid tiers (business rule)
  if (fromIndex > 0 && toTier === 'free') {
    return false;
  }
  
  return fromIndex !== -1 && toIndex !== -1 && fromIndex !== toIndex;
}

export function getTierChangeType(fromTier: string, toTier: string): 'upgrade' | 'downgrade' | 'invalid' {
  if (!isValidTierChange(fromTier, toTier)) return 'invalid';
  
  const tiers = ['free', 'professional', 'business'];
  const fromIndex = tiers.indexOf(fromTier);
  const toIndex = tiers.indexOf(toTier);
  
  return toIndex > fromIndex ? 'upgrade' : 'downgrade';
}
