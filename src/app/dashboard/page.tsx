import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { getReceiptsForUser } from '@/lib/data/receipts';
import ReceiptsDataTable from '@/components/dashboard/ReceiptsDataTable';
import Link from 'next/link';
import { AnalyticsDashboard } from '@/components/dashboard/analytics/AnalyticsDashboard';
import { getAnalyticsData } from '@/lib/data/analytics';
import { hasAnalyticsAccess } from '@/lib/subscription/tiers';
import DashboardClient from '@/components/dashboard/DashboardClient';

export default async function DashboardPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }

  // Get user's current tier and marketing override
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('current_tier, marketing_override')
    .eq('id', user.id)
    .single();

  const userTier = userData?.current_tier || 'free';
  const marketingOverride = userData?.marketing_override || false;
  const hasAccess = hasAnalyticsAccess(userTier, marketingOverride);

  // Fetch analytics data and receipts in parallel
  const [analyticsData, receipts] = await Promise.all([
    hasAccess ? getAnalyticsData() : Promise.resolve(null),
    getReceiptsForUser(10) // Limit to 10 most recent receipts for dashboard
  ]);

  return (
    <DashboardClient>
      <div>
        <div className="mb-6">
          {hasAccess ? (
            <AnalyticsDashboard data={analyticsData} />
          ) : (
            <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-4">
              <div className="text-center">
                <h3 className="text-base font-semibold text-white mb-2">
                  Analytics Available in Professional Plan
                </h3>
                <p className="text-gray-400 mb-3 text-sm">
                  Upgrade to unlock detailed insights and spending analytics.
                </p>
                <Link
                  href="/dashboard/analytics"
                  className="inline-flex items-center gap-2 bg-pink-600 hover:bg-pink-700 text-white font-medium px-3 py-2 rounded-lg transition-colors text-sm"
                >
                  Learn More
                </Link>
              </div>
            </div>
          )}
        </div>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
          <h1 className="text-xl sm:text-2xl font-bold text-white">Your Receipts</h1>
          <Link href="/dashboard/upload">
            <button className="bg-accent-pink hover:bg-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 text-sm w-full sm:w-auto">
              Upload Receipt
            </button>
          </Link>
        </div>
        <ReceiptsDataTable receipts={receipts} />
      </div>
    </DashboardClient>
  );
}
