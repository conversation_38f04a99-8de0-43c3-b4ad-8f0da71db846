name: Receipt<PERSON>abs Cron Jobs

on:
  schedule:
    # Gmail processing - every hour
    - cron: '0 * * * *'
    # Data retention monitor - daily at 2 AM UTC
    - cron: '0 2 * * *'
    # Data retention cleanup - weekly on Sunday at 3 AM UTC
    - cron: '0 3 * * 0'

  # Allow manual triggering
  workflow_dispatch:
    inputs:
      job_type:
        description: 'Which job to run'
        required: true
        default: 'gmail-processor'
        type: choice
        options:
        - gmail-processor
        - data-retention-monitor
        - data-retention-cleanup
        - all

jobs:
  gmail-processor:
    if: github.event.schedule == '0 * * * *' || github.event.inputs.job_type == 'gmail-processor' || github.event.inputs.job_type == 'all'
    runs-on: ubuntu-latest
    steps:
      - name: Call Gmail Processor
        run: |
          echo "🔄 Starting Gmail Processor..."
          response=$(curl -L -s -w "\n%{http_code}" -L -X GET "${{ secrets.VERCEL_APP_URL }}/api/cron/gmail-processor/" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -H "Content-Type: application/json" \
            -H "User-Agent: GitHub-Actions-Cron/1.0")

          http_code=$(echo "$response" | tail -n1)
          body=$(echo "$response" | head -n -1)

          echo "📊 Response Code: $http_code"
          echo "📄 Response Body: $body"

          if [ "$http_code" -ne 200 ]; then
            echo "❌ Gmail Processor failed with code $http_code"
            echo "🔍 Full response: $response"
            exit 1
          else
            echo "✅ Gmail Processor completed successfully"
          fi

  data-retention-monitor:
    if: github.event.schedule == '0 2 * * *' || github.event.inputs.job_type == 'data-retention-monitor' || github.event.inputs.job_type == 'all'
    runs-on: ubuntu-latest
    steps:
      - name: Call Data Retention Monitor
        run: |
          echo "🔍 Starting Data Retention Monitor..."
          response=$(curl -s -w "\n%{http_code}" -L -X GET "${{ secrets.VERCEL_APP_URL }}/api/cron/data-retention-monitor" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -H "Content-Type: application/json" \
            -H "User-Agent: GitHub-Actions-Cron/1.0")

          http_code=$(echo "$response" | tail -n1)
          body=$(echo "$response" | head -n -1)

          echo "📊 Response Code: $http_code"
          echo "📄 Response Body: $body"

          if [ "$http_code" -ne 200 ]; then
            echo "❌ Data Retention Monitor failed with code $http_code"
            echo "🔍 Full response: $response"
            exit 1
          else
            echo "✅ Data Retention Monitor completed successfully"
          fi

  data-retention-cleanup:
    if: github.event.schedule == '0 3 * * 0' || github.event.inputs.job_type == 'data-retention-cleanup' || github.event.inputs.job_type == 'all'
    runs-on: ubuntu-latest
    steps:
      - name: Call Data Retention Cleanup
        run: |
          echo "🧹 Starting Data Retention Cleanup..."
          response=$(curl -s -w "\n%{http_code}" -L -X GET "${{ secrets.VERCEL_APP_URL }}/api/cron/data-retention-cleanup" \
            -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
            -H "Content-Type: application/json" \
            -H "User-Agent: GitHub-Actions-Cron/1.0")

          http_code=$(echo "$response" | tail -n1)
          body=$(echo "$response" | head -n -1)

          echo "📊 Response Code: $http_code"
          echo "📄 Response Body: $body"

          if [ "$http_code" -ne 200 ]; then
            echo "❌ Data Retention Cleanup failed with code $http_code"
            echo "🔍 Full response: $response"
            exit 1
          else
            echo "✅ Data Retention Cleanup completed successfully"
          fi
