import { createClient } from '@/lib/supabase/server';

export interface GmailProcessingLog {
  id?: string;
  user_id: string;
  processing_date: string;
  emails_processed: number;
  receipts_found: number;
  receipts_created: number;
  processing_time_ms: number;
  success: boolean;
  errors?: string[];
  source: 'manual' | 'cron' | 'api';
  created_at?: string;
}

export interface GmailProcessingStats {
  totalUsers: number;
  activeUsers: number;
  totalEmailsProcessed: number;
  totalReceiptsFound: number;
  totalReceiptsCreated: number;
  averageProcessingTime: number;
  successRate: number;
  lastProcessingDate: string;
}

/**
 * Log Gmail processing results for monitoring and analytics
 */
export async function logGmailProcessing(log: GmailProcessingLog): Promise<void> {
  try {
    const supabase = await createClient();
    
    const { error } = await supabase
      .from('gmail_processing_logs')
      .insert({
        user_id: log.user_id,
        processing_date: log.processing_date,
        emails_processed: log.emails_processed,
        receipts_found: log.receipts_found,
        receipts_created: log.receipts_created,
        processing_time_ms: log.processing_time_ms,
        success: log.success,
        errors: log.errors ? JSON.stringify(log.errors) : null,
        source: log.source
      });

    if (error) {
      console.error('Failed to log Gmail processing:', error);
    }
  } catch (error) {
    console.error('Error logging Gmail processing:', error);
  }
}

/**
 * Get Gmail processing statistics for monitoring dashboard
 */
export async function getGmailProcessingStats(
  dateFrom?: Date,
  dateTo?: Date
): Promise<GmailProcessingStats> {
  try {
    const supabase = await createClient();
    
    // Default to last 30 days if no dates provided
    const defaultDateFrom = new Date();
    defaultDateFrom.setDate(defaultDateFrom.getDate() - 30);
    
    const fromDate = dateFrom || defaultDateFrom;
    const toDate = dateTo || new Date();
    
    // Get processing logs within date range
    const { data: logs, error } = await supabase
      .from('gmail_processing_logs')
      .select('*')
      .gte('processing_date', fromDate.toISOString().split('T')[0])
      .lte('processing_date', toDate.toISOString().split('T')[0])
      .order('processing_date', { ascending: false });

    if (error) {
      throw error;
    }

    if (!logs || logs.length === 0) {
      return {
        totalUsers: 0,
        activeUsers: 0,
        totalEmailsProcessed: 0,
        totalReceiptsFound: 0,
        totalReceiptsCreated: 0,
        averageProcessingTime: 0,
        successRate: 0,
        lastProcessingDate: 'Never'
      };
    }

    // Calculate statistics
    const uniqueUsers = new Set(logs.map(log => log.user_id));
    const totalEmails = logs.reduce((sum, log) => sum + (log.emails_processed || 0), 0);
    const totalReceiptsFound = logs.reduce((sum, log) => sum + (log.receipts_found || 0), 0);
    const totalReceiptsCreated = logs.reduce((sum, log) => sum + (log.receipts_created || 0), 0);
    const totalProcessingTime = logs.reduce((sum, log) => sum + (log.processing_time_ms || 0), 0);
    const successfulRuns = logs.filter(log => log.success).length;
    
    return {
      totalUsers: uniqueUsers.size,
      activeUsers: uniqueUsers.size, // Users who had processing in the period
      totalEmailsProcessed: totalEmails,
      totalReceiptsFound: totalReceiptsFound,
      totalReceiptsCreated: totalReceiptsCreated,
      averageProcessingTime: logs.length > 0 ? Math.round(totalProcessingTime / logs.length) : 0,
      successRate: logs.length > 0 ? Math.round((successfulRuns / logs.length) * 100) : 0,
      lastProcessingDate: logs[0]?.processing_date || 'Never'
    };

  } catch (error) {
    console.error('Error getting Gmail processing stats:', error);
    throw error;
  }
}

/**
 * Get recent Gmail processing logs for monitoring
 */
export async function getRecentGmailProcessingLogs(
  limit: number = 50
): Promise<GmailProcessingLog[]> {
  try {
    const supabase = await createClient();
    
    const { data: logs, error } = await supabase
      .from('gmail_processing_logs')
      .select(`
        *,
        users!inner(email)
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    return logs?.map(log => ({
      ...log,
      errors: log.errors ? JSON.parse(log.errors) : undefined
    })) || [];

  } catch (error) {
    console.error('Error getting recent Gmail processing logs:', error);
    throw error;
  }
}

/**
 * Get Gmail processing status for a specific user
 */
export async function getUserGmailProcessingStatus(userId: string): Promise<{
  lastProcessed: string | null;
  totalProcessed: number;
  totalReceipts: number;
  averageProcessingTime: number;
  recentErrors: string[];
}> {
  try {
    const supabase = await createClient();
    
    // Get user's processing logs from last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const { data: logs, error } = await supabase
      .from('gmail_processing_logs')
      .select('*')
      .eq('user_id', userId)
      .gte('processing_date', thirtyDaysAgo.toISOString().split('T')[0])
      .order('processing_date', { ascending: false });

    if (error) {
      throw error;
    }

    if (!logs || logs.length === 0) {
      return {
        lastProcessed: null,
        totalProcessed: 0,
        totalReceipts: 0,
        averageProcessingTime: 0,
        recentErrors: []
      };
    }

    const totalEmails = logs.reduce((sum, log) => sum + (log.emails_processed || 0), 0);
    const totalReceipts = logs.reduce((sum, log) => sum + (log.receipts_created || 0), 0);
    const totalTime = logs.reduce((sum, log) => sum + (log.processing_time_ms || 0), 0);
    
    // Get recent errors (last 5 failed runs)
    const recentErrors = logs
      .filter(log => !log.success && log.errors)
      .slice(0, 5)
      .map(log => JSON.parse(log.errors))
      .flat();

    return {
      lastProcessed: logs[0]?.processing_date || null,
      totalProcessed: totalEmails,
      totalReceipts: totalReceipts,
      averageProcessingTime: logs.length > 0 ? Math.round(totalTime / logs.length) : 0,
      recentErrors: recentErrors.slice(0, 10) // Limit to 10 most recent errors
    };

  } catch (error) {
    console.error('Error getting user Gmail processing status:', error);
    throw error;
  }
}

/**
 * Check Gmail processing health and send alerts if needed
 */
export async function checkGmailProcessingHealth(): Promise<{
  healthy: boolean;
  issues: string[];
  recommendations: string[];
}> {
  try {
    const stats = await getGmailProcessingStats();
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check success rate
    if (stats.successRate < 90) {
      issues.push(`Low success rate: ${stats.successRate}%`);
      recommendations.push('Review error logs and check Gmail API quotas');
    }

    // Check if processing is happening
    const lastProcessing = new Date(stats.lastProcessingDate);
    const daysSinceLastProcessing = Math.floor(
      (Date.now() - lastProcessing.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysSinceLastProcessing > 2) {
      issues.push(`No processing in ${daysSinceLastProcessing} days`);
      recommendations.push('Check cron job configuration and Vercel deployment');
    }

    // Check average processing time
    if (stats.averageProcessingTime > 60000) { // 1 minute
      issues.push(`Slow processing: ${Math.round(stats.averageProcessingTime / 1000)}s average`);
      recommendations.push('Optimize Gmail API calls and consider reducing batch sizes');
    }

    return {
      healthy: issues.length === 0,
      issues,
      recommendations
    };

  } catch (error) {
    console.error('Error checking Gmail processing health:', error);
    return {
      healthy: false,
      issues: ['Failed to check processing health'],
      recommendations: ['Check monitoring system and database connectivity']
    };
  }
}
