'use client'

import { useState, useEffect } from 'react'
import { FileSpreadsheet, Loader2, AlertCircle } from 'lucide-react'

interface GoogleSheetPickerProps {
  onSheetSelected: (sheetId: string, sheetName: string, sheetUrl: string) => void
  onCancel: () => void
  accessToken: string
  isOpen: boolean
}

interface PickerSheet {
  id: string
  name: string
  url: string
  modifiedTime: string
}

declare global {
  interface Window {
    gapi: any
    google: any
  }
}

export default function GoogleSheetPicker({ 
  onSheetSelected, 
  onCancel, 
  accessToken, 
  isOpen 
}: GoogleSheetPickerProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [sheets, setSheets] = useState<PickerSheet[]>([])
  const [gapiLoaded, setGapiLoaded] = useState(false)

  // Load Google APIs
  useEffect(() => {
    if (!isOpen) return

    const loadGoogleAPIs = async () => {
      try {
        // Load Google APIs script if not already loaded
        if (!window.gapi) {
          await new Promise((resolve, reject) => {
            const script = document.createElement('script')
            script.src = 'https://apis.google.com/js/api.js'
            script.onload = resolve
            script.onerror = reject
            document.head.appendChild(script)
          })
        }

        // Initialize gapi
        await new Promise((resolve) => {
          window.gapi.load('client', resolve)
        })

        // Initialize the client
        await window.gapi.client.init({
          apiKey: process.env.NEXT_PUBLIC_GOOGLE_API_KEY,
          discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest']
        })

        setGapiLoaded(true)
      } catch (err) {
        console.error('Failed to load Google APIs:', err)
        setError('Failed to load Google APIs')
      }
    }

    loadGoogleAPIs()
  }, [isOpen])

  // Load user's Google Sheets
  useEffect(() => {
    if (!gapiLoaded || !accessToken || !isOpen) return

    const loadSheets = async () => {
      setLoading(true)
      setError(null)

      try {
        // Set the access token
        window.gapi.client.setToken({ access_token: accessToken })

        // Query for Google Sheets files
        const response = await window.gapi.client.drive.files.list({
          q: "mimeType='application/vnd.google-apps.spreadsheet' and trashed=false",
          fields: 'files(id, name, webViewLink, modifiedTime)',
          orderBy: 'modifiedTime desc',
          pageSize: 50
        })

        const files = response.result.files || []
        const sheetList: PickerSheet[] = files.map((file: any) => ({
          id: file.id,
          name: file.name,
          url: file.webViewLink,
          modifiedTime: file.modifiedTime
        }))

        setSheets(sheetList)
      } catch (err) {
        console.error('Failed to load Google Sheets:', err)
        setError('Failed to load your Google Sheets. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    loadSheets()
  }, [gapiLoaded, accessToken, isOpen])

  const handleSheetSelect = (sheet: PickerSheet) => {
    onSheetSelected(sheet.id, sheet.name, sheet.url)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-gray-800 rounded-xl p-6 w-full max-w-2xl max-h-[80vh] overflow-hidden">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">Select Existing Google Sheet</h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg flex items-center gap-2">
            <AlertCircle className="w-5 h-5 text-red-400" />
            <span className="text-red-300">{error}</span>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-blue-400" />
            <span className="ml-3 text-gray-300">Loading your Google Sheets...</span>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-gray-400 text-sm">
              Select an existing Google Sheet to use for your receipts. Note: You can only select sheets that you have access to with the current permissions.
            </p>
            
            {sheets.length === 0 ? (
              <div className="text-center py-8">
                <FileSpreadsheet className="w-12 h-12 text-gray-500 mx-auto mb-3" />
                <p className="text-gray-400">No Google Sheets found in your account.</p>
                <button
                  onClick={onCancel}
                  className="mt-4 bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg transition-colors"
                >
                  Create New Sheet Instead
                </button>
              </div>
            ) : (
              <div className="max-h-96 overflow-y-auto space-y-2">
                {sheets.map((sheet) => (
                  <button
                    key={sheet.id}
                    onClick={() => handleSheetSelect(sheet)}
                    className="w-full p-4 bg-gray-700/50 hover:bg-gray-700 border border-gray-600 rounded-lg transition-colors text-left"
                  >
                    <div className="flex items-center gap-3">
                      <FileSpreadsheet className="w-5 h-5 text-green-400 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <h3 className="text-white font-medium truncate">{sheet.name}</h3>
                        <p className="text-gray-400 text-sm">
                          Last modified: {formatDate(sheet.modifiedTime)}
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}

            <div className="flex justify-end gap-3 pt-4 border-t border-gray-700">
              <button
                onClick={onCancel}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
