import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Get the current user
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get all unique vendors/companies for the user
    const { data: receipts, error } = await supabase
      .from('receipts')
      .select('vendor')
      .eq('user_id', user.id)
      .eq('processing_status', 'completed')
      .not('vendor', 'is', null)
      .not('vendor', 'eq', '');

    if (error) {
      console.error('Error fetching companies:', error);
      return NextResponse.json(
        { error: 'Failed to fetch companies' },
        { status: 500 }
      );
    }

    // Extract unique vendors and sort them
    const uniqueVendors = Array.from(
      new Set(receipts?.map(r => r.vendor).filter(Boolean))
    ).sort();

    return NextResponse.json({
      companies: uniqueVendors,
      count: uniqueVendors.length
    });

  } catch (error) {
    console.error('Error in companies API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
