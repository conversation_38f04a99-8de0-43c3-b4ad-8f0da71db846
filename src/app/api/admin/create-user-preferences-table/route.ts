import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Create the user_preferences table
    const createTableSQL = `
      -- Create user_preferences table
      CREATE TABLE IF NOT EXISTS user_preferences (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          default_currency VARCHAR(3) DEFAULT 'KES' NOT NULL,
          auto_process_receipts BOOLEAN DEFAULT true NOT NULL,
          confidence_threshold DECIMAL(3,2) DEFAULT 0.8 NOT NULL CHECK (confidence_threshold >= 0.5 AND confidence_threshold <= 0.95),
          notification_email BOOLEAN DEFAULT true NOT NULL,
          notification_processing BOOLEAN DEFAULT true NOT NULL,
          data_retention_months INTEGER DEFAULT 36 NOT NULL CHECK (data_retention_months = -1 OR data_retention_months >= 1),
          export_format VARCHAR(10) DEFAULT 'csv' NOT NULL CHECK (export_format IN ('csv', 'json', 'xlsx')),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
          UNIQUE(user_id)
      );
    `;

    const { error: tableError } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    
    if (tableError) {
      console.error('Error creating table:', tableError);
      // Try alternative approach
      const { error: altError } = await supabase
        .from('user_preferences')
        .select('id')
        .limit(1);
      
      if (altError && altError.code === '42P01') {
        // Table doesn't exist, let's create it using a simpler approach
        return NextResponse.json({
          success: false,
          error: 'Table creation failed. Please create the user_preferences table manually.',
          sql: createTableSQL
        });
      }
    }

    // Create index
    const createIndexSQL = `CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);`;
    await supabase.rpc('exec_sql', { sql: createIndexSQL });

    // Enable RLS
    const enableRLSSQL = `ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;`;
    await supabase.rpc('exec_sql', { sql: enableRLSSQL });

    // Create policies
    const policies = [
      `CREATE POLICY IF NOT EXISTS "Users can view own preferences" ON user_preferences FOR SELECT USING (auth.uid() = user_id);`,
      `CREATE POLICY IF NOT EXISTS "Users can insert own preferences" ON user_preferences FOR INSERT WITH CHECK (auth.uid() = user_id);`,
      `CREATE POLICY IF NOT EXISTS "Users can update own preferences" ON user_preferences FOR UPDATE USING (auth.uid() = user_id);`,
      `CREATE POLICY IF NOT EXISTS "Users can delete own preferences" ON user_preferences FOR DELETE USING (auth.uid() = user_id);`
    ];

    for (const policy of policies) {
      await supabase.rpc('exec_sql', { sql: policy });
    }

    // Create trigger function
    const triggerFunctionSQL = `
      CREATE OR REPLACE FUNCTION update_user_preferences_updated_at()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `;
    await supabase.rpc('exec_sql', { sql: triggerFunctionSQL });

    // Create trigger
    const triggerSQL = `
      DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON user_preferences;
      CREATE TRIGGER update_user_preferences_updated_at
          BEFORE UPDATE ON user_preferences
          FOR EACH ROW
          EXECUTE FUNCTION update_user_preferences_updated_at();
    `;
    await supabase.rpc('exec_sql', { sql: triggerSQL });

    return NextResponse.json({
      success: true,
      message: 'user_preferences table created successfully'
    });

  } catch (error) {
    console.error('Error creating user_preferences table:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create table',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
