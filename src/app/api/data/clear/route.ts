import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    const { dataType } = await request.json();
    
    if (!['processing_history', 'all_receipts'].includes(dataType)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid data type' 
      }, { status: 400 });
    }

    switch (dataType) {
      case 'processing_history':
        await clearProcessingHistory(supabase, user.id);
        break;
      
      case 'all_receipts':
        await clearAllReceipts(supabase, user.id);
        break;
    }

    return NextResponse.json({
      success: true,
      message: `${dataType.replace('_', ' ')} cleared successfully`
    });

  } catch (error) {
    console.error('Clear data API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

async function clearProcessingHistory(supabase: any, userId: string) {
  // Clear error messages and processing metadata while keeping receipt data
  const { error } = await supabase
    .from('receipts')
    .update({
      error_message: null,
      redis_job_id: null,
      confidence_score: null,
      extraction_method: null,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId);

  if (error) {
    throw new Error(`Failed to clear processing history: ${error.message}`);
  }
}

async function clearAllReceipts(supabase: any, userId: string) {
  // Get all user's receipts to delete files
  const { data: receipts } = await supabase
    .from('receipts')
    .select('file_path')
    .eq('user_id', userId);

  // Delete all receipt files from storage
  if (receipts && receipts.length > 0) {
    const filePaths = receipts.map((r: any) => r.file_path);
    const { error: storageError } = await supabase.storage
      .from('receipts')
      .remove(filePaths);

    if (storageError) {
      console.warn('Error deleting files from storage:', storageError);
      // Don't fail the entire operation for storage errors
    }
  }

  // Get all receipt IDs for the user first
  const { data: userReceipts } = await supabase
    .from('receipts')
    .select('id')
    .eq('user_id', userId);

  // Delete receipt items first (foreign key constraint)
  if (userReceipts && userReceipts.length > 0) {
    const receiptIds = userReceipts.map((r: any) => r.id);
    const { error: itemsError } = await supabase
      .from('receipt_items')
      .delete()
      .in('receipt_id', receiptIds);

    if (itemsError) {
      throw new Error(`Failed to delete receipt items: ${itemsError.message}`);
    }
  }

  // Delete receipts
  const { error: receiptsError } = await supabase
    .from('receipts')
    .delete()
    .eq('user_id', userId);

  if (receiptsError) {
    throw new Error(`Failed to delete receipts: ${receiptsError.message}`);
  }

  // Reset user's receipt count
  const { error: userError } = await supabase
    .from('users')
    .update({ 
      receipts_processed: 0,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId);

  if (userError) {
    console.warn('Error resetting user receipt count:', userError);
  }
}
