# Data Retention Cron Jobs for ReceiptLabs

This document describes the automated data retention system for ReceiptLabs, which monitors and cleans up expired receipts based on subscription tier policies.

## Overview

ReceiptLabs implements a tiered data retention policy:

- **Free Tier**: 30 days (1 month) retention
- **Professional Tier**: 6 months retention  
- **Business Tier**: 12 months retention

The system uses automated cron jobs to monitor and clean up expired data according to these policies.

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Cron Jobs     │───▶│  Supabase Edge   │───▶│   Database      │
│                 │    │   Functions      │    │   & Storage     │
├─────────────────┤    ├──────────────────┤    ├─────────────────┤
│ Monitor (Daily) │    │ data-retention-  │    │ receipts table  │
│ Cleanup (Weekly)│    │ monitor          │    │ receipt_items   │
│ Report (Monthly)│    │ data-retention-  │    │ file storage    │
└─────────────────┘    │ cleanup          │    └─────────────────┘
                       └──────────────────┘
```

## Cron Jobs

### 1. Data Retention Monitor
- **Schedule**: Daily at 2:00 AM (`0 2 * * *`)
- **Function**: `data-retention-monitor`
- **Purpose**: Monitor data retention status without deleting data
- **Output**: Generates reports on expired receipts per tier

### 2. Data Retention Cleanup
- **Schedule**: Weekly on Sundays at 3:00 AM (`0 3 * * 0`)
- **Function**: `data-retention-cleanup`
- **Purpose**: Actually delete expired receipts and files
- **Safety**: Includes comprehensive logging and error handling

### 3. Data Retention Report (Future)
- **Schedule**: Monthly on 1st at 9:00 AM (`0 9 1 * *`)
- **Function**: `data-retention-report`
- **Purpose**: Generate monthly summary reports for admin review

## Setup Instructions

### 1. Automatic Setup (Recommended)

Run the setup script:

```bash
# Make the script executable
chmod +x scripts/setup-data-retention-cron.sh

# Run as root for system-wide setup
sudo ./scripts/setup-data-retention-cron.sh

# Or run as user for user-specific setup
./scripts/setup-data-retention-cron.sh
```

### 2. Manual Setup

#### Deploy Supabase Edge Functions

```bash
# Deploy the monitoring function
supabase functions deploy data-retention-monitor

# Deploy the cleanup function
supabase functions deploy data-retention-cleanup
```

#### Create Cron Jobs

Add to your crontab (`crontab -e`):

```bash
# ReceiptLabs Data Retention
0 2 * * * /usr/local/bin/receiptlabs/data-retention-monitor.sh
0 3 * * 0 /usr/local/bin/receiptlabs/data-retention-cleanup.sh
```

## Configuration

### Environment Variables

Set these in your environment or `.env` file:

```bash
# Required
DATA_RETENTION_API_KEY=your-secure-api-key
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Optional
ADMIN_EMAIL=<EMAIL>
RECEIPTLABS_URL=https://yourdomain.com
LOG_LEVEL=INFO
```

### API Key Security

The data retention API is protected by an API key. Set a strong key:

```bash
# Generate a secure API key
openssl rand -hex 32

# Set in your environment
export DATA_RETENTION_API_KEY="your-generated-key"
```

## Monitoring and Logs

### Log Locations

- **System setup**: `/var/log/reco-engine/`
- **User setup**: `~/.local/log/reco-engine/`

### Log Files

- `data-retention-monitor.log` - Daily monitoring logs
- `data-retention-cleanup.log` - Weekly cleanup logs

### Log Rotation

Logs are automatically rotated daily and kept for 30 days.

## API Endpoints

### Manual Execution

```bash
# Monitor data retention status
curl -X POST \
  -H "Authorization: Bearer $DATA_RETENTION_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"action":"monitor"}' \
  https://your-project.supabase.co/functions/v1/data-retention-monitor

# Execute cleanup (dry run)
curl -X POST \
  -H "Authorization: Bearer $DATA_RETENTION_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"action":"cleanup","dryRun":true}' \
  https://your-project.supabase.co/functions/v1/data-retention-cleanup
```

### Admin Dashboard

Access via your application:

```bash
# Get cron job status
GET /api/cron/data-retention

# Execute specific job
POST /api/cron/data-retention
{
  "jobName": "data-retention-monitor"
}

# Execute all jobs
POST /api/cron/data-retention
{
  "executeAll": true
}
```

## Tier-Specific Behavior

### Free Tier (1 Month Retention)
- Receipts older than 30 days are deleted
- Most aggressive cleanup to manage storage costs
- Users notified when approaching limits

### Professional Tier (6 Months Retention)
- Receipts older than 6 months are deleted
- Balanced retention for business needs
- Analytics data preserved longer

### Business Tier (12 Months Retention)
- Receipts older than 12 months are deleted
- Extended retention for compliance
- Full feature access maintained

## Safety Features

### Dry Run Mode
All cleanup operations support dry run mode for testing:

```bash
# Test cleanup without deleting
curl -X POST \
  -H "Authorization: Bearer $API_KEY" \
  -d '{"dryRun": true}' \
  /functions/v1/data-retention-cleanup
```

### Comprehensive Logging
- All operations are logged with timestamps
- Error conditions are captured and reported
- Success metrics are tracked

### Gradual Rollout
- Monitor first, cleanup later
- Weekly cleanup reduces system load
- Monthly reporting for oversight

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Fix script permissions
   chmod +x /usr/local/bin/reco-engine/*.sh
   ```

2. **API Key Issues**
   ```bash
   # Verify API key is set
   echo $DATA_RETENTION_API_KEY
   ```

3. **Supabase Connection**
   ```bash
   # Test Supabase connectivity
   curl -H "Authorization: Bearer $SUPABASE_SERVICE_ROLE_KEY" \
        $SUPABASE_URL/rest/v1/users?select=count
   ```

### Debug Mode

Enable debug logging:

```bash
# Set debug environment
export LOG_LEVEL=DEBUG

# Run script manually
/usr/local/bin/reco-engine/data-retention-monitor.sh
```

## Maintenance

### Regular Tasks

1. **Weekly**: Review cleanup logs
2. **Monthly**: Check storage usage trends
3. **Quarterly**: Review retention policies
4. **Annually**: Update API keys

### Monitoring Alerts

Set up alerts for:
- Failed cleanup operations
- Unusual deletion volumes
- API authentication failures
- Storage usage spikes

## Security Considerations

1. **API Key Rotation**: Rotate keys quarterly
2. **Log Access**: Restrict log file permissions
3. **Function Access**: Use service role keys only
4. **Audit Trail**: Maintain deletion logs for compliance

## Performance Impact

- **Monitor Job**: Low impact, read-only operations
- **Cleanup Job**: Moderate impact during execution
- **Scheduling**: Off-peak hours to minimize user impact
- **Batch Processing**: Processes users in batches to avoid timeouts
