// Data retention service for Reco Engine
// Handles automatic cleanup of old receipts based on subscription tiers

import { createClient } from '@/lib/supabase/server';
import { getDataRetentionMonths } from '@/lib/subscription/tiers';

export interface DataRetentionResult {
  success: boolean;
  deletedReceipts: number;
  deletedItems: number;
  errors: string[];
}

export class DataRetentionService {
  private async getSupabase() {
    return await createClient();
  }

  // Clean up old receipts for all users based on their tier retention policies
  async cleanupExpiredData(): Promise<DataRetentionResult> {
    const result: DataRetentionResult = {
      success: true,
      deletedReceipts: 0,
      deletedItems: 0,
      errors: []
    };

    try {
      // Get all users with their retention settings
      const supabase = await this.getSupabase();
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select('id, current_tier, data_retention_months')
        .neq('data_retention_months', -1); // Skip users with unlimited retention

      if (usersError) {
        result.errors.push(`Failed to fetch users: ${usersError.message}`);
        result.success = false;
        return result;
      }

      if (!users || users.length === 0) {
        return result; // No users to process
      }

      // Process each user
      for (const user of users) {
        try {
          const userResult = await this.cleanupUserData(
            user.id, 
            user.data_retention_months || getDataRetentionMonths(user.current_tier)
          );
          
          result.deletedReceipts += userResult.deletedReceipts;
          result.deletedItems += userResult.deletedItems;
          result.errors.push(...userResult.errors);
          
          if (!userResult.success) {
            result.success = false;
          }
        } catch (error) {
          const errorMsg = `Failed to cleanup data for user ${user.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
          result.success = false;
        }
      }

      console.log('Data retention cleanup completed:', {
        deletedReceipts: result.deletedReceipts,
        deletedItems: result.deletedItems,
        errors: result.errors.length
      });

      return result;

    } catch (error) {
      result.errors.push(`Data retention cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      result.success = false;
      return result;
    }
  }

  // Clean up old receipts for a specific user
  async cleanupUserData(userId: string, retentionMonths: number): Promise<DataRetentionResult> {
    const result: DataRetentionResult = {
      success: true,
      deletedReceipts: 0,
      deletedItems: 0,
      errors: []
    };

    try {
      // Calculate cutoff date
      const cutoffDate = new Date();
      cutoffDate.setMonth(cutoffDate.getMonth() - retentionMonths);

      // Get receipts older than retention period
      const { data: expiredReceipts, error: receiptsError } = await (await this.supabase)
        .from('receipts')
        .select('id, file_path')
        .eq('user_id', userId)
        .lt('created_at', cutoffDate.toISOString());

      if (receiptsError) {
        result.errors.push(`Failed to fetch expired receipts for user ${userId}: ${receiptsError.message}`);
        result.success = false;
        return result;
      }

      if (!expiredReceipts || expiredReceipts.length === 0) {
        return result; // No expired receipts
      }

      const receiptIds = expiredReceipts.map(r => r.id);

      // Delete receipt items first (foreign key constraint)
      const { error: itemsError } = await (await this.supabase)
        .from('receipt_items')
        .delete()
        .in('receipt_id', receiptIds);

      if (itemsError) {
        result.errors.push(`Failed to delete receipt items for user ${userId}: ${itemsError.message}`);
        result.success = false;
        return result;
      }

      // Delete receipts
      const { error: receiptsDeleteError } = await (await this.supabase)
        .from('receipts')
        .delete()
        .in('id', receiptIds);

      if (receiptsDeleteError) {
        result.errors.push(`Failed to delete receipts for user ${userId}: ${receiptsDeleteError.message}`);
        result.success = false;
        return result;
      }

      // Delete files from storage
      const filePaths = expiredReceipts
        .map(r => r.file_path)
        .filter(path => path && path.trim() !== '');

      if (filePaths.length > 0) {
        try {
          const { error: storageError } = await (await this.supabase)
            .storage
            .from('receipts')
            .remove(filePaths);

          if (storageError) {
            result.errors.push(`Failed to delete storage files for user ${userId}: ${storageError.message}`);
            // Don't mark as failed since database cleanup succeeded
          }
        } catch (storageError) {
          result.errors.push(`Storage cleanup error for user ${userId}: ${storageError instanceof Error ? storageError.message : 'Unknown error'}`);
        }
      }

      result.deletedReceipts = expiredReceipts.length;
      result.deletedItems = receiptIds.length; // Approximate, actual count would require a separate query

      console.log(`Cleaned up ${result.deletedReceipts} receipts for user ${userId}`);

      return result;

    } catch (error) {
      result.errors.push(`User data cleanup failed for ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      result.success = false;
      return result;
    }
  }

  // Get data retention info for a user
  async getUserRetentionInfo(userId: string): Promise<{
    retentionMonths: number;
    cutoffDate: Date;
    expiredReceiptsCount: number;
  } | null> {
    try {
      // Get user's retention setting
      const { data: user, error: userError } = await (await this.supabase)
        .from('users')
        .select('current_tier, data_retention_months')
        .eq('id', userId)
        .single();

      if (userError || !user) {
        return null;
      }

      const retentionMonths = user.data_retention_months || getDataRetentionMonths(user.current_tier);
      
      // If unlimited retention
      if (retentionMonths === -1) {
        return {
          retentionMonths: -1,
          cutoffDate: new Date(0), // Beginning of time
          expiredReceiptsCount: 0
        };
      }

      // Calculate cutoff date
      const cutoffDate = new Date();
      cutoffDate.setMonth(cutoffDate.getMonth() - retentionMonths);

      // Count expired receipts
      const { count, error: countError } = await (await this.supabase)
        .from('receipts')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .lt('created_at', cutoffDate.toISOString());

      if (countError) {
        console.error('Error counting expired receipts:', countError);
        return null;
      }

      return {
        retentionMonths,
        cutoffDate,
        expiredReceiptsCount: count || 0
      };

    } catch (error) {
      console.error('Error getting user retention info:', error);
      return null;
    }
  }

  // Preview what would be deleted for a user (dry run)
  async previewCleanup(userId: string): Promise<{
    receiptsToDelete: number;
    oldestReceiptDate: string | null;
    newestReceiptDate: string | null;
    totalSizeBytes: number;
  } | null> {
    try {
      const retentionInfo = await this.getUserRetentionInfo(userId);
      if (!retentionInfo || retentionInfo.retentionMonths === -1) {
        return {
          receiptsToDelete: 0,
          oldestReceiptDate: null,
          newestReceiptDate: null,
          totalSizeBytes: 0
        };
      }

      const { data: expiredReceipts, error } = await (await this.supabase)
        .from('receipts')
        .select('created_at, file_size')
        .eq('user_id', userId)
        .lt('created_at', retentionInfo.cutoffDate.toISOString())
        .order('created_at', { ascending: true });

      if (error || !expiredReceipts) {
        return null;
      }

      const totalSize = expiredReceipts.reduce((sum, receipt) => sum + (receipt.file_size || 0), 0);

      return {
        receiptsToDelete: expiredReceipts.length,
        oldestReceiptDate: expiredReceipts[0]?.created_at || null,
        newestReceiptDate: expiredReceipts[expiredReceipts.length - 1]?.created_at || null,
        totalSizeBytes: totalSize
      };

    } catch (error) {
      console.error('Error previewing cleanup:', error);
      return null;
    }
  }
}

// Export singleton instance
export const dataRetentionService = new DataRetentionService();
