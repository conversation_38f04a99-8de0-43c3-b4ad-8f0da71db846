import { Metadata } from 'next'
import Link from 'next/link'
import { ArrowLeft, Calendar, Clock, User, CheckCircle } from 'lucide-react'

export const metadata: Metadata = {
  title: "5 Essential Expense Management Tips for Small Business Owners | ReceiptLabs",
  description: "Learn how to streamline your expense tracking and save hours every month with these proven strategies for small business owners.",
  keywords: "expense management tips, small business expenses, receipt tracking, business accounting, expense automation",
}

export default function ExpenseManagementTipsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-4 sm:py-6">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <Link href="/">
              <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-32 sm:h-24 w-auto" />
            </Link>
          </div>
          <div className="flex items-center space-x-4">
            <Link
              href="/blog"
              className="flex items-center gap-2 text-gray-300 hover:text-white transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Blog
            </Link>
            <Link
              href="/login"
              className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
            >
              Sign In
            </Link>
          </div>
        </div>
      </header>

      {/* Article */}
      <article className="relative z-10 px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Article Header */}
          <header className="mb-8">
            <div className="flex flex-wrap items-center gap-4 mb-6 text-sm text-gray-400">
              <span className="bg-pink-500/20 text-pink-300 px-3 py-1 rounded-full">
                Expense Management
              </span>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                January 15, 2025
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                5 min read
              </div>
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                ReceiptLabs Team
              </div>
            </div>
            
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              5 Essential Expense Management Tips for Small Business Owners
            </h1>
            
            <p className="text-lg sm:text-xl text-gray-300 leading-relaxed">
              Learn how to streamline your expense tracking and save hours every month with these proven strategies that successful small business owners use to stay organized and compliant.
            </p>
          </header>

          {/* Article Content */}
          <div className="prose prose-lg prose-invert max-w-none">
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-2xl p-6 sm:p-8 mb-8">
              <h2 className="text-2xl font-bold text-white mb-4">Why Expense Management Matters</h2>
              <p className="text-gray-300 mb-4">
                Effective expense management is crucial for small business success. Poor expense tracking can lead to missed tax deductions, 
                cash flow problems, and compliance issues. The good news? With the right strategies and tools, you can transform this 
                time-consuming task into an automated, efficient process.
              </p>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">1. Automate Receipt Processing</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Stop manually entering receipt data. Modern AI-powered tools can extract vendor information, dates, amounts, 
                and categories from receipts with 99% accuracy in seconds.
              </p>
              <div className="flex items-start gap-3 mb-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-gray-300">Use receipt scanning apps that integrate with your accounting software</span>
              </div>
              <div className="flex items-start gap-3 mb-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-gray-300">Set up automatic email receipt processing for digital receipts</span>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-gray-300">Choose tools that export directly to Google Sheets or QuickBooks</span>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">2. Establish Clear Expense Categories</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Consistent categorization makes tax preparation easier and provides better insights into your spending patterns.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <h4 className="text-white font-semibold mb-2">Common Categories:</h4>
                  <ul className="text-gray-300 space-y-1 text-sm">
                    <li>• Office Supplies</li>
                    <li>• Travel & Transportation</li>
                    <li>• Meals & Entertainment</li>
                    <li>• Professional Services</li>
                  </ul>
                </div>
                <div>
                  <h4 className="text-white font-semibold mb-2">Tech Categories:</h4>
                  <ul className="text-gray-300 space-y-1 text-sm">
                    <li>• Software Subscriptions</li>
                    <li>• Equipment & Hardware</li>
                    <li>• Internet & Phone</li>
                    <li>• Marketing & Advertising</li>
                  </ul>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">3. Implement Real-Time Tracking</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Don't wait until month-end to process expenses. Real-time tracking prevents lost receipts and provides 
                better cash flow visibility.
              </p>
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mt-4">
                <p className="text-blue-300 text-sm">
                  <strong>Pro Tip:</strong> Take photos of receipts immediately after purchases, even if you plan to process them later. 
                  This prevents lost receipts and ensures you capture all business expenses.
                </p>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">4. Set Up Monthly Reviews</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Regular expense reviews help identify spending patterns, catch errors, and ensure compliance with tax regulations.
              </p>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300">Review all expenses for accuracy and proper categorization</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300">Identify unusual spending patterns or potential cost savings</span>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300">Ensure all business expenses are properly documented</span>
                </div>
              </div>
            </div>

            <h2 className="text-2xl font-bold text-white mb-6 mt-8">5. Prepare for Tax Season Year-Round</h2>
            <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-xl p-6 mb-6">
              <p className="text-gray-300 mb-4">
                Don't scramble during tax season. Keep your expenses organized throughout the year to maximize deductions 
                and minimize stress.
              </p>
              <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4">
                <h4 className="text-green-300 font-semibold mb-2">Tax-Ready Checklist:</h4>
                <ul className="text-green-300 space-y-1 text-sm">
                  <li>✓ All receipts digitized and categorized</li>
                  <li>✓ Business vs. personal expenses clearly separated</li>
                  <li>✓ Mileage and travel expenses documented</li>
                  <li>✓ Equipment depreciation schedules maintained</li>
                </ul>
              </div>
            </div>

            {/* Call to Action */}
            <div className="bg-gradient-to-r from-pink-500/10 to-purple-600/10 backdrop-blur-md border border-pink-500/20 rounded-2xl p-6 sm:p-8 mt-12">
              <h3 className="text-2xl font-bold text-white mb-4">Ready to Automate Your Expense Management?</h3>
              <p className="text-gray-300 mb-6">
                ReceiptLabs makes expense management effortless with AI-powered receipt processing and automatic Google Sheets integration. 
                Start with 10 free receipts per month.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/expense-management-small-business"
                  className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-200 text-center"
                >
                  Learn More About ReceiptLabs
                </Link>
                <Link
                  href="/login"
                  className="border border-gray-600 hover:border-gray-500 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 text-center"
                >
                  Start Free Trial
                </Link>
              </div>
            </div>
          </div>
        </div>
      </article>

      {/* Footer */}
      <footer className="relative z-10 px-4 py-12 border-t border-gray-800 mt-20">
        <div className="max-w-7xl mx-auto text-center">
          <p className="text-gray-400 text-sm">
            © 2025 <strong>Receipt</strong>Labs. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}