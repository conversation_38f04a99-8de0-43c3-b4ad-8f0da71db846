import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ReceiptLabs - AI-Powered Receipt Management & Google Sheets Integration",
  description: "Transform your receipt management with AI. Automatically extract data from receipts and organize into Google Sheets. 99% accuracy, instant processing, bulk uploads. Start free!",
  keywords: "receipt management, AI receipt processing, Google Sheets integration, expense tracking, receipt scanner, automated data entry, business receipts, receipt OCR",
  icons: {
    icon: '/favicon.svg',
    shortcut: '/favicon.svg',
    apple: '/favicon.svg',
  },
  openGraph: {
    title: "ReceiptLabs - AI-Powered Receipt Management",
    description: "Stop manual data entry. Start smart processing. AI-powered receipt management with automatic Google Sheets integration.",
    type: "website",
    url: "https://receiptlabs.com",
  },
  twitter: {
    card: "summary_large_image",
    title: "ReceiptLabs - AI-Powered Receipt Management",
    description: "Transform your receipt management with AI. Automatically extract data and organize into Google Sheets.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        {children}
      </body>
    </html>
  );
}
