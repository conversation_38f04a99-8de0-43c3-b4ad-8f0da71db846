-- Gmail Integration Migration for Reco Engine
-- Date: 2025-06-28
-- Description: Add Gmail auto-processing feature for Business tier users

-- Add Gmail-related columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS gmail_connected BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS gmail_email VARCHAR(255);
ALTER TABLE users ADD COLUMN IF NOT EXISTS gmail_access_token TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS gmail_refresh_token TEXT;
ALTER TABLE users ADD COLUMN IF NOT EXISTS gmail_processing_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS gmail_processing_time TIME DEFAULT '09:00:00';
ALTER TABLE users ADD COLUMN IF NOT EXISTS gmail_last_processed_date DATE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS gmail_keywords TEXT DEFAULT 'receipt,invoice,purchase,order confirmation';

-- Add source tracking to receipts table to identify Gmail receipts
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS source VARCHAR(50) DEFAULT 'upload';
-- Possible values: 'upload', 'google_drive', 'gmail_auto', 'gmail_manual'

-- Add Gmail-specific metadata to receipts table
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS gmail_message_id VARCHAR(255);
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS gmail_thread_id VARCHAR(255);
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS gmail_subject TEXT;
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS gmail_sender VARCHAR(255);
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS gmail_received_date TIMESTAMP;

-- Create index for Gmail message tracking (prevent duplicates)
CREATE INDEX IF NOT EXISTS idx_receipts_gmail_message_id ON receipts(gmail_message_id) WHERE gmail_message_id IS NOT NULL;

-- Create index for source-based queries
CREATE INDEX IF NOT EXISTS idx_receipts_source ON receipts(source);

-- Create index for Gmail processing queries
CREATE INDEX IF NOT EXISTS idx_users_gmail_processing ON users(gmail_processing_enabled, gmail_processing_time) WHERE gmail_processing_enabled = TRUE;

-- Update the tier limits function to include Gmail access
CREATE OR REPLACE FUNCTION check_tier_limits(user_id_param UUID)
RETURNS TABLE(
  can_process_receipt BOOLEAN,
  receipts_remaining INTEGER,
  has_analytics_access BOOLEAN,
  has_google_drive_access BOOLEAN,
  has_gmail_auto_processing BOOLEAN,
  data_retention_months INTEGER
) AS $$
DECLARE
  user_tier TEXT;
  user_limit INTEGER;
  user_used INTEGER;
  user_status TEXT;
  user_retention INTEGER;
  remaining_receipts INTEGER;
  can_process BOOLEAN;
  has_analytics BOOLEAN;
  has_drive BOOLEAN;
  has_gmail BOOLEAN;
BEGIN
  -- Get user data
  SELECT 
    current_tier,
    monthly_receipt_limit,
    COALESCE(receipts_used_this_period, 0),
    subscription_status,
    COALESCE(data_retention_months, 1)
  INTO user_tier, user_limit, user_used, user_status, user_retention
  FROM users 
  WHERE id = user_id_param;

  -- If user not found, return default values
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 0, false, false, false, 1;
    RETURN;
  END IF;

  -- Calculate remaining receipts
  remaining_receipts := GREATEST(0, user_limit - user_used);
  
  -- Check if user can process receipts
  can_process := remaining_receipts > 0 AND user_status IN ('active', 'inactive');
  
  -- Check tier-based features
  has_analytics := user_tier IN ('professional', 'business');
  has_drive := user_tier = 'business';
  has_gmail := user_tier = 'business'; -- Gmail auto-processing is Business tier only

  -- Return results
  RETURN QUERY SELECT 
    can_process,
    remaining_receipts,
    has_analytics,
    has_drive,
    has_gmail,
    user_retention;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get users ready for Gmail processing
CREATE OR REPLACE FUNCTION get_users_for_gmail_processing(target_hour INTEGER)
RETURNS TABLE(
  user_id UUID,
  gmail_email VARCHAR(255),
  gmail_access_token TEXT,
  gmail_refresh_token TEXT,
  gmail_last_processed_date DATE,
  gmail_keywords TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.id,
    u.gmail_email,
    u.gmail_access_token,
    u.gmail_refresh_token,
    u.gmail_last_processed_date,
    u.gmail_keywords
  FROM users u
  WHERE 
    u.current_tier = 'business'
    AND u.subscription_status = 'active'
    AND u.gmail_connected = TRUE
    AND u.gmail_processing_enabled = TRUE
    AND EXTRACT(HOUR FROM u.gmail_processing_time) = target_hour
    AND (u.gmail_last_processed_date IS NULL OR u.gmail_last_processed_date < CURRENT_DATE);
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON COLUMN users.gmail_connected IS 'Whether user has connected their Gmail account';
COMMENT ON COLUMN users.gmail_email IS 'Email address of connected Gmail account (can differ from login email)';
COMMENT ON COLUMN users.gmail_processing_enabled IS 'Whether automatic Gmail processing is enabled';
COMMENT ON COLUMN users.gmail_processing_time IS 'Daily time when Gmail processing should run';
COMMENT ON COLUMN users.gmail_last_processed_date IS 'Last date Gmail was processed for this user';
COMMENT ON COLUMN users.gmail_keywords IS 'Keywords to search for in Gmail (comma-separated)';

COMMENT ON COLUMN receipts.source IS 'Source of receipt: upload, google_drive, gmail_auto, gmail_manual';
COMMENT ON COLUMN receipts.gmail_message_id IS 'Gmail message ID for tracking and preventing duplicates';
COMMENT ON COLUMN receipts.gmail_thread_id IS 'Gmail thread ID for grouping related messages';
COMMENT ON COLUMN receipts.gmail_subject IS 'Subject line of the Gmail message';
COMMENT ON COLUMN receipts.gmail_sender IS 'Sender email address from Gmail';
COMMENT ON COLUMN receipts.gmail_received_date IS 'Date when the Gmail message was received';

-- Create Gmail processing logs table for monitoring
CREATE TABLE IF NOT EXISTS gmail_processing_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  processing_date DATE NOT NULL,
  emails_processed INTEGER DEFAULT 0,
  receipts_found INTEGER DEFAULT 0,
  receipts_created INTEGER DEFAULT 0,
  processing_time_ms INTEGER DEFAULT 0,
  success BOOLEAN DEFAULT FALSE,
  errors TEXT, -- JSON string of error messages
  source VARCHAR(20) DEFAULT 'cron', -- 'manual', 'cron', 'api'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for Gmail processing logs
CREATE INDEX IF NOT EXISTS idx_gmail_logs_user_date ON gmail_processing_logs(user_id, processing_date DESC);
CREATE INDEX IF NOT EXISTS idx_gmail_logs_date ON gmail_processing_logs(processing_date DESC);
CREATE INDEX IF NOT EXISTS idx_gmail_logs_success ON gmail_processing_logs(success, processing_date DESC);

-- Add comments for Gmail processing logs
COMMENT ON TABLE gmail_processing_logs IS 'Logs of Gmail processing runs for monitoring and analytics';
COMMENT ON COLUMN gmail_processing_logs.user_id IS 'User who had Gmail processed';
COMMENT ON COLUMN gmail_processing_logs.processing_date IS 'Date when Gmail processing was performed';
COMMENT ON COLUMN gmail_processing_logs.emails_processed IS 'Number of emails analyzed';
COMMENT ON COLUMN gmail_processing_logs.receipts_found IS 'Number of receipt emails identified';
COMMENT ON COLUMN gmail_processing_logs.receipts_created IS 'Number of receipt records created';
COMMENT ON COLUMN gmail_processing_logs.processing_time_ms IS 'Processing time in milliseconds';
COMMENT ON COLUMN gmail_processing_logs.success IS 'Whether processing completed successfully';
COMMENT ON COLUMN gmail_processing_logs.errors IS 'JSON array of error messages if any';
COMMENT ON COLUMN gmail_processing_logs.source IS 'Source of processing: manual, cron, or api';
