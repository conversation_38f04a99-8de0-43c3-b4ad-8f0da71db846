// Data retention cron job management for Reco Engine
// Handles scheduling and execution of data retention monitoring and cleanup

export interface CronJobConfig {
  name: string;
  schedule: string; // Cron expression
  description: string;
  enabled: boolean;
  lastRun?: string;
  nextRun?: string;
  functionUrl: string;
}

export interface CronJobResult {
  jobName: string;
  success: boolean;
  executionTime: number;
  timestamp: string;
  result?: any;
  error?: string;
}

// Cron job configurations for data retention and Gmail processing
export const DATA_RETENTION_CRON_JOBS: CronJobConfig[] = [
  {
    name: 'data-retention-monitor',
    schedule: '0 2 * * *', // Daily at 2 AM
    description: 'Monitor data retention status across all tiers without deleting data',
    enabled: true,
    functionUrl: '/functions/v1/data-retention-monitor'
  },
  {
    name: 'data-retention-cleanup',
    schedule: '0 3 * * 0', // Weekly on Sunday at 3 AM
    description: 'Clean up expired receipts based on tier retention policies',
    enabled: true,
    functionUrl: '/functions/v1/data-retention-cleanup'
  },
  {
    name: 'data-retention-report',
    schedule: '0 9 1 * *', // Monthly on 1st at 9 AM
    description: 'Generate monthly data retention report for admin review',
    enabled: true,
    functionUrl: '/functions/v1/data-retention-report'
  },
  {
    name: 'gmail-receipt-processing',
    schedule: '0 * * * *', // Every hour
    description: 'Process Gmail receipts for Business tier users at their scheduled times',
    enabled: true, // Enabled for local development and production
    functionUrl: '/functions/v1/gmail-processor'
  }
];

export class DataRetentionCronManager {
  private supabaseUrl: string;
  private supabaseAnonKey: string;

  constructor() {
    this.supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    this.supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
  }

  // Execute a specific cron job
  async executeCronJob(jobName: string): Promise<CronJobResult> {
    const startTime = Date.now();
    const timestamp = new Date().toISOString();

    try {
      const job = DATA_RETENTION_CRON_JOBS.find(j => j.name === jobName);
      if (!job) {
        throw new Error(`Cron job '${jobName}' not found`);
      }

      if (!job.enabled) {
        throw new Error(`Cron job '${jobName}' is disabled`);
      }

      console.log(`🚀 Executing cron job: ${jobName}`);

      // Call the Supabase Edge Function
      const response = await fetch(`${this.supabaseUrl}${job.functionUrl}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.supabaseAnonKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          source: 'cron',
          timestamp,
          jobName
        })
      });

      const result = await response.json();
      const executionTime = Date.now() - startTime;

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${result.error || 'Unknown error'}`);
      }

      console.log(`✅ Cron job '${jobName}' completed successfully in ${executionTime}ms`);

      return {
        jobName,
        success: true,
        executionTime,
        timestamp,
        result
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      console.error(`❌ Cron job '${jobName}' failed:`, errorMessage);

      return {
        jobName,
        success: false,
        executionTime,
        timestamp,
        error: errorMessage
      };
    }
  }

  // Execute all enabled cron jobs
  async executeAllJobs(): Promise<CronJobResult[]> {
    const results: CronJobResult[] = [];

    for (const job of DATA_RETENTION_CRON_JOBS) {
      if (job.enabled) {
        const result = await this.executeCronJob(job.name);
        results.push(result);
      } else {
        console.log(`⏭️ Skipping disabled job: ${job.name}`);
      }
    }

    return results;
  }

  // Get job status and next run times
  getJobStatuses(): (CronJobConfig & { nextRunTime: Date })[] {
    return DATA_RETENTION_CRON_JOBS.map(job => ({
      ...job,
      nextRunTime: this.getNextRunTime(job.schedule)
    }));
  }

  // Calculate next run time based on cron expression
  private getNextRunTime(cronExpression: string): Date {
    // Simple cron parser for common patterns
    // In production, you'd use a proper cron library like 'node-cron' or 'cron-parser'
    
    const now = new Date();
    const nextRun = new Date(now);

    // Parse basic cron patterns
    const parts = cronExpression.split(' ');
    if (parts.length !== 5) {
      // Default to next hour if invalid cron expression
      nextRun.setHours(nextRun.getHours() + 1);
      return nextRun;
    }

    const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;

    // Handle daily jobs (0 2 * * *)
    if (dayOfMonth === '*' && month === '*' && dayOfWeek === '*') {
      nextRun.setHours(parseInt(hour), parseInt(minute), 0, 0);
      if (nextRun <= now) {
        nextRun.setDate(nextRun.getDate() + 1);
      }
      return nextRun;
    }

    // Handle weekly jobs (0 3 * * 0)
    if (dayOfMonth === '*' && month === '*' && dayOfWeek !== '*') {
      const targetDay = parseInt(dayOfWeek);
      const currentDay = nextRun.getDay();
      const daysUntilTarget = (targetDay - currentDay + 7) % 7;
      
      nextRun.setDate(nextRun.getDate() + daysUntilTarget);
      nextRun.setHours(parseInt(hour), parseInt(minute), 0, 0);
      
      if (nextRun <= now) {
        nextRun.setDate(nextRun.getDate() + 7);
      }
      return nextRun;
    }

    // Handle monthly jobs (0 9 1 * *)
    if (dayOfMonth !== '*' && month === '*') {
      const targetDay = parseInt(dayOfMonth);
      nextRun.setDate(targetDay);
      nextRun.setHours(parseInt(hour), parseInt(minute), 0, 0);
      
      if (nextRun <= now) {
        nextRun.setMonth(nextRun.getMonth() + 1);
      }
      return nextRun;
    }

    // Default fallback
    nextRun.setHours(nextRun.getHours() + 1);
    return nextRun;
  }

  // Validate cron expression
  validateCronExpression(expression: string): boolean {
    const parts = expression.split(' ');
    if (parts.length !== 5) return false;

    // Basic validation - in production use a proper cron validator
    const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;
    
    return (
      this.isValidCronField(minute, 0, 59) &&
      this.isValidCronField(hour, 0, 23) &&
      this.isValidCronField(dayOfMonth, 1, 31) &&
      this.isValidCronField(month, 1, 12) &&
      this.isValidCronField(dayOfWeek, 0, 6)
    );
  }

  private isValidCronField(field: string, min: number, max: number): boolean {
    if (field === '*') return true;
    
    const num = parseInt(field);
    return !isNaN(num) && num >= min && num <= max;
  }
}

// Export singleton instance
export const dataRetentionCronManager = new DataRetentionCronManager();
