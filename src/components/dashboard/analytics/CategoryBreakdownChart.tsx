'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>A<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

interface CategoryBreakdownChartProps {
  data: { category: string; total: number }[];
  currency?: string;
}

const COLORS = ['#FF006E', '#FB8500', '#8338EC', '#00F5FF', '#FFBE0B', '#8B5CF6'];

export function CategoryBreakdownChart({ data, currency = 'KES' }: CategoryBreakdownChartProps) {
  const hasData = data && data.length > 0 && data.some(item => item.total > 0);

  // Format currency based on the currency type
  const formatCurrency = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
  };

  const formatCurrencyDetailed = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  // Sort data by amount for better visualization
  const sortedData = hasData ? [...data].sort((a, b) => b.total - a.total).slice(0, 5) : [];

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-lg p-4">
      <h3 className="text-base font-semibold text-white mb-3">Category Breakdown</h3>

      {hasData ? (
        <>
          <ResponsiveContainer width="100%" height={200}>
            <BarChart
              data={sortedData}
              layout="horizontal"
              margin={{ top: 5, right: 30, left: 40, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis
                type="number"
                stroke="#9CA3AF"
                fontSize={10}
                tick={{ fill: '#9CA3AF' }}
                tickFormatter={(value) => formatCurrency(value)}
              />
              <YAxis
                type="category"
                dataKey="category"
                stroke="#9CA3AF"
                fontSize={9}
                tick={{ fill: '#9CA3AF' }}
                width={35}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB',
                  fontSize: '12px'
                }}
                formatter={(value: any, name: string, props: any) => [
                  <div key="tooltip" className="space-y-1">
                    <div className="font-medium">{props.payload.category}</div>
                    <div>Amount: {formatCurrencyDetailed(value)}</div>
                  </div>
                ]}
                labelFormatter={() => ''}
              />
              <Bar
                dataKey="total"
                fill="#8338EC"
                radius={[0, 2, 2, 0]}
              />
            </BarChart>
          </ResponsiveContainer>

          {/* Category Legend */}
          <div className="mt-3 space-y-1">
            {sortedData.map((category, index) => (
              <div key={category.category} className="flex items-center justify-between text-xs">
                <div className="flex items-center space-x-1.5">
                  <div
                    className="w-2 h-2 rounded-full flex-shrink-0"
                    style={{ backgroundColor: COLORS[index % COLORS.length] }}
                  />
                  <span className="text-gray-400 truncate">{category.category}</span>
                </div>
                <span className="text-white font-medium ml-1 text-xs">{formatCurrency(category.total)}</span>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="h-[200px] flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-3 bg-gray-700/30 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
            <p className="text-sm font-medium mb-1">No categories yet</p>
            <p className="text-xs">Upload receipts to see spending by category</p>
          </div>
        </div>
      )}
    </div>
  );
} 