import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { redis } from '@/lib/redis'
import { QUEUES } from '@/lib/queue'
import { createProcessReceiptJob } from '@/lib/jobs'
import { addJobToQueue } from '@/lib/queue'

// Fallback cleanup and recovery endpoint
export async function POST(request: NextRequest) {
  try {
    console.log('🧹 Fallback cleanup started')
    
    const supabase = await createClient()
    const results = {
      stuckJobs: 0,
      expiredJobs: 0,
      requeuedReceipts: 0,
      cleanedKeys: 0,
      errors: [] as string[]
    }
    
    // 1. Clean up expired Redis keys
    try {
      console.log('🗑️ Cleaning up expired Redis keys...')
      
      // Get all job tracking keys
      const jobKeys = await (redis as any).keys('job:*')
      let cleanedCount = 0
      
      for (const key of jobKeys) {
        try {
          const ttl = await (redis as any).ttl(key)
          
          // If TTL is -1 (no expiration) or very old, clean it up
          if (ttl === -1) {
            const jobData = await (redis as any).hgetall(key)
            
            if (jobData && jobData.queuedAt) {
              const queuedTime = new Date(jobData.queuedAt).getTime()
              const now = Date.now()
              const ageHours = (now - queuedTime) / (1000 * 60 * 60)
              
              // Clean up jobs older than 24 hours
              if (ageHours > 24) {
                await (redis as any).del(key)
                cleanedCount++
              }
            }
          }
        } catch (error) {
          console.error(`Error processing key ${key}:`, error)
        }
      }
      
      results.cleanedKeys = cleanedCount
      console.log(`🗑️ Cleaned up ${cleanedCount} expired job keys`)
      
    } catch (error) {
      const errorMsg = `Redis cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      console.error('❌', errorMsg)
      results.errors.push(errorMsg)
    }
    
    // 2. Find and requeue stuck receipts
    try {
      console.log('🔍 Looking for stuck receipts...')
      
      // Find receipts that have been processing for more than 10 minutes
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000).toISOString()
      
      const { data: stuckReceipts, error: stuckError } = await supabase
        .from('receipts')
        .select('id, user_id, file_path, original_file_name, file_size, redis_job_id, created_at, updated_at')
        .eq('processing_status', 'processing')
        .lt('updated_at', tenMinutesAgo)
        .limit(10) // Process max 10 stuck receipts at a time
      
      if (stuckError) {
        throw new Error(`Database query failed: ${stuckError.message}`)
      }
      
      if (stuckReceipts && stuckReceipts.length > 0) {
        console.log(`📋 Found ${stuckReceipts.length} stuck receipts`)
        
        for (const receipt of stuckReceipts) {
          try {
            // Check if job still exists in Redis
            let jobExists = false
            if (receipt.redis_job_id) {
              const jobData = await (redis as any).hgetall(`job:${receipt.redis_job_id}`)
              jobExists = jobData && Object.keys(jobData).length > 0
            }
            
            if (!jobExists) {
              console.log(`🔄 Requeuing stuck receipt: ${receipt.id}`)
              
              // Create new job
              const newJob = createProcessReceiptJob({
                receiptId: receipt.id,
                userId: receipt.user_id,
                imageUrl: receipt.file_path,
                fileName: receipt.original_file_name,
                fileSize: receipt.file_size || 0,
                priority: 'high' // High priority for stuck receipts
              })
              
              // Add to queue
              await addJobToQueue(newJob)
              
              // Update receipt status
              await supabase
                .from('receipts')
                .update({
                  processing_status: 'pending',
                  redis_job_id: newJob.id,
                  error_message: null
                })
                .eq('id', receipt.id)
              
              results.requeuedReceipts++
            }
            
          } catch (error) {
            const errorMsg = `Failed to requeue receipt ${receipt.id}: ${error instanceof Error ? error.message : 'Unknown error'}`
            console.error('❌', errorMsg)
            results.errors.push(errorMsg)
          }
        }
      }
      
    } catch (error) {
      const errorMsg = `Stuck receipt recovery failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      console.error('❌', errorMsg)
      results.errors.push(errorMsg)
    }
    
    // 3. Clean up old failed receipts
    try {
      console.log('🧹 Cleaning up old failed receipts...')
      
      // Find receipts that have been failed for more than 1 hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000).toISOString()
      
      const { data: oldFailedReceipts, error: failedError } = await supabase
        .from('receipts')
        .select('id, redis_job_id')
        .eq('processing_status', 'failed')
        .lt('updated_at', oneHourAgo)
        .limit(20)
      
      if (failedError) {
        throw new Error(`Failed receipts query failed: ${failedError.message}`)
      }
      
      if (oldFailedReceipts && oldFailedReceipts.length > 0) {
        for (const receipt of oldFailedReceipts) {
          try {
            // Clean up associated Redis job data
            if (receipt.redis_job_id) {
              await (redis as any).del(`job:${receipt.redis_job_id}`)
            }
            
            // Remove from failed queue if present
            const failedJobs = await (redis as any).zrange(QUEUES.FAILED, 0, -1)
            for (const jobJson of failedJobs) {
              try {
                const job = JSON.parse(jobJson)
                if (job.receiptId === receipt.id) {
                  await (redis as any).zrem(QUEUES.FAILED, jobJson)
                  break
                }
              } catch (parseError) {
                // Skip invalid JSON
              }
            }
            
            results.expiredJobs++
            
          } catch (error) {
            console.error(`Error cleaning up failed receipt ${receipt.id}:`, error)
          }
        }
        
        console.log(`🧹 Cleaned up ${results.expiredJobs} old failed receipts`)
      }
      
    } catch (error) {
      const errorMsg = `Failed receipt cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      console.error('❌', errorMsg)
      results.errors.push(errorMsg)
    }
    
    // 4. Check queue health
    try {
      const queueSizes = {
        high: await (redis as any).zcard(QUEUES.HIGH_PRIORITY),
        normal: await (redis as any).zcard(QUEUES.NORMAL_PRIORITY),
        low: await (redis as any).zcard(QUEUES.LOW_PRIORITY),
        processing: await (redis as any).zcard(QUEUES.PROCESSING),
        failed: await (redis as any).zcard(QUEUES.FAILED)
      }
      
      console.log('📊 Queue health check:', queueSizes)
      
      // If processing queue is too large, it might indicate stuck jobs
      if (queueSizes.processing > 10) {
        results.errors.push(`Processing queue unusually large: ${queueSizes.processing} jobs`)
      }
      
    } catch (error) {
      console.error('❌ Queue health check failed:', error)
    }
    
    console.log('✅ Fallback cleanup completed:', results)
    
    return NextResponse.json({
      success: true,
      message: 'Fallback cleanup completed',
      results,
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('❌ Fallback cleanup error:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Fallback cleanup failed',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
