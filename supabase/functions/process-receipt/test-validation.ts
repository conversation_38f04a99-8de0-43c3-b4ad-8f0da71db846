// Test script for validation system
// Run this to verify validation is working correctly

import { 
  validateExtractedData, 
  validateInsertReceipt, 
  validateInsertReceiptItems,
  sanitizeExtractedData,
  formatValidationErrors 
} from "../_shared/validation.ts";

// Test valid extracted data
const validExtractedData = {
  vendor: "Monacoo",
  vendor_tax_id: "P051234567M",
  date: "2025-06-12",
  currency: "KES",
  payment_method: "Cash",
  items: [
    {
      description: "Milk",
      total: 1050.00,
      category: "Food Supplies",
      quantity: 1
    }
  ],
  subtotal: 7050.00,
  tax_rate_percent: 1.0,
  tax_amount: 70.50,
  total_amount: 7120.50,
  paid_amount: 7120.50
};

// Test invalid extracted data
const invalidExtractedData = {
  vendor: "", // Empty vendor name (invalid)
  date: "invalid-date", // Invalid date format
  currency: "INVALID", // Invalid currency (not 3 letters)
  total_amount: -100, // Negative total (invalid)
  items: [
    {
      description: "", // Empty description (invalid)
      total: -50 // Negative total (invalid)
    }
  ]
};

// Test valid receipt insert data
const validReceiptData = {
  user_id: "550e8400-e29b-41d4-a716-446655440000",
  original_file_name: "receipt.jpg",
  file_path: "receipts/test.jpg",
  file_size: 1024,
  mime_type: "image/jpeg",
  vendor: "Test Vendor",
  date: "2025-06-12",
  currency: "KES",
  total_amount: 100.00,
  redis_job_id: "test-job-123"
};

console.log("=== Validation Test Suite ===\n");

// Test 1: Valid extracted data
console.log("Test 1: Valid extracted data");
const validResult = validateExtractedData(validExtractedData);
console.log("Result:", validResult.success ? "✅ PASSED" : "❌ FAILED");
if (!validResult.success) {
  console.log("Errors:", formatValidationErrors(validResult.errors!));
}
console.log();

// Test 2: Invalid extracted data
console.log("Test 2: Invalid extracted data");
const invalidResult = validateExtractedData(invalidExtractedData);
console.log("Result:", !invalidResult.success ? "✅ PASSED (correctly rejected)" : "❌ FAILED (should have been rejected)");
if (!invalidResult.success) {
  console.log("Expected errors found:", formatValidationErrors(invalidResult.errors!));
}
console.log();

// Test 3: Data sanitization
console.log("Test 3: Data sanitization");
const dataWithNulls = {
  vendor: "Test",
  vendor_tax_id: null,
  date: "2025-06-12",
  total_amount: "100.50", // String that should be converted to number
  items: [{
    description: "Item",
    total: "50.25" // String that should be converted to number
  }]
};
const sanitized = sanitizeExtractedData(dataWithNulls);
console.log("Sanitization test:", 
  typeof sanitized.total_amount === 'number' && 
  sanitized.vendor_tax_id === undefined ? "✅ PASSED" : "❌ FAILED");
console.log();

// Test 4: Valid receipt insertion data
console.log("Test 4: Valid receipt insertion data");
const receiptResult = validateInsertReceipt(validReceiptData);
console.log("Result:", receiptResult.success ? "✅ PASSED" : "❌ FAILED");
if (!receiptResult.success) {
  console.log("Errors:", formatValidationErrors(receiptResult.errors!));
}
console.log();

// Test 5: Receipt items validation
console.log("Test 5: Receipt items validation");
const validItems = [
  {
    receipt_id: "550e8400-e29b-41d4-a716-446655440000",
    description: "Test Item",
    quantity: 2,
    total_price: 50.00,
    category: "Test Category"
  }
];
const itemsResult = validateInsertReceiptItems(validItems);
console.log("Result:", itemsResult.success ? "✅ PASSED" : "❌ FAILED");
if (!itemsResult.success) {
  console.log("Errors:", formatValidationErrors(itemsResult.errors!));
}

console.log("\n=== Test Suite Complete ==="); 