import ReceiptMakerClient from '@/components/receipt-maker/ReceiptMakerClient';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Free Receipt Generator | Create Professional Receipts Instantly',
  description: 'Create professional receipts in seconds. Free receipt maker tool with PDF and PNG downloads. No registration required.',
  keywords: 'receipt generator, receipt maker, create receipt, free receipt, PDF receipt, invoice generator',
  openGraph: {
    title: 'Free Receipt Generator | ReceiptLabs',
    description: 'Create professional receipts in seconds. Free receipt maker tool with PDF and PNG downloads.',
    type: 'website',
  },
};

export default function ReceiptMakerPage() {
  return (
    <div className="min-h-screen bg-gray-900">
      <ReceiptMakerClient />
    </div>
  );
}
