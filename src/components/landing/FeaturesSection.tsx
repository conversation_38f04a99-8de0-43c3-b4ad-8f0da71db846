import { Zap, FileText, Upload, Mail, Shield, Users, CheckCircle } from 'lucide-react'
import { Feature } from '@/lib/types/landing-page'

interface FeaturesSectionProps {
  features: Feature[];
}

export default function FeaturesSection({ features }: FeaturesSectionProps) {
  const getIcon = (iconName: string) => {
    const iconProps = "w-6 sm:w-8 h-6 sm:h-8"
    switch (iconName) {
      case 'Zap':
        return <Zap className={`${iconProps} text-pink-400`} />
      case 'FileText':
        return <FileText className={`${iconProps} text-purple-400`} />
      case 'Upload':
        return <Upload className={`${iconProps} text-blue-400`} />
      case 'Mail':
        return <Mail className={`${iconProps} text-orange-400`} />
      case 'Shield':
        return <Shield className={`${iconProps} text-purple-400`} />
      case 'Users':
        return <Users className={`${iconProps} text-green-400`} />
      default:
        return <Zap className={`${iconProps} text-pink-400`} />
    }
  }

  const getHoverColor = (iconName: string) => {
    switch (iconName) {
      case 'Zap':
        return 'hover:border-pink-500/50'
      case 'FileText':
        return 'hover:border-purple-500/50'
      case 'Upload':
        return 'hover:border-blue-500/50'
      case 'Mail':
        return 'hover:border-orange-500/50'
      case 'Shield':
        return 'hover:border-purple-500/50'
      case 'Users':
        return 'hover:border-green-500/50'
      default:
        return 'hover:border-pink-500/50'
    }
  }

  const getIconBgColor = (iconName: string) => {
    switch (iconName) {
      case 'Zap':
        return 'bg-pink-500/20 group-hover:bg-pink-500/30'
      case 'FileText':
        return 'bg-purple-500/20 group-hover:bg-purple-500/30'
      case 'Upload':
        return 'bg-blue-500/20 group-hover:bg-blue-500/30'
      case 'Mail':
        return 'bg-orange-500/20 group-hover:bg-orange-500/30'
      case 'Shield':
        return 'bg-purple-500/20 group-hover:bg-purple-500/30'
      case 'Users':
        return 'bg-green-500/20 group-hover:bg-green-500/30'
      default:
        return 'bg-pink-500/20 group-hover:bg-pink-500/30'
    }
  }

  return (
    <section id="features" className="relative z-10 px-4 py-12 sm:py-20 bg-gray-900/50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6 px-2">
            Powerful Features Built for Small Businesses
          </h2>
          <p className="text-base sm:text-xl text-gray-300 max-w-3xl mx-auto px-2">
            Everything your small business needs to transform expense management from tedious manual work to automated intelligence.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 mb-12 sm:mb-16">
          {features.map((feature) => (
            <div 
              key={feature.id}
              className={`group bg-gray-800/50 backdrop-blur-md border ${
                feature.isNew ? 'border-orange-500/30' : 'border-gray-700'
              } rounded-xl sm:rounded-2xl p-4 sm:p-6 lg:p-8 ${getHoverColor(feature.icon)} transition-all duration-300 relative`}
            >
              {/* NEW Badge */}
              {feature.isNew && (
                <div className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs font-bold px-2 sm:px-3 py-1 rounded-full">
                  NEW
                </div>
              )}
              
              <div className={`w-12 sm:w-16 h-12 sm:h-16 ${getIconBgColor(feature.icon)} rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6 transition-colors`}>
                {getIcon(feature.icon)}
              </div>
              
              <h3 className="text-lg sm:text-xl font-bold text-white mb-3 sm:mb-4 text-center">
                {feature.title}
              </h3>
              
              <p className="text-gray-400 text-center mb-3 sm:mb-4 text-sm sm:text-base">
                {feature.description}
              </p>
              
              <ul className="text-xs sm:text-sm text-gray-500 space-y-2">
                {feature.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <CheckCircle className="w-3 sm:w-4 h-3 sm:h-4 text-green-400 flex-shrink-0" />
                    {benefit}
                  </li>
                ))}
              </ul>

              {/* Plan Requirement Badge */}
              {feature.planRequirement && (
                <div className="mt-4 text-center">
                  <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                    feature.planRequirement === 'business' 
                      ? 'bg-orange-500/20 text-orange-300 border border-orange-500/30'
                      : feature.planRequirement === 'professional'
                      ? 'bg-purple-500/20 text-purple-300 border border-purple-500/30'
                      : 'bg-green-500/20 text-green-300 border border-green-500/30'
                  }`}>
                    {feature.planRequirement.charAt(0).toUpperCase() + feature.planRequirement.slice(1)} Plan
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}