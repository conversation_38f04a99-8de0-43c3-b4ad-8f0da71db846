'use client';

import { ReceiptInfo, PAYMENT_METHODS } from '../types';

interface ReceiptDetailsSectionProps {
  receipt: ReceiptInfo;
  updateReceipt: (receipt: ReceiptInfo) => void;
}

export default function ReceiptDetailsSection({ receipt, updateReceipt }: ReceiptDetailsSectionProps) {
  const handleInputChange = (field: keyof ReceiptInfo, value: string) => {
    updateReceipt({ ...receipt, [field]: value });
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-white mb-2">Receipt Details</h2>
        <p className="text-gray-400 text-sm">Receipt number, date, and payment information</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Receipt Number
          </label>
          <input
            type="text"
            value={receipt.number}
            onChange={(e) => handleInputChange('number', e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            placeholder="Receipt Number"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Date
          </label>
          <input
            type="date"
            value={receipt.date}
            onChange={(e) => handleInputChange('date', e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Payment Method
          </label>
          <select
            value={receipt.paymentMethod}
            onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          >
            {PAYMENT_METHODS.map((method) => (
              <option key={method} value={method}>
                {method}
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  );
}
