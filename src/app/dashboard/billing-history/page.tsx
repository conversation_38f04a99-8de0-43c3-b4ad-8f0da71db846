import { createClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import BillingHistoryClient from '@/components/billing/BillingHistoryClient';

export default async function BillingHistoryPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect('/login');
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Billing History</h1>
          <p className="text-gray-400 mt-2">
            View all your subscription transactions and payment history
          </p>
        </div>
      </div>
      
      <BillingHistoryClient />
    </div>
  );
}
