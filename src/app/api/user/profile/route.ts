import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    // Get user data from users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        full_name,
        avatar_url,
        google_access_token,
        google_refresh_token,
        google_sheets_connected,
        current_tier,
        receipts_processed,
        subscription_status,
        subscription_end_date,
        monthly_receipt_limit,
        receipts_used_this_period,
        current_period_start,
        current_period_end,
        next_billing_date,
        data_retention_months,
        marketing_override,
        created_at,
        updated_at
      `)
      .eq('id', user.id)
      .single();

    if (userError) {
      console.error('Error fetching user data:', userError);
      return NextResponse.json({ 
        success: false, 
        error: userError.message 
      }, { status: 500 });
    }

    // Get user preferences (create default if not exists)
    let { data: preferences, error: prefError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (prefError && prefError.code === 'PGRST116') {
      // No preferences found, create default
      const defaultPreferences = {
        user_id: user.id,
        default_currency: 'KES',
        auto_process_receipts: true,
        confidence_threshold: 0.8,
        notification_email: true,
        notification_processing: true,
        data_retention_months: 36,
        export_format: 'csv'
      };

      const { data: newPrefs, error: createError } = await supabase
        .from('user_preferences')
        .insert(defaultPreferences)
        .select()
        .single();

      if (createError) {
        console.error('Error creating default preferences:', createError);
        return NextResponse.json({ 
          success: false, 
          error: createError.message 
        }, { status: 500 });
      }

      preferences = newPrefs;
    } else if (prefError) {
      console.error('Error fetching preferences:', prefError);
      return NextResponse.json({ 
        success: false, 
        error: prefError.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      user: userData,
      preferences: {
        default_currency: preferences.default_currency,
        auto_process_receipts: preferences.auto_process_receipts,
        confidence_threshold: preferences.confidence_threshold,
        notification_email: preferences.notification_email,
        notification_processing: preferences.notification_processing,
        data_retention_months: preferences.data_retention_months,
        export_format: preferences.export_format
      }
    });

  } catch (error) {
    console.error('Profile API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    const body = await request.json();
    const { full_name } = body;

    // Update user profile
    const { error: updateError } = await supabase
      .from('users')
      .update({ 
        full_name: full_name || null,
        updated_at: new Date().toISOString()
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error updating profile:', updateError);
      return NextResponse.json({ 
        success: false, 
        error: updateError.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully'
    });

  } catch (error) {
    console.error('Profile update API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
