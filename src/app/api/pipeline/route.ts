import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { getNextJob, getJobFromProcessingQueue, completeJob, failJob } from '@/lib/queue'
import { createClient as createServiceClient } from '@supabase/supabase-js'
import { redis } from '@/lib/redis'
import { publishJobStatusUpdate, storeNotificationForPolling } from '@/lib/pubsub'

// Create a service role client for edge function calls
function createServiceClient_() {
  return createServiceClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        persistSession: false,
        autoRefreshToken: false
      }
    }
  )
}

export async function POST(request: NextRequest) {
  try {
    const userAgent = request.headers.get('user-agent') || ''
    const isFallbackProcessor = userAgent.includes('Fallback-Processor')
    const isWebhookTrigger = userAgent.includes('Webhook-Trigger')

    console.log('Pipeline POST endpoint called', {
      userAgent,
      isFallbackProcessor,
      isWebhookTrigger
    })

    const supabase = await createClient()
    console.log('Supabase client created')

    // Optimized batch processing - smaller batches for faster individual processing
    const batchSize = isWebhookTrigger ? 1 : 3; // Smaller batches for faster individual job processing
    const jobs = [];

    // Get multiple jobs for concurrent processing with parallel fetching
    console.log(`Getting up to ${batchSize} jobs from queue...`)

    // Simplified job fetching for better performance
    for (let i = 0; i < batchSize; i++) {
      try {
        const job = await getNextJob()
        if (job) {
          jobs.push(job);
          console.log(`Job retrieved:`, job.id)
        } else {
          // If no more jobs, break early
          break;
        }
      } catch (queueError) {
        console.error('Failed to get job from queue:', queueError)
        break;
      }
    }

    if (jobs.length === 0) {
      console.log('No jobs in any queue')
      return NextResponse.json({
        success: false,
        message: 'No jobs in queue'
      }, { status: 200 })
    }

    console.log(`Processing ${jobs.length} jobs concurrently...`)

    // Process all jobs concurrently with optimized database operations
    const jobProcessingPromises = jobs.map(async (job) => {
      console.log('Processing job:', job.id)

      try {
        // Batch Redis and database operations for better performance
        const redisUpdate = redis.hset(`job:${job.id}`, {
          status: 'processing',
          processingStartedAt: new Date().toISOString()
        });

        // For test jobs, skip database operations
        let dbUpdate: Promise<any> = Promise.resolve();
        if (!job.receiptId.startsWith('test-receipt-')) {
          dbUpdate = supabase
            .from('receipts')
            .update({
              processing_status: 'processing',
              redis_job_id: job.id
            })
            .eq('id', job.receiptId);
        }

        // Execute Redis and DB updates in parallel
        const [redisResult, dbResult] = await Promise.all([redisUpdate, dbUpdate]);

        if (dbResult && dbResult.error) {
          console.error('Failed to update receipt status:', dbResult.error)
          await failJob(job.id, `Database update failed: ${dbResult.error.message}`)
          throw new Error(`Database update failed: ${dbResult.error.message}`)
        }

        console.log('Job marked as processing successfully:', job.id)
        return job;
      } catch (error) {
        console.error('Failed to mark job as processing:', error)
        await failJob(job.id, `Failed to mark as processing: ${error instanceof Error ? error.message : 'Unknown error'}`)
        throw error;
      }
    });

    // Wait for all jobs to be marked as processing
    const processingResults = await Promise.allSettled(jobProcessingPromises);

    // Filter successful jobs
    const successfulJobs = processingResults
      .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
      .map(result => result.value);

    if (successfulJobs.length === 0) {
      console.error('All jobs failed during initial processing')
      return NextResponse.json({
        success: false,
        error: 'All jobs failed during initial processing'
      }, { status: 500 })
    }

    console.log(`Successfully prepared ${successfulJobs.length} jobs for processing`)

    // Process all successful jobs concurrently through edge function
    const edgeFunctionPromises = successfulJobs.map(async (job) => {
      try {
        console.log('Calling edge function for job:', job.id)

        const serviceClient = createServiceClient_()

        const { data, error } = await serviceClient.functions.invoke('process-receipt', {
          body: { job }
        })

        if (error) {
          console.error('Edge function error for job', job.id, ':', error)
          await failJob(job.id, `Edge function error: ${error.message}`)
          return { job, success: false, error }
        }

        if (!data || !data.success) {
          console.error('Edge function returned failure for job', job.id, ':', data)
          await failJob(job.id, `Edge function failed: ${data?.error || 'Unknown error'}`)
          return { job, success: false, error: data?.error }
        }

        console.log('Edge function completed successfully for job:', job.id)

        // Complete the job and update receipt status in parallel
        const jobCompletion = completeJob(job.id);

        let receiptUpdate: Promise<any> = Promise.resolve();
        if (!job.receiptId.startsWith('test-receipt-')) {
          receiptUpdate = supabase
            .from('receipts')
            .update({
              processing_status: 'completed',
              confidence_score: data.confidence_score || 0.8
            })
            .eq('id', job.receiptId);
        }

        await Promise.all([jobCompletion, receiptUpdate]);

        // Publish job completion notification (non-blocking)
        try {
          const statusUpdate = {
            event_type: 'job.completed' as const,
            job_id: job.id,
            receipt_id: job.receiptId,
            user_id: job.userId,
            old_status: 'processing',
            new_status: 'completed',
            confidence_score: data.confidence_score || 0.8,
            updated_at: new Date().toISOString()
          }

          // Don't await these - let them run in background
          publishJobStatusUpdate(statusUpdate).catch(console.error);
          storeNotificationForPolling(job.userId, statusUpdate).catch(console.error);
        } catch (notificationError) {
          console.error('❌ Failed to publish job completion notification:', notificationError)
        }

        return { job, success: true, data };
      } catch (error) {
        console.error('Failed to process job through edge function:', job.id, error)
        await failJob(job.id, `Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
        return { job, success: false, error };
      }
    });

    // Wait for all edge function calls to complete
    const edgeFunctionResults = await Promise.allSettled(edgeFunctionPromises);

    const completedJobs = edgeFunctionResults
      .filter((result): result is PromiseFulfilledResult<any> =>
        result.status === 'fulfilled' && result.value.success
      )
      .map(result => result.value);

    const failedJobs = edgeFunctionResults
      .filter((result): result is PromiseFulfilledResult<any> =>
        result.status === 'fulfilled' && !result.value.success
      )
      .map(result => result.value);

    console.log(`Batch processing completed: ${completedJobs.length} successful, ${failedJobs.length} failed`)

    // Return success if at least one job completed successfully
    if (completedJobs.length > 0) {
      const primaryJob = completedJobs[0];
      return NextResponse.json({
        success: true,
        message: `Successfully processed ${completedJobs.length} jobs`,
        jobId: primaryJob.job.id,
        receiptId: primaryJob.job.receiptId,
        batchResults: {
          successful: completedJobs.length,
          failed: failedJobs.length,
          total: jobs.length
        }
      })
    } else {
      return NextResponse.json({
        success: false,
        message: `All ${jobs.length} jobs failed processing`,
        batchResults: {
          successful: 0,
          failed: failedJobs.length,
          total: jobs.length
        }
      }, { status: 500 })
    }



  } catch (error) {
    console.error('Pipeline processing error:', error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'Unknown error')
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      }, 
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('Pipeline GET endpoint called - checking for jobs and keeping system warm...')
    
    const supabase = await createClient()
    
    // Keep edge function warm by calling it with a test job
    try {
      const serviceClient = createServiceClient_()
      const warmupResult = await serviceClient.functions.invoke('process-receipt', {
        body: {
          job: {
            id: `warmup-${Date.now()}`,
            receiptId: `test-receipt-warmup-${Date.now()}`,
            userId: 'system',
            priority: 'low',
            attempts: 0,
            maxAttempts: 1,
            createdAt: new Date().toISOString(),
            data: {
              imageUrl: 'warmup',
              fileName: 'warmup.test',
              fileSize: 0
            }
          }
        }
      })
      console.log('Edge function warmup completed:', warmupResult.data?.success ? 'OK' : 'Failed')
    } catch (warmupError) {
      console.warn('Edge function warmup failed (this is usually okay):', warmupError)
    }
    
    // Clean up stuck receipts (older than 5 minutes in processing status)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString()
    const { data: stuckReceipts } = await supabase
      .from('receipts')
      .select('id, processing_status, created_at')
      .eq('processing_status', 'processing')
      .lt('created_at', fiveMinutesAgo)
    
    if (stuckReceipts && stuckReceipts.length > 0) {
      console.log(`Found ${stuckReceipts.length} stuck receipts, resetting them...`)
      await supabase
        .from('receipts')
        .update({ processing_status: 'pending', error_message: null })
        .in('id', stuckReceipts.map(r => r.id))
      console.log('Stuck receipts reset to pending status')
    }
    
    // Check for receipts that are in pending status but have no corresponding job in queue
    // This happens when jobs were lost due to Redis issues or server restarts
    // Only check receipts that are older than 30 seconds to avoid re-queuing recently uploaded receipts
    const thirtySecondsAgo = new Date(Date.now() - 30 * 1000).toISOString()
    const { data: pendingReceipts } = await supabase
      .from('receipts')
      .select('id, user_id, file_path, original_file_name, file_size, created_at, redis_job_id')
      .eq('processing_status', 'pending')
      .lt('created_at', thirtySecondsAgo) // Only check receipts older than 30 seconds
      .order('created_at', { ascending: true })
      .limit(5) // Only requeue a few at a time to avoid overwhelming the system
    
    let requeuedCount = 0
    if (pendingReceipts && pendingReceipts.length > 0) {
      console.log(`Found ${pendingReceipts.length} pending receipts, checking if they need re-queuing...`)
      
      for (const receipt of pendingReceipts) {
        try {
          let jobExists = false

          // First check if the receipt has a redis_job_id and if that job exists
          if (receipt.redis_job_id) {
            const jobStatus = await (redis as any).hget(`job:${receipt.redis_job_id}`, 'status')
            if (jobStatus && jobStatus !== 'failed') {
              jobExists = true
              console.log(`Receipt ${receipt.id} already has active job ${receipt.redis_job_id} with status: ${jobStatus}`)
            }
          }

          // If no active job found, check all jobs to be thorough
          if (!jobExists) {
            const jobKeys = await (redis as any).keys(`job:*`)
            const receiptIds = await Promise.all(
              jobKeys.map((key: string) => (redis as any).hget(key, 'receiptId'))
            )
            jobExists = receiptIds.includes(receipt.id)
          }

          if (!jobExists) {
            console.log(`Re-queuing stuck receipt ${receipt.id} (created: ${receipt.created_at})`)

            // Create a new job for this receipt
            const { createProcessReceiptJob } = await import('@/lib/jobs')
            const { addJobToQueue } = await import('@/lib/queue')

            const job = createProcessReceiptJob({
              receiptId: receipt.id,
              userId: receipt.user_id,
              imageUrl: receipt.file_path,
              fileName: receipt.original_file_name,
              fileSize: receipt.file_size || 0,
              priority: 'high' // High priority for re-queued receipts
            })

            await addJobToQueue(job)

            // Update receipt with new job ID
            await supabase
              .from('receipts')
              .update({ redis_job_id: job.id })
              .eq('id', receipt.id)

            console.log(`Re-queued receipt ${receipt.id} as job ${job.id}`)
            requeuedCount++
          } else {
            console.log(`Receipt ${receipt.id} already has an active job, skipping re-queue`)
          }
        } catch (requeueError) {
          console.error(`Failed to re-queue receipt ${receipt.id}:`, requeueError)
        }
      }
    }
    
    // Check for jobs in the queue
    const job = await getNextJob()
    
    if (!job) {
      return NextResponse.json({ 
        success: false,
        message: 'No jobs in queue',
        queueStatus: 'empty',
        stuckReceiptsReset: stuckReceipts?.length || 0,
        pendingReceiptsRequeued: requeuedCount
      })
    }

    // If there's a job, trigger processing by calling our own POST endpoint
    const baseUrl = new URL(request.url).origin
    console.log('Found job, triggering processing...')
    
    try {
      const response = await fetch(`${baseUrl}/api/pipeline`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      const result = await response.json()
      
      return NextResponse.json({
        success: result.success || false,
        message: result.success ? 'Job processing triggered' : 'Job processing failed',
        result,
        queueStatus: 'processing',
        stuckReceiptsReset: stuckReceipts?.length || 0,
        pendingReceiptsRequeued: requeuedCount
      })
    } catch (processError) {
      console.error('Failed to trigger job processing:', processError)
      return NextResponse.json({
        success: false,
        message: 'Failed to trigger job processing',
        error: processError instanceof Error ? processError.message : 'Unknown error',
        queueStatus: 'error',
        stuckReceiptsReset: stuckReceipts?.length || 0,
        pendingReceiptsRequeued: requeuedCount
      })
    }
    
  } catch (error) {
    console.error('Pipeline GET error:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to check pipeline status',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}

// Add a new endpoint to process all pending jobs
export async function PUT(request: NextRequest) {
  try {
    console.log('Pipeline PUT endpoint called - processing all pending jobs...')
    
    const { maxJobs = 10 } = await request.json().catch(() => ({}))
    
    const processedJobs = []
    let hasMoreJobs = true
    let jobCount = 0
    
    while (hasMoreJobs && jobCount < maxJobs) {
      const job = await getNextJob()
      
      if (!job) {
        hasMoreJobs = false
        break
      }
      
      try {
        // Process this job by calling our POST endpoint logic
        const baseUrl = new URL(request.url).origin
        const response = await fetch(`${baseUrl}/api/pipeline`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          }
        })
        
        const result = await response.json()
        processedJobs.push({
          jobId: job.id,
          receiptId: job.receiptId,
          status: response.ok ? 'completed' : 'failed',
          result
        })
        
        jobCount++
        
        // Remove artificial delay for better performance
        
      } catch (error) {
        console.error(`Failed to process job ${job.id}:`, error)
        processedJobs.push({
          jobId: job.id,
          receiptId: job.receiptId,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        jobCount++
      }
    }
    
    return NextResponse.json({
      message: `Processed ${processedJobs.length} jobs`,
      processedJobs,
      hasMoreJobs
    })
    
  } catch (error) {
    console.error('Pipeline PUT error:', error)
    return NextResponse.json(
      { error: 'Failed to process pending jobs' }, 
      { status: 500 }
    )
  }
} 