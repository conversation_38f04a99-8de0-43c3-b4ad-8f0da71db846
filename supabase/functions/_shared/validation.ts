// Data validation schemas using Zod for receipt processing
// This provides type-safe validation before database insertion

import { z } from "https://deno.land/x/zod@v3.22.4/mod.ts";

// Receipt validation schema
export const receiptValidationSchema = z.object({
  vendor: z.string()
    .min(1, "Vendor name is required")
    .max(255, "Vendor name too long"),
  
  vendor_tax_id: z.string()
    .max(100, "Tax ID too long")
    .optional()
    .nullable(),
  
  date: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, "Date must be in YYYY-MM-DD format")
    .refine((date) => {
      const parsed = new Date(date);
      const now = new Date();
      return parsed <= now && parsed >= new Date('1900-01-01');
    }, "Date must be valid and not in the future"),
  
  currency: z.string()
    .length(3, "Currency must be 3-letter code")
    .regex(/^[A-Z]{3}$/, "Currency must be uppercase letters")
    .default("KES"),
  
  payment_method: z.string()
    .max(50, "Payment method too long")
    .optional(),
  
  subtotal: z.number()
    .min(0, "Subtotal cannot be negative")
    .optional(),

  tax_rate_percent: z.number()
    .min(0, "Tax rate cannot be negative")
    .max(100, "Tax rate cannot exceed 100%")
    .optional(),

  tax_amount: z.number()
    .min(0, "Tax amount cannot be negative")
    .optional(),

  total_amount: z.number()
    .min(0, "Total amount cannot be negative"),

  paid_amount: z.number()
    .min(0, "Paid amount cannot be negative")
    .optional()
});

// Receipt item validation schema (for extracted data)
export const receiptItemValidationSchema = z.object({
  description: z.string()
    .min(1, "Item description is required")
    .max(500, "Item description too long"),
  
  quantity: z.number()
    .min(0.001, "Quantity must be positive")
    .max(10000, "Quantity too large")
    .default(1),
  
  unit_price: z.number()
    .min(0, "Unit price cannot be negative")
    .optional(),

  total: z.number()
    .min(0, "Item total cannot be negative")
    .default(0), // Provide default value instead of optional
  
  category: z.string()
    .max(100, "Category too long")
    .optional()
});

// NEW: Receipt item validation schema for DB insert (uses `total_price` column)
export const receiptItemDBValidationSchema = z.object({
  description: z.string()
    .min(1, "Item description is required")
    .max(500, "Item description too long"),

  quantity: z.number()
    .min(0.001, "Quantity must be positive")
    .max(10000, "Quantity too large")
    .default(1),

  unit_price: z.number()
    .min(0, "Unit price cannot be negative")
    .optional(),

  total_price: z.number()
    .min(0, "Item total cannot be negative")
    .default(0),

  category: z.string()
    .max(100, "Category too long")
    .optional()
});

// Full extracted data validation schema
export const extractedDataValidationSchema = z.object({
  vendor: receiptValidationSchema.shape.vendor,
  vendor_tax_id: receiptValidationSchema.shape.vendor_tax_id,
  date: receiptValidationSchema.shape.date,
  currency: receiptValidationSchema.shape.currency,
  payment_method: receiptValidationSchema.shape.payment_method,
  subtotal: receiptValidationSchema.shape.subtotal,
  tax_rate_percent: receiptValidationSchema.shape.tax_rate_percent,
  tax_amount: receiptValidationSchema.shape.tax_amount,
  total_amount: receiptValidationSchema.shape.total_amount,
  paid_amount: receiptValidationSchema.shape.paid_amount,
  items: z.array(receiptItemValidationSchema)
    .min(0, "Items array cannot be empty")
    .max(50, "Too many items")
    .optional()
}).refine((data) => {
  // Cross-field validation: subtotal + tax should roughly equal total
  if (data.subtotal && data.tax_amount && data.total_amount) {
    const calculatedTotal = data.subtotal + data.tax_amount;
    const tolerance = Math.max(0.01, data.total_amount * 0.02); // 2% tolerance or 1 cent
    return Math.abs(calculatedTotal - data.total_amount) <= tolerance;
  }
  return true;
}, {
  message: "Total amount does not match subtotal + tax amount",
  path: ["total_amount"]
}).refine((data) => {
  // Validate that items total roughly matches receipt total (if items exist)
  if (data.items && data.items.length > 0 && data.total_amount) {
    const itemsTotal = data.items.reduce((sum, item) => sum + item.total, 0);
    const roundedItemsTotal = Math.round(itemsTotal * 100) / 100;
    const roundedReceiptTotal = Math.round(data.total_amount * 100) / 100;

    // Very lenient tolerance: 25% or 20 currency units, whichever is larger
    // This allows for auto-correction in the edge function
    const tolerance = Math.max(20.0, roundedReceiptTotal * 0.25);
    const difference = Math.abs(roundedItemsTotal - roundedReceiptTotal);

    console.log('Validation check:', {
      itemsTotal: roundedItemsTotal,
      receiptTotal: roundedReceiptTotal,
      difference,
      tolerance,
      passes: difference <= tolerance
    });

    return difference <= tolerance;
  }
  return true;
}, {
  message: "Items total does not match receipt total amount",
  path: ["items"]
});

// Database insert validation schemas
export const insertReceiptValidationSchema = z.object({
  user_id: z.string().uuid("Invalid user ID format"),
  original_file_name: z.string().min(1, "File name is required").max(255),
  file_path: z.string().min(1, "File path is required"),
  file_size: z.number().min(0).max(10 * 1024 * 1024).optional(), // 10MB max
  mime_type: z.string().max(100).optional(),
  redis_job_id: z.string().max(255).optional()
}).merge(receiptValidationSchema.partial());

export const insertReceiptItemValidationSchema = z.object({
  receipt_id: z.string().uuid("Invalid receipt ID format")
}).merge(receiptItemDBValidationSchema);

// Validation error types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
  received?: any;
}

export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  errors?: ValidationError[];
}

// Main validation functions
export function validateExtractedData(data: any): ValidationResult<any> {
  try {
    const result = extractedDataValidationSchema.safeParse(data);
    
    if (result.success) {
      return {
        success: true,
        data: result.data
      };
    } else {
      const errors: ValidationError[] = result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
        received: err.received
      }));
      
      return {
        success: false,
        errors
      };
    }
  } catch (error) {
    return {
      success: false,
      errors: [{
        field: 'root',
        message: `Validation failed: ${error.message}`,
        code: 'VALIDATION_ERROR'
      }]
    };
  }
}

export function validateInsertReceipt(data: any): ValidationResult<any> {
  try {
    const result = insertReceiptValidationSchema.safeParse(data);
    
    if (result.success) {
      return {
        success: true,
        data: result.data
      };
    } else {
      const errors: ValidationError[] = result.error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
        received: err.received
      }));
      
      return {
        success: false,
        errors
      };
    }
  } catch (error) {
    return {
      success: false,
      errors: [{
        field: 'root',
        message: `Validation failed: ${error.message}`,
        code: 'VALIDATION_ERROR'
      }]
    };
  }
}

export function validateInsertReceiptItems(data: any[]): ValidationResult<any[]> {
  try {
    const results = data.map((item, index) => {
      const result = insertReceiptItemValidationSchema.safeParse(item);
      return { result, index };
    });
    
    const failedResults = results.filter(r => !r.result.success);
    
    if (failedResults.length === 0) {
      return {
        success: true,
        data: results.map(r => r.result.data)
      };
    } else {
      const errors: ValidationError[] = failedResults.flatMap(failed => 
        failed.result.error!.errors.map(err => ({
          field: `items[${failed.index}].${err.path.join('.')}`,
          message: err.message,
          code: err.code,
          received: err.received
        }))
      );
      
      return {
        success: false,
        errors
      };
    }
  } catch (error) {
    return {
      success: false,
      errors: [{
        field: 'items',
        message: `Items validation failed: ${error.message}`,
        code: 'VALIDATION_ERROR'
      }]
    };
  }
}

// Utility function to format validation errors for logging
export function formatValidationErrors(errors: ValidationError[]): string {
  return errors.map(err => `${err.field}: ${err.message}`).join('; ');
}

// Data sanitization helpers
export function sanitizeExtractedData(data: any): any {
  // Remove any potentially harmful or unnecessary fields
  const sanitized = { ...data };
  
  // Ensure numeric fields are properly typed
  if (sanitized.total_amount !== undefined) {
    sanitized.total_amount = Number(sanitized.total_amount);
  }
  if (sanitized.subtotal !== undefined) {
    sanitized.subtotal = Number(sanitized.subtotal);
  }
  if (sanitized.tax_amount !== undefined) {
    sanitized.tax_amount = Number(sanitized.tax_amount);
  }
  if (sanitized.tax_rate_percent !== undefined) {
    sanitized.tax_rate_percent = Number(sanitized.tax_rate_percent);
  }
  
  // Sanitize items
  if (sanitized.items && Array.isArray(sanitized.items)) {
    sanitized.items = sanitized.items.map((item: any) => ({
      ...item,
      total: Number(item.total),
      quantity: item.quantity ? Number(item.quantity) : 1,
      unit_price: item.unit_price ? Number(item.unit_price) : undefined
    }));
  }
  
  // Remove any null values and replace with undefined for optional fields
  Object.keys(sanitized).forEach(key => {
    if (sanitized[key] === null) {
      sanitized[key] = undefined;
    }
  });
  
  return sanitized;
} 