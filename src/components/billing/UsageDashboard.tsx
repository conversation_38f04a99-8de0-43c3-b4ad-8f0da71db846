'use client';

import { TrendingUp, Receipt, Calendar, AlertTriangle, CheckCircle } from 'lucide-react';
import { UserData } from '@/components/settings/SettingsClient';
import { getTierById, formatReceiptLimit, getUpgradeRecommendation } from '@/lib/subscription/tiers';

interface UsageDashboardProps {
  userData: UserData;
}

export default function UsageDashboard({ userData }: UsageDashboardProps) {
  const tier = getTierById(userData.current_tier);
  const receiptsUsed = userData.receipts_used_this_period || 0;
  const monthlyLimit = userData.monthly_receipt_limit || tier?.features.receipts_per_month || 10;
  const isUnlimited = monthlyLimit === -1;
  
  const usagePercentage = isUnlimited ? 0 : Math.min(100, (receiptsUsed / monthlyLimit) * 100);
  const remainingReceipts = isUnlimited ? -1 : Math.max(0, monthlyLimit - receiptsUsed);
  
  const recommendation = getUpgradeRecommendation(
    userData.current_tier,
    receiptsUsed,
    userData.receipts_processed || 0
  );

  const getUsageColor = () => {
    if (isUnlimited) return 'text-green-400';
    if (usagePercentage >= 90) return 'text-red-400';
    if (usagePercentage >= 70) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getProgressBarColor = () => {
    if (isUnlimited) return 'bg-green-500';
    if (usagePercentage >= 90) return 'bg-red-500';
    if (usagePercentage >= 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Usage Overview */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center">
            <TrendingUp className="w-5 h-5 text-blue-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Usage Overview</h3>
            <p className="text-gray-400 text-sm">Your current billing period usage</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Receipts Used */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <Receipt className="w-5 h-5 text-blue-400" />
              <span className="text-sm font-medium text-gray-300">Receipts Used</span>
            </div>
            <div className="text-2xl font-bold text-white mb-2">
              {receiptsUsed}
            </div>
            <div className="text-sm text-gray-400">
              of {isUnlimited ? '∞' : monthlyLimit} this period
            </div>
            {!isUnlimited && (
              <div className="mt-3">
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
                    style={{ width: `${usagePercentage}%` }}
                  />
                </div>
                <div className={`text-xs mt-1 ${getUsageColor()}`}>
                  {usagePercentage.toFixed(1)}% used
                </div>
              </div>
            )}
          </div>

          {/* Remaining */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <CheckCircle className="w-5 h-5 text-green-400" />
              <span className="text-sm font-medium text-gray-300">Remaining</span>
            </div>
            <div className="text-2xl font-bold text-white mb-2">
              {isUnlimited ? '∞' : remainingReceipts}
            </div>
            <div className="text-sm text-gray-400">
              receipts left
            </div>
          </div>

          {/* Billing Period */}
          <div className="bg-gray-700/50 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-3">
              <Calendar className="w-5 h-5 text-purple-400" />
              <span className="text-sm font-medium text-gray-300">Billing Period</span>
            </div>
            <div className="text-sm text-white mb-1">
              {formatDate(userData.current_period_start)}
            </div>
            <div className="text-sm text-gray-400">
              to {formatDate(userData.current_period_end)}
            </div>
          </div>
        </div>
      </div>

      {/* Usage Recommendation */}
      {recommendation.shouldUpgrade && (
        <div className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20 border border-yellow-500/30 rounded-xl p-6">
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-yellow-600/20 rounded-lg flex items-center justify-center flex-shrink-0">
              <AlertTriangle className="w-5 h-5 text-yellow-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-2">
                Upgrade Recommended
              </h3>
              <p className="text-gray-300 mb-4">
                {recommendation.reason}
              </p>
              {recommendation.recommendedTier && (
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-400">
                    Consider upgrading to:
                  </span>
                  <span className="bg-yellow-600/20 text-yellow-300 px-3 py-1 rounded-full text-sm font-medium capitalize">
                    {recommendation.recommendedTier}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Usage History */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <h3 className="text-lg font-semibold text-white mb-4">
          Usage Statistics
        </h3>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {userData.receipts_processed || 0}
            </div>
            <div className="text-sm text-gray-400">Total Processed</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {receiptsUsed}
            </div>
            <div className="text-sm text-gray-400">This Period</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {isUnlimited ? '∞' : remainingReceipts}
            </div>
            <div className="text-sm text-gray-400">Remaining</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {isUnlimited ? '0' : Math.round(usagePercentage)}%
            </div>
            <div className="text-sm text-gray-400">Used</div>
          </div>
        </div>
      </div>

      {/* Next Billing */}
      {userData.subscription_status === 'active' && userData.next_billing_date && (
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-white mb-4">
            Next Billing
          </h3>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300">
                Your next billing date is <span className="font-medium text-white">
                  {formatDate(userData.next_billing_date)}
                </span>
              </p>
              <p className="text-sm text-gray-400 mt-1">
                Usage will reset on this date
              </p>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold text-white">
                {tier?.price_display || 'Free'}
              </div>
              <div className="text-sm text-gray-400">
                {tier?.billing_cycle || 'monthly'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
