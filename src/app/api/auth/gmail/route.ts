import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getGmailAuthUrl } from '@/lib/gmail/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the base URL for absolute redirects
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.NEXT_PUBLIC_SITE_URL || 
                   `${request.nextUrl.protocol}//${request.nextUrl.host}`;

    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.redirect(`${baseUrl}/login?error=authentication_required`);
    }

    // Check if user has Business tier access
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.redirect(`${baseUrl}/dashboard/gmail?error=user_not_found`);
    }

    if (userData.current_tier !== 'business' || userData.subscription_status !== 'active') {
      return NextResponse.redirect(`${baseUrl}/dashboard/gmail?error=business_plan_required`);
    }

    // Get the Gmail OAuth authorization URL
    const authUrl = getGmailAuthUrl(user.id);
    
    // Redirect user to Gmail OAuth consent screen
    return NextResponse.redirect(authUrl);
  } catch (error) {
    console.error('Error generating Gmail OAuth URL:', error);
    // Get the base URL for error redirect
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.NEXT_PUBLIC_SITE_URL || 
                   `${request.nextUrl.protocol}//${request.nextUrl.host}`;
    return NextResponse.redirect(`${baseUrl}/dashboard/gmail?error=gmail_auth_failed`);
  }
}
