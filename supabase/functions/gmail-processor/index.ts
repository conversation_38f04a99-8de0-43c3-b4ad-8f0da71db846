import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient, SupabaseClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";
import { corsHeaders } from "../_shared/cors.ts";

// Gmail processing interfaces
interface GmailUser {
  user_id: string;
  gmail_email: string;
  gmail_access_token: string;
  gmail_refresh_token: string;
  gmail_last_processed_date: string | null;
  gmail_keywords: string;
}

interface GmailProcessingResult {
  success: boolean;
  userId: string;
  processedEmails: number;
  receiptsFound: number;
  receiptsCreated: number;
  errors: string[];
  processingTime: number;
}

interface BatchProcessingResult {
  success: boolean;
  processedUsers: number;
  totalReceipts: number;
  totalEmails: number;
  errors: string[];
  executionTime: number;
  timestamp: string;
}

// Initialize Supabase client
function createSupabaseClient(): SupabaseClient {
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  
  return createClient(supabaseUrl, supabaseServiceKey);
}

// Process Gmail for a single user
async function processGmailForUser(
  userId: string,
  userEmail: string,
  keywords: string[]
): Promise<GmailProcessingResult> {
  const startTime = Date.now();
  
  console.log(`🚀 Processing Gmail for user ${userId} (${userEmail})`);
  
  const result: GmailProcessingResult = {
    success: false,
    userId,
    processedEmails: 0,
    receiptsFound: 0,
    receiptsCreated: 0,
    errors: [],
    processingTime: 0
  };

  try {
    // Call the main application's Gmail processing API
    const appUrl = Deno.env.get('NEXT_PUBLIC_SITE_URL') || 'http://localhost:3000';
    
    // Calculate date range (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const response = await fetch(`${appUrl}/api/gmail/process-internal`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')}`,
        'User-Agent': 'Supabase-Edge-Function/1.0'
      },
      body: JSON.stringify({
        userId,
        dateAfter: yesterday.toISOString(),
        keywords: keywords,
        maxEmails: 50,
        minConfidence: 60
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Gmail processing API failed: ${response.status} - ${errorText}`);
    }

    const apiResult = await response.json();
    
    if (apiResult.success) {
      result.success = true;
      result.processedEmails = apiResult.data.processedEmails || 0;
      result.receiptsFound = apiResult.data.receiptsFound || 0;
      result.receiptsCreated = apiResult.data.receiptsCreated || 0;
      result.errors = apiResult.data.errors || [];
    } else {
      result.errors.push(apiResult.error || 'Unknown API error');
    }

  } catch (error) {
    console.error(`❌ Failed to process Gmail for user ${userId}:`, error);
    result.errors.push(error instanceof Error ? error.message : 'Unknown error');
  }

  result.processingTime = Date.now() - startTime;
  
  console.log(`✅ Gmail processing complete for user ${userId}: ${result.receiptsCreated} receipts in ${result.processingTime}ms`);
  
  return result;
}

// Process Gmail for all users at a specific hour
async function processGmailForAllUsers(targetHour: number): Promise<BatchProcessingResult> {
  const startTime = Date.now();
  const timestamp = new Date().toISOString();
  
  console.log(`🕐 Starting Gmail batch processing for hour ${targetHour}`);
  
  const result: BatchProcessingResult = {
    success: false,
    processedUsers: 0,
    totalReceipts: 0,
    totalEmails: 0,
    errors: [],
    executionTime: 0,
    timestamp
  };

  try {
    const supabase = createSupabaseClient();
    
    // Get users ready for processing at this hour
    const { data: users, error } = await supabase.rpc('get_users_for_gmail_processing', {
      target_hour: targetHour
    });

    if (error) {
      throw new Error(`Failed to get users for Gmail processing: ${error.message}`);
    }

    if (!users || users.length === 0) {
      console.log('📭 No users ready for Gmail processing at this hour');
      result.success = true;
      result.executionTime = Date.now() - startTime;
      return result;
    }

    console.log(`👥 Processing Gmail for ${users.length} users`);

    // Process each user sequentially to avoid overwhelming the system
    for (const user of users as GmailUser[]) {
      try {
        const keywords = user.gmail_keywords 
          ? user.gmail_keywords.split(',').map(k => k.trim())
          : ['receipt', 'invoice', 'purchase', 'order confirmation'];

        const userResult = await processGmailForUser(
          user.user_id,
          user.gmail_email,
          keywords
        );

        if (userResult.success) {
          result.processedUsers++;
          result.totalReceipts += userResult.receiptsCreated;
          result.totalEmails += userResult.processedEmails;
        } else {
          result.errors.push(`User ${user.user_id}: ${userResult.errors.join(', ')}`);
        }

        // Small delay between users to be respectful to APIs
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.error(`❌ Failed to process Gmail for user ${user.user_id}:`, error);
        result.errors.push(`User ${user.user_id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    result.success = true;

  } catch (error) {
    console.error('❌ Gmail batch processing failed:', error);
    result.errors.push(error instanceof Error ? error.message : 'Unknown error');
  }

  result.executionTime = Date.now() - startTime;
  
  console.log(`🎉 Gmail batch processing complete: ${result.processedUsers} users, ${result.totalReceipts} receipts in ${result.executionTime}ms`);
  
  return result;
}

// Main handler
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    console.log('📧 Gmail processor edge function called');
    
    const { targetHour, source, timestamp } = await req.json();
    
    // Validate input
    if (typeof targetHour !== 'number' || targetHour < 0 || targetHour > 23) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid targetHour. Must be between 0 and 23.' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    console.log(`🕐 Processing Gmail for users at hour ${targetHour}`);
    console.log(`📊 Request source: ${source}, timestamp: ${timestamp}`);

    // Process Gmail for all users at the target hour
    const result = await processGmailForAllUsers(targetHour);

    // Log results for monitoring
    console.log('📊 Gmail processing results:', {
      success: result.success,
      processedUsers: result.processedUsers,
      totalReceipts: result.totalReceipts,
      totalEmails: result.totalEmails,
      errorCount: result.errors.length,
      executionTime: result.executionTime
    });

    return new Response(
      JSON.stringify(result),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('❌ Gmail processor edge function error:', error);
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
