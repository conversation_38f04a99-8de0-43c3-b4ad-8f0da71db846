import { NextRequest, NextResponse } from 'next/server';
import { exportReceiptToGoogleSheets } from '@/lib/google-sheets/export';
import { ExtractedReceiptData } from '@/lib/types/receipt';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, receiptId, extractedData, originalFileName } = body;

    // Validate required fields
    if (!userId || !receiptId || !extractedData || !originalFileName) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing required fields: userId, receiptId, extractedData, originalFileName' 
        },
        { status: 400 }
      );
    }

    // Export to Google Sheets using the enhanced implementation
    const result = await exportReceiptToGoogleSheets(
      userId,
      receiptId,
      extractedData as ExtractedReceiptData,
      originalFileName
    );

    return NextResponse.json(result);
  } catch (error) {
    console.error('Google Sheets export API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}
