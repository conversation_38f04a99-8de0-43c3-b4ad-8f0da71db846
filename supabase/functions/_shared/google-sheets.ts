import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Google Sheets API types
interface GoogleSheetsClient {
  spreadsheets: {
    create: (params: any) => Promise<any>;
    values: {
      append: (params: any) => Promise<any>;
      update: (params: any) => Promise<any>;
    };
    batchUpdate: (params: any) => Promise<any>;
  };
}

interface ExtractedReceiptData {
  vendor?: string;
  vendor_tax_id?: string | null;
  date?: string;
  currency?: string;
  payment_method?: string;
  items?: Array<{
    description: string;
    total: number;
    category?: string;
  }>;
  subtotal?: number;
  tax_rate_percent?: number;
  tax_amount?: number;
  total_amount?: number;
}

interface GoogleSheetInfo {
  id: string;
  url: string;
  name: string;
  year: number;
}

/**
 * Refresh Google OAuth access token using refresh token
 */
async function refreshGoogleToken(userId: string, refreshToken: string): Promise<string> {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  console.log(`Refreshing Google token for user ${userId}`);

  const refreshResponse = await fetch('https://oauth2.googleapis.com/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      client_id: Deno.env.get('GOOGLE_CLIENT_ID')!,
      client_secret: Deno.env.get('GOOGLE_CLIENT_SECRET')!,
      refresh_token: refreshToken,
      grant_type: 'refresh_token',
    }),
  });

  if (!refreshResponse.ok) {
    const errorText = await refreshResponse.text();
    console.error('Token refresh failed:', {
      status: refreshResponse.status,
      statusText: refreshResponse.statusText,
      error: errorText
    });
    throw new Error(`Token refresh failed: ${refreshResponse.status} ${refreshResponse.statusText} - ${errorText}`);
  }

  const tokenData = await refreshResponse.json();

  if (!tokenData.access_token) {
    console.error('No access token in refresh response:', tokenData);
    throw new Error('No access token received from token refresh');
  }

  // Update the user's access token in the database
  const { error: updateError } = await supabase
    .from('users')
    .update({
      google_access_token: tokenData.access_token,
      // Update refresh token if a new one is provided
      ...(tokenData.refresh_token && { google_refresh_token: tokenData.refresh_token })
    })
    .eq('id', userId);

  if (updateError) {
    console.error('Failed to update user tokens:', updateError);
    throw new Error(`Failed to update user tokens: ${updateError.message}`);
  }

  console.log('Successfully refreshed and updated Google token');
  return tokenData.access_token;
}

// Cache for authenticated clients to avoid repeated token fetches
const clientCache = new Map<string, { sheets: GoogleSheetsClient; accessToken: string; expires: number }>();

/**
 * Create authenticated Google Sheets client for a user with automatic token refresh and caching
 */
async function createAuthenticatedSheetsClient(userId: string): Promise<{ sheets: GoogleSheetsClient; accessToken: string }> {
  // Check cache first (5-minute cache)
  const cached = clientCache.get(userId);
  if (cached && cached.expires > Date.now()) {
    console.log('Using cached Google Sheets client for user:', userId);
    return { sheets: cached.sheets, accessToken: cached.accessToken };
  }

  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  // Get user's Google tokens
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('google_access_token, google_refresh_token')
    .eq('id', userId)
    .single();

  if (userError) {
    console.error('Failed to fetch user data:', userError);
    throw new Error(`Failed to fetch user data: ${userError.message}`);
  }

  if (!user?.google_access_token) {
    console.error('User has not connected Google account:', { userId, hasUser: !!user });
    throw new Error('User has not connected Google account');
  }

  console.log('User Google auth status:', {
    userId,
    hasAccessToken: !!user.google_access_token,
    hasRefreshToken: !!user.google_refresh_token
  });

  let accessToken = user.google_access_token;

  // Function to make authenticated API calls with automatic token refresh
  async function makeAuthenticatedRequest(url: string, options: RequestInit): Promise<Response> {
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
      ...options.headers
    };

    let response = await fetch(url, {
      ...options,
      headers
    });

    // If we get a 401, try to refresh the token and retry
    if (response.status === 401 && user.google_refresh_token) {
      console.log('Access token expired, attempting to refresh...');
      try {
        accessToken = await refreshGoogleToken(userId, user.google_refresh_token);

        // Retry the request with the new token
        response = await fetch(url, {
          ...options,
          headers: {
            ...headers,
            'Authorization': `Bearer ${accessToken}`
          }
        });

        console.log('Request retried with refreshed token, status:', response.status);
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        throw new Error(`Authentication failed and token refresh failed: ${refreshError.message}`);
      }
    }

    return response;
  }

  // Initialize Google Sheets API client (simplified for Edge Function)
  const googleApiUrl = 'https://sheets.googleapis.com/v4/spreadsheets';

  // Return a client that makes authenticated API calls with automatic token refresh
  const sheets: GoogleSheetsClient = {
    spreadsheets: {
      create: async (params: any) => {
        const response = await makeAuthenticatedRequest(googleApiUrl, {
          method: 'POST',
          body: JSON.stringify(params.resource)
        });
        return await response.json();
      },
      values: {
        append: async (params: any) => {
          const url = `${googleApiUrl}/${params.spreadsheetId}/values/${params.range}:append?valueInputOption=${params.valueInputOption}`;
          const response = await makeAuthenticatedRequest(url, {
            method: 'POST',
            body: JSON.stringify(params.requestBody)
          });
          return await response.json();
        },
        update: async (params: any) => {
          const url = `${googleApiUrl}/${params.spreadsheetId}/values/${params.range}?valueInputOption=${params.valueInputOption}`;
          const response = await makeAuthenticatedRequest(url, {
            method: 'PUT',
            body: JSON.stringify(params.requestBody)
          });
          return await response.json();
        }
      },
      batchUpdate: async (params: any) => {
        const url = `${googleApiUrl}/${params.spreadsheetId}:batchUpdate`;
        const response = await makeAuthenticatedRequest(url, {
          method: 'POST',
          body: JSON.stringify(params.requestBody)
        });
        return await response.json();
      }
    }
  };

  // Cache the client for 5 minutes
  clientCache.set(userId, {
    sheets,
    accessToken,
    expires: Date.now() + 5 * 60 * 1000 // 5 minutes
  });

  return { sheets, accessToken };
}

/**
 * Get or create current year's Google Sheet for user
 */
async function getOrCreateCurrentYearSheet(userId: string): Promise<GoogleSheetInfo> {
  const currentYear = new Date().getFullYear();
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  // Check if sheet exists for current year
  const { data: existingSheet } = await supabase
    .from('google_sheets')
    .select('*')
    .eq('user_id', userId)
    .eq('year', currentYear)
    .single();

  if (existingSheet) {
    return {
      id: existingSheet.sheet_id,
      url: existingSheet.sheet_url,
      name: existingSheet.sheet_name,
      year: existingSheet.year
    };
  }

  // Create new sheet
  const { sheets } = await createAuthenticatedSheetsClient(userId);
  
  // Get user info for sheet naming
  const { data: user } = await supabase
    .from('users')
    .select('full_name, email')
    .eq('id', userId)
    .single();

  const userName = user?.full_name || user?.email?.split('@')[0] || 'User';
  const sheetName = `Receipts ${currentYear} - ${userName}`;

  // Create the spreadsheet
  const spreadsheet = await sheets.spreadsheets.create({
    resource: {
      properties: {
        title: sheetName
      },
      sheets: [{
        properties: {
          title: 'Receipts',
          gridProperties: {
            rowCount: 1000,
            columnCount: 14
          }
        }
      }]
    }
  });

  // Add headers
  const headers = [
    'Receipt ID', 'Vendor', 'Vendor Tax ID', 'Date', 'Currency',
    'Payment Method', 'Item Description', 'Item Category', 'Item Total',
    'Subtotal', 'Tax Rate %', 'Tax Amount', 'Total Amount', 'Source File'
  ];

  await sheets.spreadsheets.values.update({
    spreadsheetId: spreadsheet.spreadsheetId,
    range: 'Receipts!A1:N1',
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: [headers]
    }
  });

  // Style the header row
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId: spreadsheet.spreadsheetId,
    requestBody: {
      requests: [{
        repeatCell: {
          range: {
            sheetId: 0,
            startRowIndex: 0,
            endRowIndex: 1,
            startColumnIndex: 0,
            endColumnIndex: 14
          },
          cell: {
            userEnteredFormat: {
              backgroundColor: { red: 0.2, green: 0.2, blue: 0.2 },
              textFormat: {
                foregroundColor: { red: 1, green: 1, blue: 1 },
                bold: true
              }
            }
          },
          fields: 'userEnteredFormat(backgroundColor,textFormat)'
        }
      }]
    }
  });

  // Save to database
  const { data: newSheet } = await supabase
    .from('google_sheets')
    .insert({
      user_id: userId,
      year: currentYear,
      sheet_id: spreadsheet.spreadsheetId,
      sheet_url: spreadsheet.spreadsheetUrl,
      sheet_name: sheetName,
      last_row_number: 1,
      total_receipts: 0
    })
    .select()
    .single();

  return {
    id: spreadsheet.spreadsheetId,
    url: spreadsheet.spreadsheetUrl,
    name: sheetName,
    year: currentYear
  };
}

/**
 * Export extracted receipt data to the user's Google Sheet
 */
export async function exportReceiptToGoogleSheets(
  userId: string,
  receiptId: string,
  extractedData: ExtractedReceiptData,
  originalFileName: string
): Promise<{ success: boolean; rowNumber?: number; error?: string }> {
  try {
    console.log(`🔄 Starting Google Sheets export for receipt ${receiptId}, user ${userId}`);

    // Validate environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    const googleClientId = Deno.env.get('GOOGLE_CLIENT_ID');
    const googleClientSecret = Deno.env.get('GOOGLE_CLIENT_SECRET');

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase environment variables');
    }

    if (!googleClientId || !googleClientSecret) {
      throw new Error('Missing Google OAuth environment variables');
    }

    console.log('✅ Environment variables validated');

    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get user's Google tokens
    const { data: user } = await supabase
      .from('users')
      .select('google_access_token, google_refresh_token')
      .eq('id', userId)
      .single();

    if (!user?.google_access_token) {
      console.log('User has not connected Google account, skipping Google Sheets export');
      return { success: false, error: 'User has not connected Google account' };
    }

    // Get current year
    const currentYear = new Date().getFullYear();
    
    // Check if sheet exists for current year
    const { data: existingSheet } = await supabase
      .from('google_sheets')
      .select('*')
      .eq('user_id', userId)
      .eq('year', currentYear)
      .single();

    let sheetId = existingSheet?.sheet_id;
    let lastRow = existingSheet?.last_row_number || 1;

    // If no sheet exists, create one
    if (!sheetId) {
      console.log('📝 Creating new Google Sheet for user');

      try {
        // Get user info for sheet naming
        const { data: userInfo, error: userInfoError } = await supabase
          .from('users')
          .select('full_name, email')
          .eq('id', userId)
          .single();

        if (userInfoError) {
          console.error('Failed to get user info for sheet naming:', userInfoError);
        }

        const userName = userInfo?.full_name || userInfo?.email?.split('@')[0] || 'User';
        const sheetName = `Receipts ${currentYear} - ${userName}`;
        console.log(`📊 Creating sheet: "${sheetName}"`);

        // Get authenticated client for creating the sheet
        console.log('🔐 Getting authenticated Google Sheets client...');
        const { sheets: authSheets } = await createAuthenticatedSheetsClient(userId);
        console.log('✅ Got authenticated client');

        // Create spreadsheet via authenticated client
        console.log('🔨 Creating Google Spreadsheet...');
        const spreadsheet = await authSheets.spreadsheets.create({
          resource: {
            properties: {
              title: sheetName
            },
            sheets: [{
              properties: {
                title: 'Receipts',
                gridProperties: {
                  rowCount: 1000,
                  columnCount: 14
                }
              }
            }]
          }
        });

        if (!spreadsheet.spreadsheetId) {
          console.error('❌ Google Sheets create response missing spreadsheetId:', spreadsheet);
          throw new Error('Google Sheets create response missing spreadsheetId');
        }

        sheetId = spreadsheet.spreadsheetId;
        console.log(`✅ Created new Google Sheet with ID: ${sheetId}`);

        // Add headers using the authenticated client
        const headers = [
          'Receipt ID', 'Vendor', 'Vendor Tax ID', 'Date', 'Currency',
          'Payment Method', 'Item Description', 'Item Category', 'Item Total',
          'Subtotal', 'Tax Rate %', 'Tax Amount', 'Total Amount', 'Source File'
        ];

        console.log('📝 Adding headers to Google Sheet...');
        await authSheets.spreadsheets.values.update({
          spreadsheetId: sheetId,
          range: 'Receipts!A1:N1',
          valueInputOption: 'USER_ENTERED',
          requestBody: {
            values: [headers]
          }
        });

        console.log('✅ Successfully added headers to Google Sheet');

      // Save to database
      const { error: insertError } = await supabase
        .from('google_sheets')
        .insert({
          user_id: userId,
          year: currentYear,
          sheet_id: sheetId,
          sheet_url: spreadsheet.spreadsheetUrl,
          sheet_name: sheetName,
          last_row_number: 1,
          total_receipts: 0
        });

      if (insertError) {
        console.error('Failed to save Google Sheet info to database:', insertError);
        throw new Error(`Failed to save Google Sheet info to database: ${insertError.message}`);
      }

        console.log('✅ Successfully saved Google Sheet info to database');
        lastRow = 1;

      } catch (createError) {
        console.error('❌ Failed to create Google Sheet:', createError);
        throw new Error(`Failed to create Google Sheet: ${createError.message}`);
      }
    }

    const nextRow = lastRow + 1;
    console.log(`📊 Preparing to export data starting at row ${nextRow}`);

    // Prepare data rows
    const rows: any[][] = [];

    if (extractedData.items && extractedData.items.length > 0) {
      console.log(`📝 Processing ${extractedData.items.length} items from receipt`);
      for (let i = 0; i < extractedData.items.length; i++) {
        const item = extractedData.items[i];
        const isFirstItem = i === 0;

        const row = [
          receiptId,
          extractedData.vendor || '',
          extractedData.vendor_tax_id || '',
          extractedData.date || '',
          extractedData.currency || 'KES',
          extractedData.payment_method || '',
          item.description || '',
          item.category || '',
          item.total || 0,
          // Only include subtotal, tax, and total for the first item to avoid duplication
          isFirstItem ? (extractedData.subtotal || 0) : '',
          isFirstItem ? (extractedData.tax_rate_percent || 0) : '',
          isFirstItem ? (extractedData.tax_amount || 0) : '',
          isFirstItem ? (extractedData.total_amount || 0) : '',
          originalFileName
        ];
        rows.push(row);
      }
    } else {
      // No items, export receipt data as single row
      const row = [
        receiptId,
        extractedData.vendor || '',
        extractedData.vendor_tax_id || '',
        extractedData.date || '',
        extractedData.currency || 'KES',
        extractedData.payment_method || '',
        'No items extracted',
        '',
        extractedData.total_amount || 0,
        extractedData.subtotal || 0,
        extractedData.tax_rate_percent || 0,
        extractedData.tax_amount || 0,
        extractedData.total_amount || 0,
        originalFileName
      ];
      rows.push(row);
    }
    
    // Get authenticated client for appending data
    console.log('🔐 Getting authenticated client for data export...');
    const { sheets: authSheets } = await createAuthenticatedSheetsClient(userId);

    // Append data to sheet with proper error handling
    console.log(`📤 Attempting to append ${rows.length} rows to Google Sheets...`);
    try {
      const appendResult = await authSheets.spreadsheets.values.append({
        spreadsheetId: sheetId,
        range: 'Receipts!A:N',
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: rows
        }
      });

      console.log('✅ Google Sheets append successful:', appendResult);

      // Verify the append was successful by checking the response
      if (!appendResult.updates || !appendResult.updates.updatedRows) {
        console.error('❌ Google Sheets append response missing expected data:', appendResult);
        throw new Error('Google Sheets append response missing expected data');
      }

      const actualRowsAdded = appendResult.updates.updatedRows;
      if (actualRowsAdded !== rows.length) {
        console.warn(`⚠️ Expected to add ${rows.length} rows but actually added ${actualRowsAdded} rows`);
      }

    } catch (appendError) {
      console.error('❌ Failed to append data to Google Sheets:', appendError);
      throw new Error(`Failed to append data to Google Sheets: ${appendError.message}`);
    }

    // Update last row number and receipt count
    const newLastRow = nextRow + rows.length - 1;
    const { error: updateError } = await supabase
      .from('google_sheets')
      .update({
        last_row_number: newLastRow,
        total_receipts: (existingSheet?.total_receipts || 0) + 1
      })
      .eq('user_id', userId)
      .eq('year', currentYear);

    if (updateError) {
      console.error('Failed to update google_sheets table:', updateError);
      throw new Error(`Failed to update google_sheets table: ${updateError.message}`);
    }

    console.log(`🎉 Successfully exported ${rows.length} rows to Google Sheets at row ${nextRow}`);
    return { success: true, rowNumber: nextRow };

  } catch (error) {
    console.error('❌ Error exporting to Google Sheets:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('❌ Full error details:', {
      message: errorMessage,
      userId,
      receiptId,
      stack: error instanceof Error ? error.stack : undefined
    });
    return {
      success: false,
      error: errorMessage
    };
  }
}

/**
 * Update the Google Sheet row number in the receipt record
 */
export async function updateReceiptSheetRowNumber(
  receiptId: string,
  rowNumber: number
): Promise<void> {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
  
  const { error } = await supabase
    .from('receipts')
    .update({ google_sheet_row_number: rowNumber })
    .eq('id', receiptId);
  
  if (error) {
    console.error('Error updating receipt sheet row number:', error);
    throw new Error('Failed to update receipt sheet row number');
  }
} 