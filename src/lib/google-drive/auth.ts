import { google } from 'googleapis';
import { createClient } from '@/lib/supabase/server';

export interface GoogleDriveTokens {
  access_token: string;
  refresh_token: string;
  expires_at?: number;
  email?: string;
}

export interface GoogleDriveAuthResult {
  success: boolean;
  tokens?: GoogleDriveTokens;
  userInfo?: {
    email: string;
    name: string;
    picture?: string;
  };
  error?: string;
}

/**
 * Creates a Google OAuth2 client configured for Drive API
 */
export function createGoogleDriveAuth() {
  const oauth2Client = new google.auth.OAuth2(
    process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    `${process.env.NEXT_PUBLIC_APP_URL}/auth/google-drive/callback`
  );

  return oauth2Client;
}

/**
 * Get the authorization URL for Google Drive OAuth consent
 */
export function getGoogleDriveAuthUrl(userId: string) {
  const oauth2Client = createGoogleDriveAuth();
  
  const scopes = [
    'https://www.googleapis.com/auth/drive.readonly', // Read access to Google Drive files and folders
    'https://www.googleapis.com/auth/userinfo.email', // Get user email
    'https://www.googleapis.com/auth/userinfo.profile', // Get user profile
  ];

  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    prompt: 'consent', // Force consent to get refresh token
    include_granted_scopes: true,
    state: userId, // Pass user ID to identify user in callback
  });

  return authUrl;
}

/**
 * Exchange authorization code for Google Drive tokens
 */
export async function exchangeCodeForDriveTokens(code: string): Promise<GoogleDriveAuthResult> {
  try {
    const oauth2Client = createGoogleDriveAuth();
    const { tokens } = await oauth2Client.getToken(code);
    
    if (!tokens.access_token) {
      return {
        success: false,
        error: 'No access token received from Google'
      };
    }

    // Set credentials to get user info
    oauth2Client.setCredentials(tokens);
    
    // Get user information
    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
    const userInfoResponse = await oauth2.userinfo.get();
    const userInfo = userInfoResponse.data;

    if (!userInfo.email) {
      return {
        success: false,
        error: 'Could not retrieve user email from Google'
      };
    }

    return {
      success: true,
      tokens: {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token || '',
        expires_at: tokens.expiry_date || undefined,
        email: userInfo.email
      },
      userInfo: {
        email: userInfo.email,
        name: userInfo.name || '',
        picture: userInfo.picture || undefined
      }
    };
  } catch (error) {
    console.error('Error exchanging code for Drive tokens:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to exchange authorization code'
    };
  }
}

/**
 * Create authenticated Google Drive client for a user
 */
export async function createAuthenticatedDriveClient(userId: string) {
  try {
    const supabase = await createClient();
    
    // Get user's Google Drive tokens from database
    const { data: user, error } = await supabase
      .from('users')
      .select('google_drive_access_token, google_drive_refresh_token, google_drive_email')
      .eq('id', userId)
      .single();

    if (error || !user) {
      throw new Error('User not found');
    }

    if (!user.google_drive_access_token) {
      throw new Error('User has not authorized Google Drive access');
    }

    const oauth2Client = createGoogleDriveAuth();
    
    // Set the user's tokens
    oauth2Client.setCredentials({
      access_token: user.google_drive_access_token,
      refresh_token: user.google_drive_refresh_token,
    });

    // Handle token refresh with better error handling
    oauth2Client.on('tokens', async (tokens) => {
      try {
        console.log('Google Drive tokens refreshed for user:', userId);
        if (tokens.refresh_token) {
          // Update both access and refresh tokens
          await updateUserGoogleDriveTokens(userId, {
            access_token: tokens.access_token!,
            refresh_token: tokens.refresh_token
          });
          console.log('Updated both access and refresh tokens');
        } else if (tokens.access_token) {
          // Update only access token
          await updateUserGoogleDriveTokens(userId, {
            access_token: tokens.access_token,
            refresh_token: user.google_drive_refresh_token! // Keep old refresh token
          });
          console.log('Updated access token only');
        }
      } catch (error) {
        console.error('Error refreshing Drive tokens for user', userId, ':', error);
        // Mark the connection as potentially invalid
        await markGoogleDriveTokensAsInvalid(userId);
      }
    });

    // Create and return authenticated Drive client
    const drive = google.drive({ version: 'v3', auth: oauth2Client });
    return { drive, oauth2Client };
  } catch (error) {
    console.error('Error creating authenticated Drive client:', error);
    throw error;
  }
}

/**
 * Update user's Google Drive tokens in the database
 */
export async function updateUserGoogleDriveTokens(
  userId: string, 
  tokens: Omit<GoogleDriveTokens, 'email'>
): Promise<void> {
  try {
    const supabase = await createClient();
    const { error } = await supabase
      .from('users')
      .update({
        google_drive_access_token: tokens.access_token,
        google_drive_refresh_token: tokens.refresh_token,
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error updating Drive tokens:', error);
    throw error;
  }
}

/**
 * Store Google Drive connection info for user
 */
export async function connectUserGoogleDrive(
  userId: string,
  tokens: GoogleDriveTokens,
  userInfo: { email: string; name: string; picture?: string }
): Promise<void> {
  try {
    const supabase = await createClient();
    const { error } = await supabase
      .from('users')
      .update({
        google_drive_connected: true,
        google_drive_email: userInfo.email,
        google_drive_access_token: tokens.access_token,
        google_drive_refresh_token: tokens.refresh_token,
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error connecting Google Drive:', error);
    throw error;
  }
}

/**
 * Disconnect Google Drive for user
 */
export async function disconnectUserGoogleDrive(userId: string): Promise<void> {
  try {
    const supabase = await createClient();
    const { error } = await supabase
      .from('users')
      .update({
        google_drive_connected: false,
        google_drive_email: null,
        google_drive_access_token: null,
        google_drive_refresh_token: null,
        google_drive_folder_id: null,
      })
      .eq('id', userId);

    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error disconnecting Google Drive:', error);
    throw error;
  }
}

/**
 * Mark Google Drive tokens as invalid (for error handling)
 */
export async function markGoogleDriveTokensAsInvalid(userId: string): Promise<void> {
  try {
    const supabase = await createClient();
    await supabase
      .from('users')
      .update({
        google_drive_connected: false
      })
      .eq('id', userId);
    console.log('Marked Google Drive tokens as invalid for user:', userId);
  } catch (error) {
    console.error('Error marking Drive tokens as invalid:', error);
  }
}

/**
 * Test and validate Google Drive tokens
 */
export async function validateGoogleDriveTokens(userId: string): Promise<{ valid: boolean; error?: string }> {
  try {
    const { drive } = await createAuthenticatedDriveClient(userId);

    // Try a simple API call to test the tokens
    await drive.files.list({
      pageSize: 1,
      fields: 'files(id)'
    });

    return { valid: true };
  } catch (error) {
    console.error('Google Drive token validation failed:', error);

    if (error instanceof Error) {
      if (error.message.includes('invalid_grant') ||
          error.message.includes('unauthorized') ||
          error.message.includes('insufficient authentication')) {
        await markGoogleDriveTokensAsInvalid(userId);
        return {
          valid: false,
          error: 'Google Drive authentication expired. Please reconnect your account.'
        };
      }
    }

    return {
      valid: false,
      error: error instanceof Error ? error.message : 'Unknown error validating tokens'
    };
  }
}

/**
 * Check if user has valid Google Drive authorization
 */
export async function hasValidGoogleDriveAuth(userId: string): Promise<boolean> {
  try {
    const supabase = await createClient();
    const { data: user, error } = await supabase
      .from('users')
      .select('google_drive_connected, google_drive_access_token, google_drive_refresh_token')
      .eq('id', userId)
      .single();

    if (error || !user?.google_drive_connected || !user?.google_drive_access_token || !user?.google_drive_refresh_token) {
      return false;
    }

    // Validate tokens by making a test API call
    const validation = await validateGoogleDriveTokens(userId);
    return validation.valid;
  } catch (error) {
    console.error('Error checking Google Drive auth:', error);
    return false;
  }
}
