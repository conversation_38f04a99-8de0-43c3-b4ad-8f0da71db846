// Comprehensive logging module for Edge Functions
// Provides structured logging with different levels and error tracking

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

export interface LogContext {
  jobId?: string;
  receiptId?: string;
  userId?: string;
  functionName?: string;
  timestamp?: string;
  requestId?: string;
  processingStep?: string;
  [key: string]: any;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  context: LogContext;
  timestamp: string;
  error?: any;
  stack?: string;
  duration?: number;
}

export class Logger {
  private static instance: Logger;
  private defaultContext: LogContext = {};

  private constructor() {}

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  setDefaultContext(context: LogContext): void {
    this.defaultContext = { ...this.defaultContext, ...context };
  }

  private formatLogEntry(level: LogLevel, message: string, context: LogContext, error?: any): LogEntry {
    const timestamp = new Date().toISOString();
    const logEntry: LogEntry = {
      level,
      message,
      context: { ...this.defaultContext, ...context, timestamp },
      timestamp,
    };

    if (error) {
      logEntry.error = error;
      if (error.stack) {
        logEntry.stack = error.stack;
      }
    }

    return logEntry;
  }

  private output(logEntry: LogEntry): void {
    const output: any = {
      level: logEntry.level.toUpperCase(),
      timestamp: logEntry.timestamp,
      message: logEntry.message,
      ...logEntry.context
    };

    if (logEntry.error) {
      output.error = logEntry.error.message || logEntry.error;
      if (logEntry.stack) {
        output.stack = logEntry.stack;
      }
    }

    // Use appropriate console method based on level
    switch (logEntry.level) {
      case 'debug':
        console.debug(JSON.stringify(output));
        break;
      case 'info':
        console.info(JSON.stringify(output));
        break;
      case 'warn':
        console.warn(JSON.stringify(output));
        break;
      case 'error':
      case 'fatal':
        console.error(JSON.stringify(output));
        break;
    }
  }

  debug(message: string, context: LogContext = {}): void {
    const logEntry = this.formatLogEntry('debug', message, context);
    this.output(logEntry);
  }

  info(message: string, context: LogContext = {}): void {
    const logEntry = this.formatLogEntry('info', message, context);
    this.output(logEntry);
  }

  warn(message: string, context: LogContext = {}, error?: any): void {
    const logEntry = this.formatLogEntry('warn', message, context, error);
    this.output(logEntry);
  }

  error(message: string, context: LogContext = {}, error?: any): void {
    const logEntry = this.formatLogEntry('error', message, context, error);
    this.output(logEntry);
  }

  fatal(message: string, context: LogContext = {}, error?: any): void {
    const logEntry = this.formatLogEntry('fatal', message, context, error);
    this.output(logEntry);
  }

  // Specialized logging methods for common scenarios
  jobStarted(jobId: string, userId: string, receiptId: string): void {
    this.info('Job processing started', {
      jobId,
      userId,
      receiptId,
      processingStep: 'job_start'
    });
  }

  jobCompleted(jobId: string, duration: number, context: LogContext = {}): void {
    this.info('Job processing completed successfully', {
      jobId,
      duration,
      processingStep: 'job_complete',
      ...context
    });
  }

  jobFailed(jobId: string, error: any, context: LogContext = {}): void {
    this.error('Job processing failed', {
      jobId,
      processingStep: 'job_failed',
      ...context
    }, error);
  }

  databaseOperation(operation: string, table: string, success: boolean, context: LogContext = {}, error?: any): void {
    const message = `Database ${operation} on ${table} ${success ? 'succeeded' : 'failed'}`;
    if (success) {
      this.info(message, {
        operation,
        table,
        processingStep: 'database_operation',
        ...context
      });
    } else {
      this.error(message, {
        operation,
        table,
        processingStep: 'database_operation',
        ...context
      }, error);
    }
  }

  validationResult(dataType: string, success: boolean, errors?: string[], context: LogContext = {}): void {
    const message = `${dataType} validation ${success ? 'passed' : 'failed'}`;
    if (success) {
      this.info(message, {
        dataType,
        processingStep: 'validation',
        ...context
      });
    } else {
      this.warn(message, {
        dataType,
        processingStep: 'validation',
        validationErrors: errors,
        ...context
      });
    }
  }

  apiCall(service: string, success: boolean, duration: number, context: LogContext = {}, error?: any): void {
    const message = `${service} API call ${success ? 'succeeded' : 'failed'}`;
    if (success) {
      this.info(message, {
        service,
        duration,
        processingStep: 'api_call',
        ...context
      });
    } else {
      this.error(message, {
        service,
        duration,
        processingStep: 'api_call',
        ...context
      }, error);
    }
  }

  performance(operation: string, duration: number, context: LogContext = {}): void {
    const level = duration > 5000 ? 'warn' : 'info'; // Warn if operation takes more than 5 seconds
    const message = `${operation} completed in ${duration}ms`;
    
    if (level === 'warn') {
      this.warn(message, {
        operation,
        duration,
        processingStep: 'performance',
        slowOperation: true,
        ...context
      });
    } else {
      this.info(message, {
        operation,
        duration,
        processingStep: 'performance',
        ...context
      });
    }
  }
}

// Performance timing utility
export class PerformanceTimer {
  private startTime: number;
  private operation: string;
  private logger: Logger;
  private context: LogContext;

  constructor(operation: string, context: LogContext = {}) {
    this.operation = operation;
    this.context = context;
    this.logger = Logger.getInstance();
    this.startTime = Date.now();
    
    this.logger.debug(`${operation} started`, {
      ...context,
      processingStep: 'timer_start'
    });
  }

  end(additionalContext: LogContext = {}): number {
    const duration = Date.now() - this.startTime;
    this.logger.performance(this.operation, duration, {
      ...this.context,
      ...additionalContext
    });
    return duration;
  }
}

// Error classification
export enum ErrorCategory {
  VALIDATION = 'validation',
  DATABASE = 'database',
  EXTERNAL_API = 'external_api',
  PROCESSING = 'processing',
  CONFIGURATION = 'configuration',
  UNKNOWN = 'unknown'
}

export interface ClassifiedError {
  category: ErrorCategory;
  message: string;
  originalError: any;
  isRetryable: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export function classifyError(error: any): ClassifiedError {
  const errorMessage = error.message || error.toString();
  const errorCode = error.code || error.status;

  // Database errors
  if (errorMessage.includes('Failed to save') || errorMessage.includes('database') || errorCode === 'PGRST') {
    return {
      category: ErrorCategory.DATABASE,
      message: errorMessage,
      originalError: error,
      isRetryable: true,
      severity: 'high'
    };
  }

  // Validation errors
  if (errorMessage.includes('validation') || errorMessage.includes('Invalid') || errorMessage.includes('required')) {
    return {
      category: ErrorCategory.VALIDATION,
      message: errorMessage,
      originalError: error,
      isRetryable: false,
      severity: 'medium'
    };
  }

  // External API errors (OpenAI, etc.)
  if (errorMessage.includes('OpenAI') || errorMessage.includes('API') || errorCode >= 400) {
    return {
      category: ErrorCategory.EXTERNAL_API,
      message: errorMessage,
      originalError: error,
      isRetryable: errorCode >= 500, // Server errors are retryable
      severity: 'medium'
    };
  }

  // Processing errors
  if (errorMessage.includes('processing') || errorMessage.includes('extraction')) {
    return {
      category: ErrorCategory.PROCESSING,
      message: errorMessage,
      originalError: error,
      isRetryable: true,
      severity: 'medium'
    };
  }

  // Configuration errors
  if (errorMessage.includes('environment') || errorMessage.includes('config') || errorMessage.includes('Missing required')) {
    return {
      category: ErrorCategory.CONFIGURATION,
      message: errorMessage,
      originalError: error,
      isRetryable: false,
      severity: 'critical'
    };
  }

  // Unknown errors
  return {
    category: ErrorCategory.UNKNOWN,
    message: errorMessage,
    originalError: error,
    isRetryable: false,
    severity: 'high'
  };
}

// Singleton logger instance
export const logger = Logger.getInstance(); 