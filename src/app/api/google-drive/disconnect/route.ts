import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { disconnectUserGoogleDrive } from '@/lib/google-drive/auth';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Disconnect Google Drive
    await disconnectUserGoogleDrive(user.id);

    return NextResponse.json({
      success: true,
      message: 'Google Drive disconnected successfully'
    });
  } catch (error) {
    console.error('Error disconnecting Google Drive:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to disconnect Google Drive' 
      }, 
      { status: 500 }
    );
  }
}
