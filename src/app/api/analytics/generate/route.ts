import { NextRequest, NextResponse } from 'next/server';
import { triggerAnalyticsGeneration } from '@/lib/data/analytics-generator';

export async function POST(request: NextRequest) {
  try {
    const result = await triggerAnalyticsGeneration();
    
    if (result.success) {
      return NextResponse.json(result, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Analytics generation API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
