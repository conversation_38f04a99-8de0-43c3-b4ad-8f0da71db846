import { google } from 'googleapis';
import { createClient } from '@/lib/supabase/server';
import { 
  GoogleSheetsLogger, 
  executeGoogleSheetsOperation, 
  GoogleSheetsErrorType,
  parseGoogleSheetsError 
} from './errors';

export interface GoogleAuthTokens {
  access_token: string;
  refresh_token: string;
  expires_at?: number;
}

/**
 * Creates a Google OAuth2 client configured for Sheets API
 */
export function createGoogleAuth() {
  const oauth2Client = new google.auth.OAuth2(
    process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    `${process.env.NEXT_PUBLIC_APP_URL}/auth/callback`
  );

  return oauth2Client;
}

/**
 * Get the authorization URL for Google OAuth consent
 */
export function getAuthorizationUrl() {
  const oauth2Client = createGoogleAuth();
  
  const scopes = [
    'https://www.googleapis.com/auth/drive.file',
    'openid',
    'email',
    'profile'
  ];

  const authUrl = oauth2Client.generateAuthUrl({
    access_type: 'offline',
    scope: scopes,
    prompt: 'consent', // Force consent to get refresh token
    include_granted_scopes: true
  });

  return authUrl;
}

/**
 * Exchange authorization code for tokens
 */
export async function exchangeCodeForTokens(code: string): Promise<GoogleAuthTokens> {
  const logger = GoogleSheetsLogger.getInstance();
  
  return executeGoogleSheetsOperation(
    async () => {
      const oauth2Client = createGoogleAuth();
      const { tokens } = await oauth2Client.getToken(code);
      
      if (!tokens.access_token) {
        throw new Error('No access token received from Google');
      }

      return {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token || '',
        expires_at: tokens.expiry_date || undefined
      };
    },
    'exchangeCodeForTokens',
    'system',
    { codeLength: code?.length }
  ).then(result => {
    if (!result.success || !result.data) {
      throw new Error(result.error?.message || 'Failed to exchange authorization code for tokens');
    }
    return result.data;
  });
}

/**
 * Create authenticated Google Sheets client for a user
 */
export async function createAuthenticatedSheetsClient(userId: string) {
  const logger = GoogleSheetsLogger.getInstance();
  
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      
      // Get user's Google tokens from database
      const { data: user, error } = await supabase
        .from('users')
        .select('google_access_token, google_refresh_token')
        .eq('id', userId)
        .single();

      if (error || !user) {
        // Use a specific error type for better handling
        throw parseGoogleSheetsError(error || new Error('User not found'), 'createAuthenticatedSheetsClient.fetchUser');
      }

      if (!user.google_access_token) {
        throw parseGoogleSheetsError(new Error('User has not authorized Google Sheets access'), 'createAuthenticatedSheetsClient.noAuth');
      }

      const oauth2Client = createGoogleAuth();
      
      // Set the user's tokens
      oauth2Client.setCredentials({
        access_token: user.google_access_token,
        refresh_token: user.google_refresh_token,
      });

      // Handle token refresh
      oauth2Client.on('tokens', async (tokens) => {
        try {
          if (tokens.refresh_token) {
            // Update both access and refresh tokens
            await updateUserGoogleTokens(userId, {
              access_token: tokens.access_token!,
              refresh_token: tokens.refresh_token
            });
          } else if (tokens.access_token) {
            // Update only access token
            await updateUserGoogleTokens(userId, {
              access_token: tokens.access_token,
              refresh_token: user.google_refresh_token! // Keep old refresh token
            });
          }
          logger.logSuccess('tokenRefresh', userId, { hasNewRefreshToken: !!tokens.refresh_token });
        } catch (error) {
          logger.logError('tokenRefresh', userId, parseGoogleSheetsError(error, 'tokenRefresh'));
        }
      });

      // Create and return authenticated Sheets client
      const sheets = google.sheets({ version: 'v4', auth: oauth2Client });
      return { sheets, oauth2Client };
    },
    'createAuthenticatedSheetsClient',
    userId
  );

  if (!result.success) {
    throw result.error;
  }
  return result.data!;
}

/**
 * Update user's Google tokens in the database
 */
export async function updateUserGoogleTokens(
  userId: string, 
  tokens: GoogleAuthTokens
): Promise<void> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      const { error } = await supabase
        .from('users')
        .update({
          google_access_token: tokens.access_token,
          google_refresh_token: tokens.refresh_token,
        })
        .eq('id', userId);

      if (error) {
        throw error; // Let the wrapper handle it
      }
    },
    'updateUserGoogleTokens',
    userId
  );

  if (!result.success) {
    throw result.error;
  }
}

/**
 * Check if user has valid Google Sheets authorization
 */
export async function hasValidGoogleAuth(userId: string): Promise<boolean> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      const { data: user, error } = await supabase
        .from('users')
        .select('google_access_token, google_refresh_token')
        .eq('id', userId)
        .single();

      if (error) {
        // If user not found, it's not an error, just means no auth
        if (error.code === 'PGRST116') return false; 
        throw error;
      }

      return !!(user?.google_access_token && user?.google_refresh_token);
    },
    'hasValidGoogleAuth',
    userId
  );

  // For this function, a failure in the operation should be treated as "false"
  // rather than throwing an error, as it's a check, not a critical operation.
  return result.success && !!result.data;
} 