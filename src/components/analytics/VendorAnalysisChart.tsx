'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { Store } from 'lucide-react';

interface VendorAnalysisChartProps {
  data: {
    vendor: string;
    totalSpent: number;
    receiptCount: number;
    averageAmount: number;
  }[];
  currency?: string;
}

export default function VendorAnalysisChart({ data, currency = 'KES' }: VendorAnalysisChartProps) {
  const hasData = data && data.length > 0;

  // Format currency based on the currency type
  const formatCurrency = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
  };

  const formatCurrencyDetailed = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  // Truncate vendor names for display
  const truncateVendor = (vendor: string, maxLength: number = 12) => {
    return vendor.length > maxLength ? `${vendor.substring(0, maxLength)}...` : vendor;
  };

  // Prepare data for chart (top 8 vendors)
  const chartData = hasData ? data.slice(0, 8).map(item => ({
    ...item,
    displayVendor: truncateVendor(item.vendor)
  })) : [];

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-white flex items-center">
            <Store className="w-5 h-5 mr-2 text-accent-purple" />
            Top Vendors
          </h3>
          <p className="text-sm text-gray-400">Your most frequent merchants</p>
        </div>
        {hasData && (
          <div className="text-right">
            <p className="text-sm text-gray-400">Total Vendors</p>
            <p className="text-lg font-semibold text-white">{data.length}</p>
          </div>
        )}
      </div>
      
      {hasData ? (
        <>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis 
                dataKey="displayVendor" 
                stroke="#9CA3AF"
                fontSize={11}
                tick={{ fill: '#9CA3AF' }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis
                stroke="#9CA3AF"
                fontSize={12}
                tick={{ fill: '#9CA3AF' }}
                tickFormatter={(value) => formatCurrency(value)}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '12px',
                  color: '#F9FAFB',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                }}
                formatter={(value: any, name: string, props: any) => {
                  const { payload } = props;
                  return [
                    <div key="vendor-tooltip" className="space-y-1">
                      <div className="font-medium">{payload.vendor}</div>
                      <div>Total Spent: {formatCurrencyDetailed(payload.totalSpent)}</div>
                      <div>Receipts: {payload.receiptCount}</div>
                      <div>Average: {formatCurrencyDetailed(payload.averageAmount)}</div>
                    </div>
                  ];
                }}
                labelFormatter={() => ''}
              />
              <Bar 
                dataKey="totalSpent" 
                fill="#8338EC"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
          
          {/* Vendor Summary List */}
          <div className="mt-6 space-y-3 max-h-40 overflow-y-auto">
            {data.slice(0, 5).map((vendor, index) => (
              <div key={vendor.vendor} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-accent-purple/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-accent-purple">#{index + 1}</span>
                  </div>
                  <div>
                    <p className="text-white font-medium">{vendor.vendor}</p>
                    <p className="text-xs text-gray-400">{vendor.receiptCount} receipts</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-medium">{formatCurrency(vendor.totalSpent)}</p>
                  <p className="text-xs text-gray-400">Avg: {formatCurrency(vendor.averageAmount)}</p>
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="h-[300px] flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gray-700/30 rounded-full flex items-center justify-center">
              <Store className="w-10 h-10" />
            </div>
            <p className="text-lg font-medium mb-2">No vendor data yet</p>
            <p className="text-sm">Upload receipts to see your top vendors</p>
          </div>
        </div>
      )}
    </div>
  );
}
