import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    const { format } = await request.json();
    
    if (!['csv', 'json', 'xlsx'].includes(format)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid format. Must be csv, json, or xlsx' 
      }, { status: 400 });
    }

    // Get all user's receipts with items
    const { data: receipts, error: receiptsError } = await supabase
      .from('receipts')
      .select(`
        *,
        receipt_items (*)
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (receiptsError) {
      console.error('Error fetching receipts for export:', receiptsError);
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to fetch receipt data' 
      }, { status: 500 });
    }

    // Transform data for export
    const exportData = receipts?.map(receipt => ({
      id: receipt.id,
      filename: receipt.original_file_name,
      vendor: receipt.vendor || '',
      date: receipt.receipt_date || '',
      currency: receipt.currency,
      subtotal: receipt.subtotal || 0,
      tax_rate: receipt.tax_rate_percent || 0,
      tax_amount: receipt.tax_amount || 0,
      total_amount: receipt.total_amount || 0,
      payment_method: receipt.payment_method || '',
      processing_status: receipt.processing_status,
      confidence_score: receipt.confidence_score || 0,
      google_sheet_row: receipt.google_sheet_row_number || '',
      created_at: receipt.created_at,
      updated_at: receipt.updated_at,
      items: receipt.receipt_items?.map((item: any) => ({
        description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price || 0,
        total_price: item.total_price,
        category: item.category || ''
      })) || []
    })) || [];

    let responseData: string | Buffer;
    let contentType: string;
    let filename: string;

    switch (format) {
      case 'csv':
        responseData = generateCSV(exportData);
        contentType = 'text/csv';
        filename = `receipts-export-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      
      case 'json':
        responseData = JSON.stringify(exportData, null, 2);
        contentType = 'application/json';
        filename = `receipts-export-${new Date().toISOString().split('T')[0]}.json`;
        break;
      
      case 'xlsx':
        // For now, we'll return CSV format for xlsx requests
        // In a real implementation, you'd use a library like 'xlsx' to generate Excel files
        responseData = generateCSV(exportData);
        contentType = 'text/csv';
        filename = `receipts-export-${new Date().toISOString().split('T')[0]}.csv`;
        break;
      
      default:
        throw new Error('Unsupported format');
    }

    return new NextResponse(responseData, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });

  } catch (error) {
    console.error('Export API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}

function generateCSV(data: any[]): string {
  if (data.length === 0) {
    return 'No data to export';
  }

  // CSV headers
  const headers = [
    'ID',
    'Filename',
    'Vendor',
    'Date',
    'Currency',
    'Subtotal',
    'Tax Rate (%)',
    'Tax Amount',
    'Total Amount',
    'Payment Method',
    'Status',
    'Confidence Score',
    'Google Sheet Row',
    'Created At',
    'Updated At',
    'Items Count',
    'Items Details'
  ];

  // Generate CSV rows
  const rows = data.map(receipt => [
    receipt.id,
    `"${receipt.filename}"`,
    `"${receipt.vendor}"`,
    receipt.date,
    receipt.currency,
    receipt.subtotal,
    receipt.tax_rate,
    receipt.tax_amount,
    receipt.total_amount,
    `"${receipt.payment_method}"`,
    receipt.processing_status,
    receipt.confidence_score,
    receipt.google_sheet_row,
    receipt.created_at,
    receipt.updated_at,
    receipt.items.length,
    `"${receipt.items.map((item: any) => 
      `${item.description} (${item.quantity}x ${item.unit_price} = ${item.total_price})`
    ).join('; ')}"`
  ]);

  // Combine headers and rows
  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
}
