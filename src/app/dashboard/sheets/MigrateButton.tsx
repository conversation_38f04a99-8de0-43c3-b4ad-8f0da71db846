'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';

export default function MigrateButton() {
  const [isLoading, setIsLoading] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleMigrate = async () => {
    setIsLoading(true);
    setStatus('idle');
    setMessage('');

    try {
      const response = await fetch('/api/google-sheets/migrate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        setStatus('success');
        setMessage(result.message || 'Migration completed successfully!');
        // Refresh the page after a short delay to show updated sheets
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        setStatus('error');
        setMessage(result.error || 'Migration failed');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Failed to migrate sheets');
      console.error('Migration error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (status === 'success') {
    return (
      <div className="flex items-center gap-2 text-green-400 text-sm">
        <CheckCircle className="w-4 h-4" />
        <span>Upgraded!</span>
      </div>
    );
  }

  if (status === 'error') {
    return (
      <div className="flex items-center gap-2 text-red-400 text-sm">
        <AlertCircle className="w-4 h-4" />
        <span>Failed</span>
      </div>
    );
  }

  return (
    <Button
      onClick={handleMigrate}
      disabled={isLoading}
      size="sm"
      className="bg-blue-600 hover:bg-blue-700 text-white"
    >
      {isLoading ? (
        <>
          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
          Upgrading...
        </>
      ) : (
        <>
          <RefreshCw className="w-4 h-4 mr-2" />
          Upgrade Sheets
        </>
      )}
    </Button>
  );
}
