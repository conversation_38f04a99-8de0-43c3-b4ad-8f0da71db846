import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { exchangeCodeForGmailTokens, connectUserGmail } from '@/lib/gmail/auth';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const state = searchParams.get('state'); // This contains the user ID
  const error = searchParams.get('error');

  // Handle OAuth errors
  if (error) {
    console.error('Gmail OAuth error:', error);
    return NextResponse.redirect(`${origin}/dashboard/gmail?error=gmail_auth_cancelled`);
  }

  if (!code || !state) {
    return NextResponse.redirect(`${origin}/dashboard/gmail?error=gmail_auth_invalid`);
  }

  try {
    // Verify user is still authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || user.id !== state) {
      return NextResponse.redirect(`${origin}/dashboard/gmail?error=gmail_auth_unauthorized`);
    }

    // Check if user has Business tier access
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.redirect(`${origin}/dashboard/gmail?error=user_not_found`);
    }

    if (userData.current_tier !== 'business' || userData.subscription_status !== 'active') {
      return NextResponse.redirect(`${origin}/dashboard/gmail?error=business_plan_required`);
    }

    // Exchange code for tokens
    const authResult = await exchangeCodeForGmailTokens(code);

    if (!authResult.success || !authResult.tokens || !authResult.userInfo) {
      console.error('Failed to exchange code for Gmail tokens:', authResult.error);
      return NextResponse.redirect(`${origin}/dashboard/gmail?error=gmail_auth_failed`);
    }

    // Store the Gmail connection
    await connectUserGmail(user.id, authResult.tokens, authResult.userInfo);

    // Redirect back to Gmail page with success
    return NextResponse.redirect(`${origin}/dashboard/gmail?gmail_connected=true&gmail_email=${encodeURIComponent(authResult.userInfo.email)}`);
  } catch (error) {
    console.error('Error in Gmail callback:', error);
    return NextResponse.redirect(`${origin}/dashboard/gmail?error=gmail_auth_error`);
  }
}
