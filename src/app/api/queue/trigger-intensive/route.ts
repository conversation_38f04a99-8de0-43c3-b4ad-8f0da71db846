import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Intensive polling trigger endpoint called')
    
    // This endpoint is called when a file is uploaded to trigger intensive polling
    // The actual intensive polling logic is handled by the SmartQueueProcessor component
    
    return NextResponse.json({
      success: true,
      message: 'Intensive polling trigger sent',
      timestamp: new Date().toISOString()
    })
    
  } catch (error) {
    console.error('Failed to trigger intensive polling:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to trigger intensive polling',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    )
  }
}
