import { google } from 'googleapis';
import { createClient } from '@/lib/supabase/server';
import { createAuthenticatedSheetsClient } from './auth';
import { getOrCreateCurrentYearSheet, updateSheetLastRow, incrementSheetReceiptCount } from './sheets';
import { executeGoogleSheetsOperation } from './errors';

interface ExtractedReceiptData {
  vendor?: string;
  vendor_tax_id?: string;
  date?: string;
  currency?: string;
  payment_method?: string;
  items?: Array<{
    description: string;
    total: number;
    category?: string;
  }>;
  subtotal?: number;
  tax_rate_percent?: number;
  tax_amount?: number;
  total_amount?: number;
}

/**
 * Export extracted receipt data to the user's Google Sheet
 */
export async function exportReceiptToGoogleSheets(
  userId: string,
  receiptId: string,
  extractedData: ExtractedReceiptData,
  originalFileName: string
): Promise<{ success: boolean; rowNumber?: number; error?: any }> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      console.log(`Exporting receipt ${receiptId} to Google Sheets for user ${userId}`);
      
      const sheetInfo = await getOrCreateCurrentYearSheet(userId);
      if (!sheetInfo) {
        throw new Error('Could not get or create Google Sheet for user.');
      }
      console.log(`Using Google Sheet: ${sheetInfo.name} (ID: ${sheetInfo.id})`);
      
      const { sheets } = await createAuthenticatedSheetsClient(userId);
      
      const supabase = await createClient();
      const { data: sheetData } = await supabase
        .from('google_sheets')
        .select('last_row_number')
        .eq('user_id', userId)
        .eq('year', sheetInfo.year)
        .single();
      
      const nextRow = (sheetData?.last_row_number || 1) + 1;
      
      const rows: any[][] = [];
      if (extractedData.items && extractedData.items.length > 0) {
        for (let i = 0; i < extractedData.items.length; i++) {
          const item = extractedData.items[i];
          const isFirstItem = i === 0;

          const row = [
            receiptId,
            extractedData.vendor || '',
            extractedData.vendor_tax_id || '',
            extractedData.date || '',
            extractedData.currency || 'KES',
            extractedData.payment_method || '',
            item.description || '',
            item.category || '',
            item.total || 0,
            // Only include subtotal, tax, and total for the first item to avoid duplication
            isFirstItem ? (extractedData.subtotal || 0) : '',
            isFirstItem ? (extractedData.tax_rate_percent || 0) : '',
            isFirstItem ? (extractedData.tax_amount || 0) : '',
            isFirstItem ? (extractedData.total_amount || 0) : '',
            originalFileName
          ];
          rows.push(row);
        }
      } else {
        const row = [
          receiptId,
          extractedData.vendor || '',
          extractedData.vendor_tax_id || '',
          extractedData.date || '',
          extractedData.currency || 'KES',
          extractedData.payment_method || '',
          'No items extracted',
          '',
          extractedData.total_amount || 0,
          extractedData.subtotal || 0,
          extractedData.tax_rate_percent || 0,
          extractedData.tax_amount || 0,
          extractedData.total_amount || 0,
          originalFileName
        ];
        rows.push(row);
      }
        
      await sheets.spreadsheets.values.append({
        spreadsheetId: sheetInfo.id,
        range: 'Receipts!A:N',
        valueInputOption: 'USER_ENTERED',
        requestBody: {
          values: rows,
        },
      });
        
      const newLastRow = nextRow + rows.length - 1;
      await updateSheetLastRow(userId, sheetInfo.year, newLastRow);
      await incrementSheetReceiptCount(userId, sheetInfo.year);

      // Queue summary sheet update for background processing (non-blocking)
      try {
        const { updateSummarySheets } = await import('./sheets');
        // Don't await - let it run in background
        updateSummarySheets(userId, sheetInfo.id).catch(summaryError => {
          console.warn('Background summary sheet update failed:', summaryError);
        });
        console.log('Summary sheet update queued for background processing');
      } catch (summaryError) {
        console.warn('Failed to queue summary sheet update:', summaryError);
        // Don't fail the main export if summary update fails
      }

      console.log(`Exported ${rows.length} item rows to Google Sheets`);
      return { rowNumber: nextRow };
    },
    'exportReceiptToGoogleSheets',
    userId,
    { receiptId }
  );

  return {
    success: result.success,
    rowNumber: result.data?.rowNumber,
    error: result.error
  };
}

/**
 * Update the Google Sheet row number in the receipt record
 */
export async function updateReceiptSheetRowNumber(
  receiptId: string,
  rowNumber: number
): Promise<void> {
  const result = await executeGoogleSheetsOperation(
    async () => {
      const supabase = await createClient();
      const { error } = await supabase
        .from('receipts')
        .update({ google_sheet_row_number: rowNumber })
        .eq('id', receiptId);
      
      if (error) {
        throw error;
      }
    },
    'updateReceiptSheetRowNumber',
    'system', // This can be a system operation
    { receiptId, rowNumber }
  );

  if (!result.success) {
    throw result.error;
  }
} 