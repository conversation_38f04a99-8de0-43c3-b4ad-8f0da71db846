import { NextRequest, NextResponse } from 'next/server';
import { processGmailForUser } from '@/lib/gmail/processor';

export async function POST(request: NextRequest) {
  try {
    // Verify this is an internal request from Supabase Edge Function
    const authHeader = request.headers.get('authorization');
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    if (!serviceKey || authHeader !== `Bearer ${serviceKey}`) {
      console.error('Unauthorized internal Gmail processing request');
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      userId,
      dateAfter,
      dateBefore,
      maxEmails = 50,
      keywords,
      minConfidence = 50  // Lowered for accountant-grade detection
    } = body;

    console.log(`📧 Processing Gmail for user ${userId} with enhanced accountant-grade detection`);
    console.log(`📅 Date range: ${dateAfter} to ${dateBefore || 'now'}`);
    console.log(`🎯 Min confidence: ${minConfidence}% (accountant-grade threshold)`);

    // Validate required parameters
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'userId is required'
      }, { status: 400 });
    }

    // Validate date inputs
    let dateAfterObj: Date | undefined;
    let dateBeforeObj: Date | undefined;

    if (dateAfter) {
      dateAfterObj = new Date(dateAfter);
      if (isNaN(dateAfterObj.getTime())) {
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid dateAfter format' 
        }, { status: 400 });
      }
    }

    if (dateBefore) {
      dateBeforeObj = new Date(dateBefore);
      if (isNaN(dateBeforeObj.getTime())) {
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid dateBefore format' 
        }, { status: 400 });
      }
    }

    // Validate other inputs
    if (maxEmails && (typeof maxEmails !== 'number' || maxEmails < 1 || maxEmails > 100)) {
      return NextResponse.json({ 
        success: false, 
        error: 'maxEmails must be between 1 and 100' 
      }, { status: 400 });
    }

    if (minConfidence && (typeof minConfidence !== 'number' || minConfidence < 0 || minConfidence > 100)) {
      return NextResponse.json({ 
        success: false, 
        error: 'minConfidence must be between 0 and 100' 
      }, { status: 400 });
    }

    console.log(`🤖 Internal Gmail processing for user ${userId}`);

    // Process Gmail for the user
    const result = await processGmailForUser({
      userId,
      dateAfter: dateAfterObj,
      dateBefore: dateBeforeObj,
      maxEmails,
      keywords: keywords ? keywords : undefined,
      minConfidence
    });

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          processedEmails: result.processedEmails,
          receiptsFound: result.receiptsFound,
          receiptsCreated: result.receiptsCreated.length,
          processingTime: result.processingTime,
          errors: result.errors
        }
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Gmail processing failed',
        details: result.errors
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error in internal Gmail processing API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process Gmail',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
