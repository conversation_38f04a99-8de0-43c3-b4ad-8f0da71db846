import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { exchangeCodeForDriveTokens, connectUserGoogleDrive } from '@/lib/google-drive/auth';

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url);
  const code = searchParams.get('code');
  const state = searchParams.get('state'); // This contains the user ID
  const error = searchParams.get('error');

  // Handle OAuth errors
  if (error) {
    console.error('Google Drive OAuth error:', error);
    return NextResponse.redirect(`${origin}/dashboard/upload?error=drive_auth_cancelled`);
  }

  if (!code || !state) {
    return NextResponse.redirect(`${origin}/dashboard/upload?error=drive_auth_invalid`);
  }

  try {
    // Verify user is still authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user || user.id !== state) {
      return NextResponse.redirect(`${origin}/dashboard/upload?error=drive_auth_unauthorized`);
    }

    // Exchange code for tokens
    const authResult = await exchangeCodeForDriveTokens(code);

    if (!authResult.success || !authResult.tokens || !authResult.userInfo) {
      console.error('Failed to exchange code for Drive tokens:', authResult.error);
      return NextResponse.redirect(`${origin}/dashboard/upload?error=drive_auth_failed`);
    }

    // Store the Google Drive connection
    await connectUserGoogleDrive(user.id, authResult.tokens, authResult.userInfo);

    // Redirect back to upload page with success
    return NextResponse.redirect(`${origin}/dashboard/upload?drive_connected=true&drive_email=${encodeURIComponent(authResult.userInfo.email)}`);
  } catch (error) {
    console.error('Error in Google Drive callback:', error);
    return NextResponse.redirect(`${origin}/dashboard/upload?error=drive_auth_error`);
  }
}
