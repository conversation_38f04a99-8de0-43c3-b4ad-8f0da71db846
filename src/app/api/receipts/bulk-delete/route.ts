import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

interface DeleteReceiptRequest {
  receiptIds: string[];
  deleteGoogleSheetRows: boolean;
}

interface DeleteResult {
  receiptId: string;
  success: boolean;
  error?: string;
  sheetRowDeleted?: boolean;
}

export async function DELETE(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const { receiptIds, deleteGoogleSheetRows }: DeleteReceiptRequest = await request.json();
    
    if (!receiptIds || !Array.isArray(receiptIds) || receiptIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Receipt IDs are required' },
        { status: 400 }
      );
    }

    // Validate that all receipts belong to the user and get their details
    const { data: receipts, error: fetchError } = await supabase
      .from('receipts')
      .select(`
        id,
        user_id,
        file_path,
        google_sheet_row_number,
        vendor,
        original_file_name
      `)
      .in('id', receiptIds)
      .eq('user_id', user.id);

    if (fetchError) {
      return NextResponse.json(
        { success: false, error: 'Failed to fetch receipts' },
        { status: 500 }
      );
    }

    if (!receipts || receipts.length !== receiptIds.length) {
      return NextResponse.json(
        { success: false, error: 'Some receipts not found or access denied' },
        { status: 404 }
      );
    }

    const results: DeleteResult[] = [];
    let successCount = 0;
    let errorCount = 0;

    // Get user's Google tokens if we need to delete sheet rows
    let userGoogleTokens = null;
    if (deleteGoogleSheetRows) {
      const { data: userData } = await supabase
        .from('users')
        .select('google_access_token, google_refresh_token')
        .eq('id', user.id)
        .single();
      
      userGoogleTokens = userData;
    }

    // Process each receipt
    for (const receipt of receipts) {
      try {
        let sheetRowDeleted = false;

        // Delete from Google Sheets first if requested and receipt has a row number
        if (deleteGoogleSheetRows && receipt.google_sheet_row_number && userGoogleTokens?.google_access_token) {
          try {
            const deleteSheetResult = await deleteGoogleSheetRow(
              userGoogleTokens.google_access_token,
              receipt.google_sheet_row_number,
              new Date().getFullYear() // Assuming current year, could be improved
            );
            sheetRowDeleted = deleteSheetResult.success;
            
            if (!deleteSheetResult.success) {
              console.warn(`Failed to delete Google Sheet row for receipt ${receipt.id}:`, deleteSheetResult.error);
            }
          } catch (sheetError) {
            console.error(`Error deleting Google Sheet row for receipt ${receipt.id}:`, sheetError);
          }
        }

        // Delete receipt items first (foreign key constraint)
        const { error: itemsError } = await supabase
          .from('receipt_items')
          .delete()
          .eq('receipt_id', receipt.id);

        if (itemsError) {
          throw new Error(`Failed to delete receipt items: ${itemsError.message}`);
        }

        // Delete the receipt from database
        const { error: receiptError } = await supabase
          .from('receipts')
          .delete()
          .eq('id', receipt.id)
          .eq('user_id', user.id);

        if (receiptError) {
          throw new Error(`Failed to delete receipt: ${receiptError.message}`);
        }

        // Delete the file from storage
        try {
          const { error: storageError } = await supabase.storage
            .from('receipts')
            .remove([receipt.file_path]);

          if (storageError) {
            console.warn(`Failed to delete file from storage for receipt ${receipt.id}:`, storageError);
            // Don't fail the entire operation for storage errors
          }
        } catch (storageError) {
          console.error(`Error deleting file from storage for receipt ${receipt.id}:`, storageError);
        }

        results.push({
          receiptId: receipt.id,
          success: true,
          sheetRowDeleted
        });
        successCount++;

      } catch (error) {
        console.error(`Error deleting receipt ${receipt.id}:`, error);
        results.push({
          receiptId: receipt.id,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        errorCount++;
      }
    }

    return NextResponse.json({
      success: errorCount === 0,
      message: `Successfully deleted ${successCount} of ${receiptIds.length} receipts`,
      results,
      summary: {
        total: receiptIds.length,
        successful: successCount,
        failed: errorCount
      }
    });

  } catch (error) {
    console.error('Bulk delete error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to delete a row from Google Sheets
async function deleteGoogleSheetRow(
  accessToken: string,
  rowNumber: number,
  year: number
): Promise<{ success: boolean; error?: string }> {
  try {
    // First, get the user's sheet for the year
    const supabase = await createClient();
    const { data: sheet } = await supabase
      .from('google_sheets')
      .select('sheet_id')
      .eq('year', year)
      .single();

    if (!sheet) {
      return { success: false, error: 'Google Sheet not found for year' };
    }

    // Delete the row using Google Sheets API
    const deleteResponse = await fetch(
      `https://sheets.googleapis.com/v4/spreadsheets/${sheet.sheet_id}:batchUpdate`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          requests: [
            {
              deleteDimension: {
                range: {
                  sheetId: 0, // Assuming first sheet
                  dimension: 'ROWS',
                  startIndex: rowNumber - 1, // 0-based index
                  endIndex: rowNumber
                }
              }
            }
          ]
        })
      }
    );

    if (!deleteResponse.ok) {
      const errorData = await deleteResponse.json().catch(() => ({}));
      return { 
        success: false, 
        error: errorData.error?.message || `HTTP ${deleteResponse.status}` 
      };
    }

    // Update the last_row_number for the sheet (decrement by 1)
    await supabase
      .from('google_sheets')
      .update({ 
        last_row_number: Math.max(1, rowNumber - 1),
        total_receipts: Math.max(0, (sheet as any).total_receipts - 1)
      })
      .eq('sheet_id', sheet.sheet_id);

    return { success: true };

  } catch (error) {
    console.error('Error deleting Google Sheet row:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}
