FROM node:18-alpine

WORKDIR /app

# Install curl for HTTP requests
RUN apk add --no-cache curl

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy cron scripts
COPY cron-runner.js ./
COPY entrypoint.sh ./

# Make entrypoint executable
RUN chmod +x entrypoint.sh

# Install cron
RUN apk add --no-cache dcron

# Create crontab
RUN echo "0 * * * * node /app/cron-runner.js gmail-processor" > /etc/crontabs/root
RUN echo "0 2 * * * node /app/cron-runner.js data-retention-monitor" >> /etc/crontabs/root  
RUN echo "0 3 * * 0 node /app/cron-runner.js data-retention-cleanup" >> /etc/crontabs/root

CMD ["./entrypoint.sh"]
