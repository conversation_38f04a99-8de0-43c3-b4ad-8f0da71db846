'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Mail,
  AlertCircle,
  ArrowLeft,
  Construction,
  Clock
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function GmailPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to dashboard after 10 seconds
    const timer = setTimeout(() => {
      router.push('/dashboard');
    }, 10000);

    return () => clearTimeout(timer);
  }, [router]);

  const handleGoBack = () => {
    router.push('/dashboard');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3 mb-8">
          <div className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg">
            <Construction className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Gmail Processing - Temporarily Disabled</h1>
            <p className="text-gray-600">This feature is currently unavailable</p>
          </div>
        </div>

        {/* Main Alert */}
        <Alert className="border-orange-500 bg-orange-50">
          <Construction className="h-4 w-4" />
          <AlertDescription className="text-gray-800">
            <strong>Feature Temporarily Disabled:</strong> Gmail processing has been temporarily disabled while we complete Google verification process. This feature will be re-enabled once verification is complete.
          </AlertDescription>
        </Alert>

        {/* Information Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              About Gmail Processing
            </CardTitle>
            <CardDescription>
              What this feature does and why it is temporarily unavailable
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium mb-2">What Gmail Processing Does:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Automatically scans your Gmail for receipt attachments</li>
                <li>• Processes receipts from emails without manual upload</li>
                <li>• Integrates with your existing ReceiptLabs workflow</li>
                <li>• Available for Business plan subscribers</li>
              </ul>
            </div>

            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="font-medium mb-2">Why It is Temporarily Disabled:</h3>
              <p className="text-sm text-gray-600">
                Google requires verification for applications that access Gmail data. We are currently going through this verification process to ensure the highest security standards for your data. The feature will be re-enabled once verification is complete.
              </p>
            </div>

            <div className="bg-green-50 rounded-lg p-4">
              <h3 className="font-medium mb-2">Alternative Options:</h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Continue uploading receipts manually via the Upload page</li>
                <li>• Use Google Drive integration for batch processing</li>
                <li>• All other ReceiptLabs features remain fully functional</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <Button onClick={handleGoBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/upload')}
            className="flex items-center gap-2"
          >
            <Mail className="h-4 w-4" />
            Upload Receipts Instead
          </Button>
        </div>

        {/* Auto-redirect notice */}
        <div className="text-center text-sm text-gray-500 mt-8">
          <Clock className="h-4 w-4 inline mr-1" />
          You will be automatically redirected to the dashboard in 10 seconds
        </div>
      </div>
    </div>
  );
}