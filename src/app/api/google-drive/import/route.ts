import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { downloadFileFromGoogleDrive } from '@/lib/google-drive/client';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const { fileIds } = await request.json();

    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'File IDs array is required' },
        { status: 400 }
      );
    }

    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('Authentication failed:', authError);
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`🔐 Authenticated user: ${user.id} (${user.email})`);

    // Check if user has connected Google Drive
    const { data: userData } = await supabase
      .from('users')
      .select('google_drive_connected')
      .eq('id', user.id)
      .single();

    if (!userData?.google_drive_connected) {
      return NextResponse.json(
        { success: false, error: 'Google Drive not connected' },
        { status: 400 }
      );
    }

    const results = [];
    const errors = [];

    // Process each file
    for (const fileId of fileIds) {
      try {
        console.log(`Processing Google Drive file: ${fileId}`);

        // Download file from Google Drive
        const downloadResult = await downloadFileFromGoogleDrive(user.id, fileId);
        
        if (!downloadResult.success || !downloadResult.buffer) {
          errors.push(`Failed to download file ${fileId}: ${downloadResult.error}`);
          continue;
        }

        // Upload to Supabase storage first (using same path structure as regular uploads)
        const timestamp = Date.now();
        const fileExtension = downloadResult.fileName?.split('.').pop() || 'bin';
        const fileName = `gdrive_${fileId}_${timestamp}.${fileExtension}`;
        const filePath = `${user.id}/${new Date().getFullYear()}/${fileName}`;

        console.log(`📁 Uploading to storage path: ${filePath}`);
        console.log(`👤 User ID: ${user.id}`);
        console.log(`📄 File size: ${downloadResult.buffer.length} bytes`);
        console.log(`🎯 Content type: ${downloadResult.mimeType || 'application/octet-stream'}`);

        // Convert buffer to Uint8Array for Supabase storage
        const fileData = new Uint8Array(downloadResult.buffer);

        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('receipts')
          .upload(filePath, fileData, {
            contentType: downloadResult.mimeType || 'application/octet-stream',
            upsert: false
          });

        if (uploadError) {
          console.error(`❌ Storage upload failed for ${fileId}:`, uploadError);
          errors.push(`Failed to upload file ${fileId} to storage: ${uploadError.message}`);
          continue;
        }

        console.log(`✅ File uploaded successfully to: ${uploadData.path}`);

        // Create receipt record in database
        const { data: receipt, error: dbError } = await supabase
          .from('receipts')
          .insert({
            user_id: user.id,
            original_file_name: downloadResult.fileName || fileName,
            file_path: filePath,
            file_size: downloadResult.buffer.length,
            mime_type: downloadResult.mimeType || 'application/octet-stream',
            processing_status: 'pending'
          })
          .select()
          .single();

        if (dbError) {
          // Clean up uploaded file on DB error
          await supabase.storage
            .from('receipts')
            .remove([filePath]);

          errors.push(`Failed to create receipt record for file ${fileId}: ${dbError.message}`);
          continue;
        }

        const receiptId = receipt.id;

        // OPTIMIZATION: Always create job immediately for Google Drive imports
        // This bypasses webhook delays and ensures immediate processing
        console.log('🚀 Creating immediate processing job for Google Drive import...');

        try {
          // Import here to avoid issues if not needed
          const { createProcessReceiptJob } = await import('@/lib/jobs');
          const { addJobToQueue } = await import('@/lib/queue');

          // Create high-priority job for Google Drive imports (faster processing)
          const job = createProcessReceiptJob({
            receiptId,
            userId: user.id,
            imageUrl: filePath,
            fileName: downloadResult.fileName || fileName,
            fileSize: downloadResult.buffer.length,
            priority: 'high' // High priority for Google Drive imports
          });

          // Add job to Redis queue immediately
          await addJobToQueue(job);

          // Update receipt with job ID and mark as processing
          await supabase
            .from('receipts')
            .update({
              redis_job_id: job.id,
              processing_status: 'processing' // Mark as processing immediately
            })
            .eq('id', receiptId);

          console.log(`✅ High-priority job created for Google Drive import: ${job.id}`);

          // Trigger immediate processing by calling pipeline API
          try {
            const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
            const pipelineResponse = await fetch(`${baseUrl}/api/pipeline`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'GoogleDrive-Import/1.0'
              }
            });

            if (pipelineResponse.ok) {
              console.log('⚡ Immediate processing triggered for Google Drive import');
            } else {
              console.warn('⚠️ Immediate processing trigger failed, job will be picked up by queue processor');
            }
          } catch (triggerError) {
            console.warn('⚠️ Failed to trigger immediate processing:', triggerError);
            // Job is still in queue, so it will be processed eventually
          }

        } catch (jobError) {
          console.error('❌ Failed to create immediate job for Google Drive import:', jobError);
          // Fall back to webhook/polling system
          console.log('📞 Falling back to webhook/polling system for processing');
        }

        results.push({
          fileId,
          receiptId,
          status: 'processing' // Immediate processing started
        });

        console.log(`✅ Successfully started immediate processing for Google Drive file ${fileId}`);
      } catch (error) {
        console.error(`Error processing Google Drive file ${fileId}:`, error);
        errors.push(`Error processing file ${fileId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    const response = {
      success: results.length > 0,
      data: {
        processed: results.length,
        total: fileIds.length,
        results,
        errors: errors.length > 0 ? errors : undefined
      }
    };

    console.log('📤 Import API response:', response);
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error importing from Google Drive:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to import from Google Drive' },
      { status: 500 }
    );
  }
}
