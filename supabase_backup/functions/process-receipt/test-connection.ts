import { createClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";

// Simple test to verify Supabase client connection
async function testSupabaseConnection() {
  try {
    console.log('Testing Supabase connection...');
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL')!,
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false
        }
      }
    );
    
    // Test 1: Simple query to check connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    
    // Test 2: Check storage bucket access
    const { data: buckets, error: storageError } = await supabase
      .storage
      .listBuckets();
    
    if (storageError) {
      console.error('❌ Storage access failed:', storageError.message);
      return false;
    }
    
    console.log('✅ Storage access successful');
    console.log('Available buckets:', buckets?.map(b => b.name));
    
    return true;
  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return false;
  }
}

// Run test if this file is executed directly
if (import.meta.main) {
  await testSupabaseConnection();
} 