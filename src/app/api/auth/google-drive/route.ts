import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getGoogleDriveAuthUrl } from '@/lib/google-drive/auth';

export async function GET(request: NextRequest) {
  try {
    // Get the base URL for absolute redirects
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.NEXT_PUBLIC_SITE_URL || 
                   `${request.nextUrl.protocol}//${request.nextUrl.host}`;

    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.redirect(`${baseUrl}/login?error=authentication_required`);
    }

    // Get the Google Drive OAuth authorization URL
    const authUrl = getGoogleDriveAuthUrl(user.id);
    
    // Redirect user to Google OAuth consent screen
    return NextResponse.redirect(authUrl);
  } catch (error) {
    console.error('Error generating Google Drive OAuth URL:', error);
    // Get the base URL for error redirect
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.NEXT_PUBLIC_SITE_URL || 
                   `${request.nextUrl.protocol}//${request.nextUrl.host}`;
    return NextResponse.redirect(`${baseUrl}/dashboard/upload?error=drive_auth_failed`);
  }
}
