import { NextRequest, NextResponse } from 'next/server';
import { getEnhancedAnalyticsData, AnalyticsFilters } from '@/lib/data/analytics';

export async function POST(request: NextRequest) {
  try {
    const filters: AnalyticsFilters = await request.json();
    
    // Validate filters
    if (!filters.period) {
      return NextResponse.json(
        { error: 'Period is required' },
        { status: 400 }
      );
    }

    const analyticsData = await getEnhancedAnalyticsData(filters);
    
    return NextResponse.json(analyticsData, { status: 200 });
  } catch (error) {
    console.error('Enhanced analytics API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
