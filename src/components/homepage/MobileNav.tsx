'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Menu, X } from 'lucide-react'

export default function MobileNav() {
  const [isOpen, setIsOpen] = useState(false)

  const toggleMenu = () => setIsOpen(!isOpen)

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  return (
    <>
      {/* Mobile menu button */}
      <button
        onClick={toggleMenu}
        className="md:hidden p-2 text-gray-300 hover:text-white transition-colors"
        aria-label="Toggle menu"
      >
        {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
      </button>

      {/* Mobile menu overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-[9999] md:hidden">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black/50 backdrop-blur-sm"
            onClick={toggleMenu}
          />

          {/* Menu panel */}
          <div className="fixed top-0 right-0 h-full w-64 bg-gray-900 border-l border-gray-700 shadow-2xl relative z-[10000] transform transition-transform duration-300 ease-in-out">
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div className="flex items-center">
                <img src="/receiptlabs-logo.svg" alt="ReceiptLabs" className="h-10 w-auto" />
              </div>
              <button
                onClick={toggleMenu}
                className="p-2 text-gray-300 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <nav className="p-6 space-y-6">
              <a 
                href="#features" 
                className="block text-gray-300 hover:text-white transition-colors text-lg"
                onClick={toggleMenu}
              >
                Features
              </a>
              <a 
                href="#how-it-works" 
                className="block text-gray-300 hover:text-white transition-colors text-lg"
                onClick={toggleMenu}
              >
                How it Works
              </a>
              <a 
                href="#pricing" 
                className="block text-gray-300 hover:text-white transition-colors text-lg"
                onClick={toggleMenu}
              >
                Pricing
              </a>
              
              <div className="pt-6 border-t border-gray-700">
                <Link
                  href="/login"
                  className="block w-full bg-pink-600 hover:bg-pink-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 text-center"
                  onClick={toggleMenu}
                >
                  Sign In
                </Link>
              </div>
            </nav>
          </div>
        </div>
      )}
    </>
  )
}
