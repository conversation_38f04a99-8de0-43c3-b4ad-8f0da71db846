'use client';

import { CheckCircle, X, Zap, Mail, Upload } from 'lucide-react';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function SuccessModal({ isOpen, onClose }: SuccessModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 rounded-xl border border-gray-700 max-w-md w-full p-6 relative">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Success Icon */}
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-8 h-8 text-green-400" />
          </div>
          <h2 className="text-xl font-bold text-white mb-2">Receipt Downloaded!</h2>
          <p className="text-gray-400">Your professional receipt has been generated successfully.</p>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-pink-600/20 to-purple-600/20 border border-pink-600/30 rounded-lg p-4 mb-6">
          <h3 className="text-lg font-semibold text-white mb-2">Tired of Dealing with Receipts Manually?</h3>
          <p className="text-gray-300 text-sm mb-4">
            Automate your entire receipt workflow with AI-powered processing from Gmail and Google Drive.
          </p>
          
          <div className="grid grid-cols-3 gap-3 mb-4">
            <div className="text-center">
              <div className="w-10 h-10 bg-purple-600/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Mail className="w-5 h-5 text-purple-400" />
              </div>
              <p className="text-xs text-gray-400">Gmail Auto-Processing</p>
            </div>
            <div className="text-center">
              <div className="w-10 h-10 bg-pink-600/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Upload className="w-5 h-5 text-pink-400" />
              </div>
              <p className="text-xs text-gray-400">Bulk Upload</p>
            </div>
            <div className="text-center">
              <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Zap className="w-5 h-5 text-blue-400" />
              </div>
              <p className="text-xs text-gray-400">AI Extraction</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <a
            href="/dashboard"
            className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-medium px-4 py-3 rounded-lg transition-all duration-200 text-center block"
          >
            Try <strong>Receipt</strong>Labs Free
          </a>
          <div className="flex gap-3">
            <a
              href="/"
              className="flex-1 border border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 text-center"
            >
              Learn More
            </a>
            <button
              onClick={onClose}
              className="flex-1 text-gray-400 hover:text-white font-medium px-4 py-2 rounded-lg transition-colors text-center"
            >
              Continue
            </button>
          </div>
        </div>

        {/* Small Print */}
        <p className="text-xs text-gray-500 text-center mt-4">
          Start with 10 free receipts per month. No credit card required.
        </p>
      </div>
    </div>
  );
}
