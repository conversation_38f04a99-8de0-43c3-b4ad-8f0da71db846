import { QueueJob } from './queue'
import { v4 as uuidv4 } from 'uuid'

interface CreateProcessReceiptJobData {
  receiptId: string;
  userId: string;
  priority?: 'high' | 'normal' | 'low';
  imageUrl: string;
  fileName: string;
  fileSize: number;
}

export function createProcessR<PERSON>eiptJob(
  data: CreateProcessReceiptJobData
): QueueJob {
  const { 
    receiptId, 
    userId, 
    priority = 'normal', 
    imageUrl,
    fileName,
    fileSize
  } = data;

  return {
    id: uuidv4(),
    receiptId,
    userId,
    priority,
    attempts: 0,
    maxAttempts: 3,
    createdAt: new Date().toISOString(),
    data: {
      imageUrl,
      fileName,
      fileSize,
    },
  };
} 