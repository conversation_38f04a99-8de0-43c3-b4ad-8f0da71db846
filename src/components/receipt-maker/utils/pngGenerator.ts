import html2canvas from 'html2canvas';
import { ReceiptData, CURRENCIES } from '../types';

export async function generateReceiptPNG(receiptData: ReceiptData) {
  // Create a temporary div to render the receipt
  const receiptElement = document.createElement('div');
  receiptElement.style.position = 'absolute';
  receiptElement.style.left = '-9999px';
  receiptElement.style.top = '0';
  receiptElement.style.width = '600px';
  receiptElement.style.backgroundColor = 'white';
  receiptElement.style.padding = '40px';
  receiptElement.style.fontFamily = 'Arial, sans-serif';
  receiptElement.style.color = '#000';

  // Helper function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: receiptData.currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formattedDate = new Date(receiptData.receipt.date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Build receipt HTML
  let receiptHTML = `
    <div style="max-width: 600px; margin: 0 auto; background: white; padding: 0;">
      <!-- Header -->
      <div style="text-align: center; padding: 15px; margin-bottom: 20px; border-bottom: 2px solid #000;">
        <h1 style="margin: 0; font-size: 24px; font-weight: bold; color: #000;">RECEIPT</h1>
      </div>

      <!-- Business and Receipt Info -->
      <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
        <div style="flex: 1;">
          ${receiptData.business.name ? `<div style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">${receiptData.business.name}</div>` : ''}
          ${receiptData.business.address ? `<div style="margin-bottom: 4px;">${receiptData.business.address}</div>` : ''}
          ${[receiptData.business.city, receiptData.business.state, receiptData.business.zip].filter(Boolean).length > 0 ? 
            `<div style="margin-bottom: 4px;">${[receiptData.business.city, receiptData.business.state, receiptData.business.zip].filter(Boolean).join(', ')}</div>` : ''}
          ${[receiptData.business.phone, receiptData.business.email].filter(Boolean).length > 0 ? 
            `<div style="margin-bottom: 4px;">${[receiptData.business.phone, receiptData.business.email].filter(Boolean).join(' | ')}</div>` : ''}
        </div>
        <div style="text-align: right;">
          <div style="margin-bottom: 4px;"><strong>Receipt #:</strong> ${receiptData.receipt.number}</div>
          <div style="margin-bottom: 4px;"><strong>Date:</strong> ${formattedDate}</div>
          <div style="margin-bottom: 4px;"><strong>Payment:</strong> ${receiptData.receipt.paymentMethod}</div>
        </div>
      </div>
  `;

  // Customer info if provided
  if (receiptData.customer.name) {
    receiptHTML += `
      <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-bottom: 20px;">
        <div style="font-weight: bold; color: #666; margin-bottom: 8px;">SOLD TO:</div>
        <div style="margin-bottom: 4px;">${receiptData.customer.name}</div>
        ${receiptData.customer.address ? `<div style="margin-bottom: 4px;">${receiptData.customer.address}</div>` : ''}
        ${[receiptData.customer.city, receiptData.customer.state, receiptData.customer.zip].filter(Boolean).length > 0 ? 
          `<div style="margin-bottom: 4px;">${[receiptData.customer.city, receiptData.customer.state, receiptData.customer.zip].filter(Boolean).join(', ')}</div>` : ''}
      </div>
    `;
  }

  // Items table
  receiptHTML += `
    <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-bottom: 20px;">
      <table style="width: 100%; border-collapse: collapse;">
        <thead>
          <tr style="border-bottom: 1px solid #ddd;">
            <th style="text-align: left; padding: 8px 0; font-weight: bold; color: #666;">DESCRIPTION</th>
            <th style="text-align: center; padding: 8px 0; font-weight: bold; color: #666; width: 60px;">QTY</th>
            <th style="text-align: right; padding: 8px 0; font-weight: bold; color: #666; width: 80px;">RATE</th>
            <th style="text-align: right; padding: 8px 0; font-weight: bold; color: #666; width: 80px;">AMOUNT</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add items
  receiptData.items.forEach((item) => {
    if (item.description.trim()) {
      receiptHTML += `
        <tr style="border-bottom: 1px solid #eee;">
          <td style="padding: 8px 0;">${item.description}</td>
          <td style="text-align: center; padding: 8px 0;">${item.quantity}</td>
          <td style="text-align: right; padding: 8px 0;">${formatCurrency(item.rate)}</td>
          <td style="text-align: right; padding: 8px 0;">${formatCurrency(item.amount)}</td>
        </tr>
      `;
    }
  });

  receiptHTML += `
        </tbody>
      </table>
    </div>
  `;

  // Totals
  receiptHTML += `
    <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-bottom: 20px;">
      <div style="text-align: right;">
        <div style="margin-bottom: 8px;">
          <span style="margin-right: 20px;">SUBTOTAL:</span>
          <span style="font-weight: bold;">${formatCurrency(receiptData.totals.subtotal)}</span>
        </div>
  `;

  if (receiptData.tax.rate > 0) {
    receiptHTML += `
        <div style="margin-bottom: 8px;">
          <span style="margin-right: 20px;">TAX (${receiptData.tax.rate}%):</span>
          <span style="font-weight: bold;">${formatCurrency(receiptData.tax.amount)}</span>
        </div>
    `;
  }

  if (receiptData.discount.amount > 0) {
    const discountAmount = receiptData.discount.type === 'percentage'
      ? (receiptData.totals.subtotal * receiptData.discount.amount) / 100
      : receiptData.discount.amount;

    const discountLabel = receiptData.discount.type === 'percentage'
      ? `DISCOUNT (${receiptData.discount.amount}%):`
      : 'DISCOUNT:';

    receiptHTML += `
        <div style="margin-bottom: 8px;">
          <span style="margin-right: 20px;">${discountLabel}</span>
          <span style="font-weight: bold;">-${formatCurrency(discountAmount)}</span>
        </div>
    `;
  }

  receiptHTML += `
        <div style="border-top: 1px solid #ddd; padding-top: 8px; margin-top: 8px;">
          <span style="margin-right: 20px; font-size: 18px; font-weight: bold;">TOTAL:</span>
          <span style="font-size: 18px; font-weight: bold;">${formatCurrency(receiptData.totals.total)}</span>
        </div>
      </div>
    </div>
  `;

  // Notes
  if (receiptData.notes.trim()) {
    receiptHTML += `
      <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-bottom: 20px;">
        <div style="color: #666; font-style: italic;">${receiptData.notes}</div>
      </div>
    `;
  }

  // No footer watermark
  receiptHTML += `
    </div>
  `;

  receiptElement.innerHTML = receiptHTML;
  document.body.appendChild(receiptElement);

  try {
    // Generate canvas
    const canvas = await html2canvas(receiptElement, {
      backgroundColor: 'white',
      scale: 2, // Higher resolution
      useCORS: true,
      allowTaint: true
    });

    // Convert to blob and download
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `Receipt_${receiptData.receipt.number}_${receiptData.receipt.date}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }
    }, 'image/png');

  } finally {
    // Clean up
    document.body.removeChild(receiptElement);
  }
}
