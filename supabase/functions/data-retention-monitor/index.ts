import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

interface UserRetentionStatus {
  userId: string;
  email: string;
  tier: string;
  retentionMonths: number;
  totalReceipts: number;
  expiredReceipts: number;
  oldestReceiptDate: string | null;
  newestExpiredDate: string | null;
  estimatedStorageSize: number; // in bytes
  needsCleanup: boolean;
}

interface RetentionMonitorResult {
  success: boolean;
  totalUsers: number;
  usersNeedingCleanup: number;
  totalExpiredReceipts: number;
  totalEstimatedCleanupSize: number;
  userStatuses: UserRetentionStatus[];
  tierSummary: {
    [tier: string]: {
      users: number;
      expiredReceipts: number;
      estimatedSize: number;
    };
  };
  errors: string[];
  executionTime: number;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const startTime = Date.now();
    
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('📊 Starting data retention monitoring...')

    const result: RetentionMonitorResult = {
      success: true,
      totalUsers: 0,
      usersNeedingCleanup: 0,
      totalExpiredReceipts: 0,
      totalEstimatedCleanupSize: 0,
      userStatuses: [],
      tierSummary: {
        free: { users: 0, expiredReceipts: 0, estimatedSize: 0 },
        professional: { users: 0, expiredReceipts: 0, estimatedSize: 0 },
        business: { users: 0, expiredReceipts: 0, estimatedSize: 0 }
      },
      errors: [],
      executionTime: 0
    };

    // Get all users with their current tiers and retention settings
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, email, current_tier, data_retention_months, created_at')
      .order('current_tier', { ascending: true });

    if (usersError) {
      throw new Error(`Failed to fetch users: ${usersError.message}`);
    }

    if (!users || users.length === 0) {
      console.log('No users found for monitoring');
      return new Response(JSON.stringify({
        ...result,
        executionTime: Date.now() - startTime
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    result.totalUsers = users.length;
    console.log(`📈 Monitoring ${users.length} users for data retention status`);

    // Process each user
    for (const user of users) {
      try {
        // Determine retention period for this user
        const retentionMonths = user.data_retention_months || getDefaultRetentionMonths(user.current_tier);
        
        // Initialize user status
        const userStatus: UserRetentionStatus = {
          userId: user.id,
          email: user.email,
          tier: user.current_tier,
          retentionMonths,
          totalReceipts: 0,
          expiredReceipts: 0,
          oldestReceiptDate: null,
          newestExpiredDate: null,
          estimatedStorageSize: 0,
          needsCleanup: false
        };

        // Update tier summary
        if (!result.tierSummary[user.current_tier]) {
          result.tierSummary[user.current_tier] = { users: 0, expiredReceipts: 0, estimatedSize: 0 };
        }
        result.tierSummary[user.current_tier].users++;

        // Get total receipts count for this user
        const { count: totalReceipts, error: totalError } = await supabase
          .from('receipts')
          .select('*', { count: 'exact', head: true })
          .eq('user_id', user.id);

        if (totalError) {
          result.errors.push(`Failed to count total receipts for ${user.email}: ${totalError.message}`);
          continue;
        }

        userStatus.totalReceipts = totalReceipts || 0;

        // Skip if unlimited retention
        if (retentionMonths === -1) {
          result.userStatuses.push(userStatus);
          continue;
        }

        // Calculate cutoff date
        const cutoffDate = new Date();
        cutoffDate.setMonth(cutoffDate.getMonth() - retentionMonths);

        // Find expired receipts for this user
        const { data: expiredReceipts, error: expiredError } = await supabase
          .from('receipts')
          .select('id, created_at, file_size, original_file_name')
          .eq('user_id', user.id)
          .lt('created_at', cutoffDate.toISOString())
          .order('created_at', { ascending: true });

        if (expiredError) {
          result.errors.push(`Failed to fetch expired receipts for ${user.email}: ${expiredError.message}`);
          continue;
        }

        if (expiredReceipts && expiredReceipts.length > 0) {
          userStatus.expiredReceipts = expiredReceipts.length;
          userStatus.needsCleanup = true;
          userStatus.oldestReceiptDate = expiredReceipts[0].created_at;
          userStatus.newestExpiredDate = expiredReceipts[expiredReceipts.length - 1].created_at;
          
          // Calculate estimated storage size
          const totalSize = expiredReceipts.reduce((sum, receipt) => sum + (receipt.file_size || 0), 0);
          userStatus.estimatedStorageSize = totalSize;

          // Update totals
          result.usersNeedingCleanup++;
          result.totalExpiredReceipts += expiredReceipts.length;
          result.totalEstimatedCleanupSize += totalSize;

          // Update tier summary
          result.tierSummary[user.current_tier].expiredReceipts += expiredReceipts.length;
          result.tierSummary[user.current_tier].estimatedSize += totalSize;

          console.log(`⚠️ User ${user.email} (${user.current_tier}): ${expiredReceipts.length} expired receipts, ${formatBytes(totalSize)}`);
        } else {
          console.log(`✅ User ${user.email} (${user.current_tier}): No expired receipts`);
        }

        result.userStatuses.push(userStatus);

      } catch (error) {
        const errorMsg = `Error processing user ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }

    result.executionTime = Date.now() - startTime;

    // Log summary
    console.log('📋 Data retention monitoring completed:', {
      totalUsers: result.totalUsers,
      usersNeedingCleanup: result.usersNeedingCleanup,
      totalExpiredReceipts: result.totalExpiredReceipts,
      totalEstimatedCleanupSize: formatBytes(result.totalEstimatedCleanupSize),
      errors: result.errors.length,
      executionTimeMs: result.executionTime
    });

    // Log tier breakdown
    console.log('📊 Tier breakdown:');
    Object.entries(result.tierSummary).forEach(([tier, summary]) => {
      if (summary.users > 0) {
        console.log(`  ${tier}: ${summary.users} users, ${summary.expiredReceipts} expired receipts, ${formatBytes(summary.estimatedSize)}`);
      }
    });

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });

  } catch (error) {
    console.error('💥 Fatal error in data retention monitoring:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      totalUsers: 0,
      usersNeedingCleanup: 0,
      totalExpiredReceipts: 0,
      totalEstimatedCleanupSize: 0,
      userStatuses: [],
      tierSummary: {},
      errors: [error instanceof Error ? error.message : 'Unknown error'],
      executionTime: 0
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
})

function getDefaultRetentionMonths(tier: string): number {
  switch (tier) {
    case 'free':
      return 1; // 30 days
    case 'professional':
      return 6; // 6 months
    case 'business':
      return 12; // 1 year
    default:
      return 1; // Default to free tier
  }
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
