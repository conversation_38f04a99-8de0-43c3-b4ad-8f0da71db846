'use client';

import { useState } from 'react';
import { X, Trash2, AlertTriangle, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Receipt } from './ReceiptHistoryClient';

interface BulkDeleteModalProps {
  selectedReceipts: string[];
  receipts: Receipt[];
  onClose: () => void;
  onSuccess: () => void;
}

interface DeleteProgress {
  total: number;
  completed: number;
  current: string;
  errors: string[];
}

export default function BulkDeleteModal({ selectedReceipts, receipts, onClose, onSuccess }: BulkDeleteModalProps) {
  const [deleteGoogleSheetRows, setDeleteGoogleSheetRows] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [progress, setProgress] = useState<DeleteProgress | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const receiptsWithSheetRows = receipts.filter(r => r.google_sheet_row_number);
  const receiptsWithoutSheetRows = receipts.filter(r => !r.google_sheet_row_number);

  const formatCurrency = (amount: number | null): string => {
    if (amount === null || amount === undefined) return 'N/A';
    return new Intl.NumberFormat('en-KE', { 
      style: 'currency', 
      currency: 'KES' 
    }).format(amount);
  };

  const handleDelete = async () => {
    if (!showConfirmation) {
      setShowConfirmation(true);
      return;
    }

    setIsDeleting(true);
    setProgress({
      total: selectedReceipts.length,
      completed: 0,
      current: '',
      errors: []
    });

    try {
      const response = await fetch('/api/receipts/bulk-delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          receiptIds: selectedReceipts,
          deleteGoogleSheetRows
        }),
      });

      const result = await response.json();

      if (result.success) {
        onSuccess();
      } else {
        setProgress(prev => prev ? {
          ...prev,
          errors: [...prev.errors, result.error || 'Unknown error occurred']
        } : null);
      }
    } catch (error) {
      console.error('Error deleting receipts:', error);
      setProgress(prev => prev ? {
        ...prev,
        errors: [...prev.errors, 'Network error occurred']
      } : null);
    } finally {
      setIsDeleting(false);
    }
  };

  const totalAmount = receipts.reduce((sum, receipt) => {
    return sum + (receipt.total_amount || 0);
  }, 0);

  if (isDeleting && progress) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-gray-900 border border-gray-700 rounded-xl max-w-md w-full p-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Loader2 className="w-8 h-8 text-red-400 animate-spin" />
            </div>
            
            <h3 className="text-lg font-semibold text-white mb-2">
              Deleting Receipts...
            </h3>
            
            <div className="space-y-3">
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div 
                  className="bg-red-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(progress.completed / progress.total) * 100}%` }}
                />
              </div>
              
              <p className="text-gray-400 text-sm">
                {progress.completed} of {progress.total} receipts deleted
              </p>
              
              {progress.current && (
                <p className="text-gray-300 text-sm">
                  Currently deleting: {progress.current}
                </p>
              )}
              
              {progress.errors.length > 0 && (
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3 text-left">
                  <p className="text-red-400 font-medium text-sm mb-2">Errors:</p>
                  <ul className="text-red-300 text-xs space-y-1">
                    {progress.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 border border-gray-700 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-600/20 rounded-lg flex items-center justify-center">
              <Trash2 className="w-5 h-5 text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Delete Receipts</h2>
              <p className="text-gray-400 text-sm">
                {selectedReceipts.length} receipt{selectedReceipts.length !== 1 ? 's' : ''} selected
              </p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          {!showConfirmation ? (
            <div className="space-y-6">
              {/* Warning */}
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-yellow-200 font-medium">Warning</p>
                    <p className="text-yellow-300 text-sm mt-1">
                      This action cannot be undone. The selected receipts and their associated data will be permanently deleted.
                    </p>
                  </div>
                </div>
              </div>

              {/* Summary */}
              <div className="bg-gray-800/50 rounded-lg p-4">
                <h3 className="text-white font-medium mb-3">Deletion Summary</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Total Receipts:</span>
                    <span className="text-white ml-2">{selectedReceipts.length}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Total Value:</span>
                    <span className="text-white ml-2">{formatCurrency(totalAmount)}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">With Sheet Rows:</span>
                    <span className="text-white ml-2">{receiptsWithSheetRows.length}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Without Sheet Rows:</span>
                    <span className="text-white ml-2">{receiptsWithoutSheetRows.length}</span>
                  </div>
                </div>
              </div>

              {/* Google Sheets Option */}
              {receiptsWithSheetRows.length > 0 && (
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <input
                      type="checkbox"
                      id="deleteSheetRows"
                      checked={deleteGoogleSheetRows}
                      onChange={(e) => setDeleteGoogleSheetRows(e.target.checked)}
                      className="w-4 h-4 text-pink-600 bg-gray-700 border-gray-600 rounded focus:ring-pink-500 focus:ring-2 mt-1"
                    />
                    <div className="flex-1">
                      <label htmlFor="deleteSheetRows" className="text-blue-200 font-medium cursor-pointer">
                        Also delete Google Sheets rows
                      </label>
                      <p className="text-blue-300 text-sm mt-1">
                        {receiptsWithSheetRows.length} receipt{receiptsWithSheetRows.length !== 1 ? 's have' : ' has'} associated Google Sheets row{receiptsWithSheetRows.length !== 1 ? 's' : ''}. 
                        Checking this option will also remove the corresponding row{receiptsWithSheetRows.length !== 1 ? 's' : ''} from your Google Sheets.
                      </p>
                      {!deleteGoogleSheetRows && (
                        <p className="text-yellow-300 text-xs mt-2">
                          ⚠️ If unchecked, the Google Sheets rows will remain but will reference deleted receipts.
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {/* Receipt List */}
              <div>
                <h3 className="text-white font-medium mb-3">Receipts to Delete</h3>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {receipts.map((receipt) => (
                    <div key={receipt.id} className="bg-gray-800/50 rounded-lg p-3">
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-white font-medium truncate">
                            {receipt.vendor || 'Unknown Vendor'}
                          </p>
                          <p className="text-gray-400 text-sm truncate">
                            {receipt.original_file_name}
                          </p>
                        </div>
                        <div className="flex items-center gap-3 ml-4">
                          <span className="text-white font-medium">
                            {formatCurrency(receipt.total_amount)}
                          </span>
                          {receipt.google_sheet_row_number && (
                            <Badge className="bg-green-500/20 text-green-400 text-xs">
                              Row {receipt.google_sheet_row_number}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center space-y-6">
              <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto">
                <AlertTriangle className="w-8 h-8 text-red-400" />
              </div>
              
              <div>
                <h3 className="text-xl font-bold text-white mb-2">
                  Are you absolutely sure?
                </h3>
                <p className="text-gray-400">
                  This will permanently delete {selectedReceipts.length} receipt{selectedReceipts.length !== 1 ? 's' : ''} 
                  {deleteGoogleSheetRows && receiptsWithSheetRows.length > 0 && 
                    ` and ${receiptsWithSheetRows.length} Google Sheets row${receiptsWithSheetRows.length !== 1 ? 's' : ''}`
                  }.
                </p>
                <p className="text-red-400 font-medium mt-2">
                  This action cannot be undone.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white rounded-lg transition-colors"
          >
            Cancel
          </button>
          
          {!showConfirmation ? (
            <button
              onClick={handleDelete}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Delete {selectedReceipts.length} Receipt{selectedReceipts.length !== 1 ? 's' : ''}
            </button>
          ) : (
            <button
              onClick={handleDelete}
              disabled={isDeleting}
              className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-600/50 text-white rounded-lg transition-colors flex items-center gap-2"
            >
              {isDeleting ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
              Yes, Delete Permanently
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
