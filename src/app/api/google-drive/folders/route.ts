import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { listGoogleDriveFolders } from '@/lib/google-drive/client';

export async function GET(request: NextRequest) {
  console.log('📁 Google Drive folders API called');

  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('❌ Authentication failed:', authError?.message);
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log('✅ User authenticated:', user.id);

    // Check if user has connected Google Drive
    const { data: userData } = await supabase
      .from('users')
      .select('google_drive_connected')
      .eq('id', user.id)
      .single();

    console.log('🔗 User Google Drive status:', userData);

    if (!userData?.google_drive_connected) {
      console.log('❌ Google Drive not connected');
      return NextResponse.json(
        { success: false, error: 'Google Drive not connected' },
        { status: 400 }
      );
    }

    console.log('🚀 Listing folders from Google Drive...');
    // List folders from Google Drive
    const folders = await listGoogleDriveFolders(user.id);
    console.log('📂 Found folders:', folders.length);

    return NextResponse.json({
      success: true,
      data: { folders }
    });
  } catch (error) {
    console.error('💥 Error listing Google Drive folders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to list Google Drive folders' },
      { status: 500 }
    );
  }
}
