// Paystack API client for Reco Engine
// Handles payment initialization, verification, and customer management

interface PaystackConfig {
  secretKey: string;
  publicKey: string;
  baseUrl: string;
}

interface PaystackCustomer {
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  metadata?: Record<string, any>;
}

interface PaystackTransaction {
  email: string;
  amount: number; // Amount in kobo (smallest currency unit)
  currency: string;
  reference: string;
  callback_url?: string;
  metadata?: Record<string, any>;
  channels?: string[];
}

interface PaystackResponse<T = any> {
  status: boolean;
  message: string;
  data?: T;
}

interface PaystackTransactionData {
  id: number;
  domain: string;
  status: string;
  reference: string;
  amount: number;
  message?: string;
  gateway_response: string;
  paid_at?: string;
  created_at: string;
  channel: string;
  currency: string;
  ip_address?: string;
  metadata: any;
  fees: number;
  customer: {
    id: number;
    first_name?: string;
    last_name?: string;
    email: string;
    customer_code: string;
    phone?: string;
    metadata?: any;
  };
  authorization?: {
    authorization_code: string;
    bin: string;
    last4: string;
    exp_month: string;
    exp_year: string;
    channel: string;
    card_type: string;
    bank: string;
    country_code: string;
    brand: string;
    reusable: boolean;
    signature: string;
    account_name?: string;
  };
}

class PaystackClient {
  private config: PaystackConfig;

  constructor() {
    this.config = {
      secretKey: process.env.PAYSTACK_SECRET_KEY!,
      publicKey: process.env.NEXT_PUBLIC_PAYSTACK_PUBLIC_KEY!,
      baseUrl: 'https://api.paystack.co'
    };

    if (!this.config.secretKey) {
      throw new Error('PAYSTACK_SECRET_KEY environment variable is required');
    }
  }

  private async makeRequest<T = any>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<PaystackResponse<T>> {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${this.config.secretKey}`,
      'Content-Type': 'application/json',
    };

    const options: RequestInit = {
      method,
      headers,
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(`Paystack API error: ${result.message || 'Unknown error'}`);
      }

      return result;
    } catch (error) {
      console.error('Paystack API request failed:', error);
      throw error;
    }
  }

  // Initialize a transaction
  async initializeTransaction(transaction: PaystackTransaction): Promise<{
    authorization_url: string;
    access_code: string;
    reference: string;
  }> {
    const response = await this.makeRequest<{
      authorization_url: string;
      access_code: string;
      reference: string;
    }>('/transaction/initialize', 'POST', transaction);

    if (!response.status || !response.data) {
      throw new Error(`Failed to initialize transaction: ${response.message}`);
    }

    return response.data;
  }

  // Verify a transaction
  async verifyTransaction(reference: string): Promise<PaystackTransactionData> {
    const response = await this.makeRequest<PaystackTransactionData>(
      `/transaction/verify/${reference}`
    );

    if (!response.status || !response.data) {
      throw new Error(`Failed to verify transaction: ${response.message}`);
    }

    return response.data;
  }

  // Create a customer
  async createCustomer(customer: PaystackCustomer): Promise<{
    email: string;
    integration: number;
    domain: string;
    customer_code: string;
    id: number;
    identified: boolean;
    identifications?: any;
    createdAt: string;
    updatedAt: string;
  }> {
    const response = await this.makeRequest('/customer', 'POST', customer);

    if (!response.status || !response.data) {
      throw new Error(`Failed to create customer: ${response.message}`);
    }

    return response.data;
  }

  // Get customer by email or customer code
  async getCustomer(emailOrCode: string): Promise<any> {
    const response = await this.makeRequest(`/customer/${emailOrCode}`);

    if (!response.status || !response.data) {
      throw new Error(`Failed to get customer: ${response.message}`);
    }

    return response.data;
  }

  // List transactions for a customer
  async getCustomerTransactions(customerId: number): Promise<PaystackTransactionData[]> {
    const response = await this.makeRequest<PaystackTransactionData[]>(
      `/transaction?customer=${customerId}`
    );

    if (!response.status || !response.data) {
      throw new Error(`Failed to get customer transactions: ${response.message}`);
    }

    return response.data;
  }

  // Generate a unique transaction reference
  generateReference(prefix: string = 'reco'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${prefix}_${timestamp}_${random}`;
  }

  // Convert amount to kobo (Paystack uses kobo for NGN)
  toKobo(amount: number): number {
    return Math.round(amount * 100);
  }

  // Convert amount from kobo to naira
  fromKobo(amount: number): number {
    return amount / 100;
  }

  // Verify webhook signature
  verifyWebhookSignature(payload: string, signature: string): boolean {
    const crypto = require('crypto');
    const hash = crypto
      .createHmac('sha512', this.config.secretKey)
      .update(payload, 'utf8')
      .digest('hex');
    
    return hash === signature;
  }
}

// Export singleton instance
export const paystackClient = new PaystackClient();

// Export types for use in other files
export type {
  PaystackCustomer,
  PaystackTransaction,
  PaystackTransactionData,
  PaystackResponse
};
