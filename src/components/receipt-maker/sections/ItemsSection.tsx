'use client';

import { Plus, Trash2 } from 'lucide-react';
import { ReceiptItem, Tax, Discount, Totals, CURRENCIES } from '../types';

interface ItemsSectionProps {
  items: ReceiptItem[];
  tax: Tax;
  discount: Discount;
  totals: Totals;
  currency: string;
  updateItems: (items: ReceiptItem[]) => void;
  updateTax: (tax: Tax) => void;
  updateDiscount: (discount: Discount) => void;
}

export default function ItemsSection({
  items,
  tax,
  discount,
  totals,
  currency,
  updateItems,
  updateTax,
  updateDiscount
}: ItemsSectionProps) {
  
  const addItem = () => {
    const newItem: ReceiptItem = {
      description: '',
      quantity: 1,
      rate: 0,
      amount: 0
    };
    updateItems([...items, newItem]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      updateItems(items.filter((_, i) => i !== index));
    }
  };

  const updateItem = (index: number, field: keyof ReceiptItem, value: string | number) => {
    const updatedItems = items.map((item, i) => {
      if (i === index) {
        const updatedItem = { ...item, [field]: value };
        // Calculate amount when quantity or rate changes
        if (field === 'quantity' || field === 'rate') {
          updatedItem.amount = updatedItem.quantity * updatedItem.rate;
        }
        return updatedItem;
      }
      return item;
    });
    updateItems(updatedItems);
  };

  const formatCurrency = (amount: number) => {
    const currencyInfo = CURRENCIES.find(c => c.code === currency) || CURRENCIES[0];
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-700 pb-4">
        <h2 className="text-xl font-semibold text-white mb-2">Items</h2>
        <p className="text-gray-400 text-sm">Add items to your receipt</p>
      </div>

      {/* Items Table */}
      <div className="overflow-x-auto">
        <table className="w-full min-w-[600px]">
          <thead>
            <tr className="border-b border-gray-700">
              <th className="text-left text-sm font-medium text-gray-300 pb-3">Description</th>
              <th className="text-center text-sm font-medium text-gray-300 pb-3 w-20">Qty</th>
              <th className="text-right text-sm font-medium text-gray-300 pb-3 w-24">Rate</th>
              <th className="text-right text-sm font-medium text-gray-300 pb-3 w-24">Amount</th>
              <th className="w-10 pb-3"></th>
            </tr>
          </thead>
          <tbody className="space-y-2">
            {items.map((item, index) => (
              <tr key={index} className="border-b border-gray-800">
                <td className="py-3">
                  <input
                    type="text"
                    value={item.description}
                    onChange={(e) => updateItem(index, 'description', e.target.value)}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                    placeholder="Item description"
                  />
                </td>
                <td className="py-3 px-2">
                  <input
                    type="number"
                    min="1"
                    value={item.quantity}
                    onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-center focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  />
                </td>
                <td className="py-3 px-2">
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.rate}
                    onChange={(e) => updateItem(index, 'rate', parseFloat(e.target.value) || 0)}
                    className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-right focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  />
                </td>
                <td className="py-3 px-2 text-right text-white font-medium">
                  {formatCurrency(item.amount)}
                </td>
                <td className="py-3 px-2">
                  {items.length > 1 && (
                    <button
                      onClick={() => removeItem(index)}
                      className="text-red-400 hover:text-red-300 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add Item Button */}
      <button
        onClick={addItem}
        className="flex items-center gap-2 text-pink-400 hover:text-pink-300 transition-colors"
      >
        <Plus className="w-4 h-4" />
        Add Item
      </button>

      {/* Tax and Discount */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Tax Rate (%)
          </label>
          <input
            type="number"
            min="0"
            max="100"
            step="0.01"
            value={tax.rate}
            onChange={(e) => updateTax({ ...tax, rate: parseFloat(e.target.value) || 0 })}
            className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            placeholder="0.00"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Discount
          </label>
          <div className="flex gap-2">
            <select
              value={discount.type}
              onChange={(e) => updateDiscount({ ...discount, type: e.target.value as 'amount' | 'percentage' })}
              className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            >
              <option value="amount">Amount</option>
              <option value="percentage">Percentage</option>
            </select>
            <input
              type="number"
              min="0"
              step="0.01"
              value={discount.amount}
              onChange={(e) => updateDiscount({ ...discount, amount: parseFloat(e.target.value) || 0 })}
              className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              placeholder={discount.type === 'percentage' ? '0.00' : '0.00'}
            />
          </div>
          <p className="text-xs text-gray-400 mt-1">
            {discount.type === 'percentage' ? 'Enter percentage (e.g., 10 for 10%)' : `Enter amount in ${currency}`}
          </p>
        </div>
      </div>

      {/* Totals */}
      <div className="bg-gray-700/50 rounded-lg p-4 space-y-2">
        <div className="flex justify-between text-gray-300">
          <span>Subtotal:</span>
          <span>{formatCurrency(totals.subtotal)}</span>
        </div>
        {tax.rate > 0 && (
          <div className="flex justify-between text-gray-300">
            <span>Tax ({tax.rate}%):</span>
            <span>{formatCurrency(tax.amount)}</span>
          </div>
        )}
        {discount.amount > 0 && (
          <div className="flex justify-between text-gray-300">
            <span>Discount {discount.type === 'percentage' ? `(${discount.amount}%)` : ''}:</span>
            <span>-{formatCurrency(
              discount.type === 'percentage'
                ? (totals.subtotal * discount.amount) / 100
                : discount.amount
            )}</span>
          </div>
        )}
        <div className="border-t border-gray-600 pt-2">
          <div className="flex justify-between text-white font-bold text-lg">
            <span>Total:</span>
            <span>{formatCurrency(totals.total)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
