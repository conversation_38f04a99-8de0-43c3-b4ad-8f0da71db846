'use client'

import { useState } from 'react'
import { Setting<PERSON>, Loader2 } from 'lucide-react'
import GoogleSheetPicker from '@/components/google-picker/GoogleSheetPicker'

interface SheetPickerButtonProps {
  accessToken: string | null
  onSheetSelected?: () => void
}

export default function SheetPickerButton({ accessToken, onSheetSelected }: SheetPickerButtonProps) {
  const [showPicker, setShowPicker] = useState(false)
  const [loading, setLoading] = useState(false)

  const handleSheetSelected = async (sheetId: string, sheetName: string, sheetUrl: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/google-sheets/select-existing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sheetId,
          sheetName,
          sheetUrl,
          year: new Date().getFullYear()
        })
      })

      const result = await response.json()
      
      if (result.success) {
        setShowPicker(false)
        onSheetSelected?.()
        // Refresh the page to show the updated sheet
        window.location.reload()
      } else {
        console.error('Failed to select sheet:', result.error)
        alert('Failed to select sheet. Please try again.')
      }
    } catch (error) {
      console.error('Error selecting sheet:', error)
      alert('Failed to select sheet. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setShowPicker(false)
  }

  if (!accessToken) {
    return null
  }

  return (
    <>
      <button
        onClick={() => setShowPicker(true)}
        disabled={loading}
        className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors disabled:opacity-50"
      >
        {loading ? (
          <Loader2 className="w-4 h-4 animate-spin" />
        ) : (
          <Settings className="w-4 h-4" />
        )}
        Use Existing Sheet
      </button>

      <GoogleSheetPicker
        isOpen={showPicker}
        accessToken={accessToken}
        onSheetSelected={handleSheetSelected}
        onCancel={handleCancel}
      />
    </>
  )
}
