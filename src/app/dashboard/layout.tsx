import Sidebar from '@/components/layout/Sidebar';
import SmartQueueProcessor from '@/components/queue/SmartQueueProcessor';
import FallbackProcessor from '@/components/queue/FallbackProcessor';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen bg-gray-900 text-white">
      <Sidebar />
      <main className="flex-1 px-2 py-3 sm:px-4 sm:py-4 lg:px-6 lg:py-6 md:ml-0 ml-0">
        {/* Add top padding on mobile to account for the mobile menu button */}
        <div className="md:pt-0 pt-12">
          {children}
        </div>
      </main>
      {/* Smart queue processor with webhook-triggered intensive polling */}
      <SmartQueueProcessor enabled={true} />
      <FallbackProcessor
        enabled={false}
        cleanupIntervalMs={1800000}  // 30 minutes
        processingIntervalMs={900000} // 15 minutes
      />
    </div>
  );
} 