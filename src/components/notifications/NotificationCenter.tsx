'use client'

import { useState } from 'react'
import { Bell, CheckCircle, XCircle, AlertCircle, X, Wifi, WifiOff } from 'lucide-react'
import { useNotifications, NotificationMessage } from '@/hooks/useNotifications'

interface NotificationCenterProps {
  className?: string
}

export default function NotificationCenter({ className = '' }: NotificationCenterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const {
    notifications,
    isConnected,
    lastUpdate,
    error,
    clearNotifications,
    removeNotification,
    reconnect
  } = useNotifications({
    enableSSE: true, // Enable SSE for notifications only
    enablePolling: false // Keep polling disabled
  })

  const unreadCount = notifications.length

  const getNotificationIcon = (notification: NotificationMessage) => {
    if ('event_type' in notification) {
      switch (notification.event_type) {
        case 'job.completed':
        case 'receipt.processed':
          return <CheckCircle className="w-5 h-5 text-green-500" />
        case 'job.failed':
        case 'receipt.failed':
          return <XCircle className="w-5 h-5 text-red-500" />
        case 'job.status_changed':
          return <AlertCircle className="w-5 h-5 text-blue-500" />
        default:
          return <Bell className="w-5 h-5 text-gray-500" />
      }
    }
    return <Bell className="w-5 h-5 text-gray-500" />
  }

  const getNotificationMessage = (notification: NotificationMessage) => {
    if ('event_type' in notification) {
      switch (notification.event_type) {
        case 'job.completed':
          return `Receipt processing completed successfully`
        case 'job.failed':
          return `Receipt processing failed: ${notification.error_message || 'Unknown error'}`
        case 'job.status_changed':
          return `Receipt status changed from ${notification.old_status} to ${notification.new_status}`
        case 'receipt.processed':
          return `Receipt "${notification.receipt_id}" has been processed`
        case 'receipt.failed':
          return `Receipt "${notification.receipt_id}" processing failed`
        case 'receipt.uploaded':
          return `Receipt "${notification.receipt_id}" has been uploaded`
        default:
          return 'New notification'
      }
    }
    return 'New notification'
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    
    if (diff < 60000) return 'Just now'
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`
    return date.toLocaleDateString()
  }

  return (
    <div className={`relative ${className}`}>
      {/* Notification Bell */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-300 hover:text-white hover:bg-gray-800/60 rounded-lg transition-all duration-200 group"
        aria-label="Notifications"
      >
        <Bell className="w-6 h-6 group-hover:scale-110 transition-transform duration-200" />
        
        {/* Unread Badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-accent-pink text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium shadow-lg animate-pulse">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
        
        {/* Connection Status */}
        <div className="absolute -bottom-1 -right-1 bg-gray-900 rounded-full p-0.5">
          {isConnected ? (
            <Wifi className="w-3 h-3 text-green-400" />
          ) : (
            <WifiOff className="w-3 h-3 text-red-400" />
          )}
        </div>
      </button>

      {/* Notification Panel */}
      {isOpen && (
        <div className="fixed top-16 right-4 w-80 bg-gray-800 border border-gray-700 rounded-lg shadow-xl z-[60] max-w-[calc(100vw-2rem)]">
          {/* Header */}
          <div className="flex items-center justify-between p-3 border-b border-gray-700">
            <h3 className="text-base font-semibold text-white">Notifications</h3>
            <div className="flex items-center gap-2">
              {/* Connection Status */}
              <div className="flex items-center gap-1 text-xs">
                {isConnected ? (
                  <>
                    <Wifi className="w-3 h-3 text-green-500" />
                    <span className="text-green-500">Live</span>
                  </>
                ) : (
                  <>
                    <WifiOff className="w-3 h-3 text-red-500" />
                    <span className="text-red-500">Offline</span>
                  </>
                )}
              </div>
              
              {/* Clear All */}
              {notifications.length > 0 && (
                <button
                  onClick={clearNotifications}
                  className="text-xs text-gray-400 hover:text-white"
                >
                  Clear All
                </button>
              )}
            </div>
          </div>

          {/* Error State */}
          {error && (
            <div className="p-4 bg-red-900/20 border-b border-gray-700">
              <div className="flex items-center justify-between">
                <span className="text-red-400 text-sm">{error}</span>
                <button
                  onClick={reconnect}
                  className="text-xs text-red-400 hover:text-red-300 underline"
                >
                  Retry
                </button>
              </div>
            </div>
          )}

          {/* Notifications List */}
          <div className="max-h-80 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-6 text-center text-gray-400">
                <Bell className="w-6 h-6 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No notifications yet</p>
                <p className="text-xs mt-1">
                  You'll see updates about your receipts here
                </p>
              </div>
            ) : (
              notifications.map((notification, index) => (
                <div
                  key={index}
                  className="p-3 border-b border-gray-700 hover:bg-gray-700/50 transition-colors"
                >
                  <div className="flex items-start gap-2">
                    {getNotificationIcon(notification)}

                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-white leading-tight">
                        {getNotificationMessage(notification)}
                      </p>

                      {/* Additional Info */}
                      {'confidence_score' in notification && notification.confidence_score && (
                        <p className="text-xs text-gray-400 mt-1">
                          Confidence: {Math.round(notification.confidence_score * 100)}%
                        </p>
                      )}

                      <p className="text-xs text-gray-500 mt-1">
                        {formatTime(notification.updated_at || notification.timestamp || new Date().toISOString())}
                      </p>
                    </div>

                    <button
                      onClick={() => removeNotification(index)}
                      className="text-gray-400 hover:text-white flex-shrink-0"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {lastUpdate && (
            <div className="p-2 text-xs text-gray-500 text-center border-t border-gray-700">
              Last update: {formatTime(lastUpdate.toISOString())}
            </div>
          )}
        </div>
      )}

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}
