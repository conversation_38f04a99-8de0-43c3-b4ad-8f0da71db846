import { redis } from './redis';

// Circuit breaker for Redis operations
let redisFailureCount = 0;
let lastFailureTime = 0;
const FAILURE_THRESHOLD = 3;
const RECOVERY_TIME = 30000; // 30 seconds

function isRedisHealthy(): boolean {
  if (redisFailureCount < FAILURE_THRESHOLD) return true;
  if (Date.now() - lastFailureTime > RECOVERY_TIME) {
    redisFailureCount = 0;
    return true;
  }
  return false;
}

function recordRedisFailure() {
  redisFailureCount++;
  lastFailureTime = Date.now();
}

// Queue configurations
export const QUEUES = {
  HIGH_PRIORITY: 'queue:receipts:high',
  NORMAL_PRIORITY: 'queue:receipts:normal',
  LOW_PRIORITY: 'queue:receipts:low',
  PROCESSING: 'queue:receipts:processing',
  FAILED: 'queue:receipts:failed'
};

export interface QueueJob {
  id: string;
  receiptId: string;
  userId: string;
  priority: 'high' | 'normal' | 'low';
  attempts: number;
  maxAttempts: number;
  createdAt: string;
  processingStartedAt?: string;
  data: {
    imageUrl: string;
    fileName: string;
    fileSize: number;
    mimeType?: string;
  };
}

function getQueueKey(priority: string): string {
  switch (priority) {
    case 'high':
      return QUEUES.HIGH_PRIORITY;
    case 'low':
      return QUEUES.LOW_PRIORITY;
    default:
      return QUEUES.NORMAL_PRIORITY;
  }
}

// Add job to queue with optimized Redis operations
export async function addJobToQueue(job: QueueJob): Promise<void> {
  const queueKey = getQueueKey(job.priority);
  const jobData = JSON.stringify(job);

  console.log('Adding job to queue:', { queueKey, jobId: job.id });

  // Batch Redis operations for better performance
  const score = job.priority === 'high' ? Date.now() + 1000000 : Date.now();

  // Simplified Redis operations - reduce overhead
  await (redis as any).zadd(queueKey, { score, member: jobData });

  // Minimal job tracking for performance
  await (redis as any).hset(`job:${job.id}`, {
    status: 'queued',
    queuedAt: new Date().toISOString(),
    receiptId: job.receiptId,
    userId: job.userId
  });

  // Set TTL for job tracking (2 hours for faster cleanup)
  await (redis as any).expire(`job:${job.id}`, 7200);

  console.log('Job added to queue successfully');
}

// Get job from processing queue (for resuming failed jobs)
export async function getJobFromProcessingQueue(): Promise<QueueJob | null> {
  if (!isRedisHealthy()) {
    console.log('Redis circuit breaker is open, skipping processing queue check');
    return null;
  }

  try {
    const jobs = await Promise.race([
      (redis as any).zrange(QUEUES.PROCESSING, 0, 0, { withScores: true }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('Redis timeout')), 3000)) // Reduced timeout
    ]);
    console.log(`Processing queue returned:`, jobs);
    
    if (jobs.length >= 2) {
      // Redis with withScores returns flat array: [member, score]
      const jobJson = jobs[0] as string;
      const score = jobs[1] as number;
      
      console.log('Extracted job JSON from processing queue:', jobJson);
      
      // If it's already an object, it means the mock Redis isn't storing it as a string
      let jobData: QueueJob;
      if (typeof jobJson === 'object') {
        console.log('Job is already an object (mock Redis issue), using directly');
        jobData = jobJson as QueueJob;
      } else {
        // Parse the job data
        try {
          jobData = JSON.parse(jobJson);
        } catch (parseError) {
          console.error('Failed to parse job JSON from processing queue:', parseError);
          // Remove invalid job from queue
          await (redis as any).zrem(QUEUES.PROCESSING, jobJson);
          return null;
        }
      }
      
      // Check if this job is already being processed
      const jobStatus = await (redis as any).hget(`job:${jobData.id}`, 'status');
      if (jobStatus === 'processing') {
        console.log(`Job ${jobData.id} is already being processed, skipping`);
        return null;
      }

      console.log(`Job ${jobData.id} found in processing queue`);
      return jobData;
    }
  } catch (error) {
    console.error(`Error getting job from processing queue:`, error);
    recordRedisFailure();
  }

  console.log('No jobs found in processing queue');
  return null;
}

// Get next job from queue
export async function getNextJob(): Promise<QueueJob | null> {
  // Check if Redis is healthy before attempting operations
  if (!isRedisHealthy()) {
    console.log('Redis circuit breaker is open, skipping queue operations');
    return null;
  }

  // Check high priority first
  for (const queueKey of [QUEUES.HIGH_PRIORITY, QUEUES.NORMAL_PRIORITY, QUEUES.LOW_PRIORITY]) {
    try {
      const jobs = await Promise.race([
        (redis as any).zrange(queueKey, 0, 0, { withScores: true }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Redis timeout')), 3000)) // Reduced timeout
      ]);
      console.log(`Queue ${queueKey} returned:`, jobs);
      
      if (jobs.length >= 2) {
        // Redis with withScores returns flat array: [member, score]
        const jobJson = jobs[0] as string;
        const score = jobs[1] as number;
        
        console.log('Extracted job JSON:', jobJson);
        console.log('Type of jobJson:', typeof jobJson);
        console.log('Is it a string?', typeof jobJson === 'string');
        console.log('Extracted score:', score);
        
        // If it's already an object, it means the mock Redis isn't storing it as a string
        let jobData: QueueJob;
        if (typeof jobJson === 'object') {
          console.log('Job is already an object (mock Redis issue), using directly');
          jobData = jobJson as QueueJob;
        } else {
          // Parse the job data
          try {
            jobData = JSON.parse(jobJson);
          } catch (parseError) {
            console.error('Failed to parse job JSON:', parseError);
            // Remove invalid job from queue
            await (redis as any).zrem(queueKey, jobJson);
            continue;
          }
        }
        
        // Simplified job processing - remove from queue and mark as processing
        const jobJsonString = typeof jobJson === 'string' ? jobJson : JSON.stringify(jobData);

        // Remove from queue and update status in parallel for better performance
        await Promise.all([
          (redis as any).zrem(queueKey, jobJsonString),
          (redis as any).hset(`job:${jobData.id}`, {
            status: 'processing',
            processingStartedAt: new Date().toISOString()
          })
        ]);

        console.log(`Job ${jobData.id} moved to processing queue`);
        return jobData;
      }
    } catch (error) {
      console.error(`Error getting job from queue ${queueKey}:`, error);
      recordRedisFailure();
      // If Redis is timing out, break the circuit breaker faster
      if (error instanceof Error && error.message.includes('timeout')) {
        console.log('Redis timeout detected, opening circuit breaker');
        redisFailureCount = FAILURE_THRESHOLD; // Force circuit breaker open
      }
      continue;
    }
  }
  
  console.log('No jobs found in any queue');
  return null;
}

// Complete job
export async function completeJob(jobId: string): Promise<void> {
  console.log(`Completing job ${jobId}`);

  // Simplified completion - just mark as completed and set TTL
  await Promise.all([
    (redis as any).hset(`job:${jobId}`, {
      status: 'completed',
      completedAt: new Date().toISOString()
    }),
    // Set shorter TTL for completed jobs (30 minutes)
    (redis as any).expire(`job:${jobId}`, 1800)
  ]);

  console.log(`Job ${jobId} completed successfully`);
}

// Fail a job
export async function failJob(jobId: string, error: string): Promise<void> {
  try {
    console.log(`Failing job ${jobId} with error: ${error}`);
    
    const jobKey = `job:${jobId}`;
    const job = await (redis as any).hgetall(jobKey);
    const attempts = parseInt(job.attempts || '0') + 1;
    const maxAttempts = 3;
    
    // Always mark job status as failed to prevent reprocessing
    await (redis as any).hset(jobKey, {
      status: 'failed',
      attempts: attempts.toString(),
      lastError: error,
      failedAt: new Date().toISOString()
    });

    // Set shorter TTL for failed jobs (2 hours)
    await (redis as any).expire(jobKey, 7200);
    
    // Get the job data to remove from queues
    const originalJobData = job.originalData;
    const processingQueueData = job.processingQueueData;

    if (originalJobData || processingQueueData) {
      // Remove from ALL queues to ensure it's never processed again
      const allQueues = [
        QUEUES.HIGH_PRIORITY,
        QUEUES.NORMAL_PRIORITY,
        QUEUES.LOW_PRIORITY
      ];

      // Remove from regular queues using original data
      if (originalJobData) {
        for (const queueKey of allQueues) {
          try {
            await (redis as any).zrem(queueKey, originalJobData);
          } catch (removeError) {
            console.error(`Failed to remove job ${jobId} from ${queueKey}:`, removeError);
          }
        }
      }

      // Remove from processing queue using processing data
      if (processingQueueData) {
        try {
          await (redis as any).zrem(QUEUES.PROCESSING, processingQueueData);
          console.log(`Job ${jobId} removed from processing queue`);
        } catch (removeError) {
          console.error(`Failed to remove job ${jobId} from processing queue:`, removeError);
        }
      }

      // Move to failed queue for tracking
      await (redis as any).zadd(QUEUES.FAILED, {
        score: Date.now(),
        member: originalJobData || processingQueueData
      });
      
      console.log(`Job ${jobId} moved to failed queue after ${attempts} attempts`);
    } else {
      console.error(`No original job data found for job ${jobId}, cannot remove from queues`);
    }
    
  } catch (error) {
    console.error(`Error failing job ${jobId}:`, error);
    throw error;
  }
} 