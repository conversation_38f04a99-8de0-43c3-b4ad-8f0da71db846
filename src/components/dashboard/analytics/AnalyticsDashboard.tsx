'use client';

import { DollarSign, Receipt, TrendingUp, VenetianMask, RefreshCw } from 'lucide-react';
import { CategoryBreakdownChart } from './CategoryBreakdownChart';
import { SpendingTrendChart } from './SpendingTrendChart';
import { StatsCard } from './StatsCard';
import { useState } from 'react';

interface AnalyticsDashboardProps {
  data?: {
    monthlySpending: number;
    receiptCount: number;
    averageReceiptValue: number;
    topVendor: { vendor: string; count: number };
    spendingTrend: { month: string; total: number }[];
    categoryBreakdown: { category: string; total: number }[];
    currency: string;
  };
}

export function AnalyticsDashboard({ data }: AnalyticsDashboardProps) {
  const [isGenerating, setIsGenerating] = useState(false);

  // Provide default values when data is not available
  const defaultData = {
    monthlySpending: 0,
    receiptCount: 0,
    averageReceiptValue: 0,
    topVendor: { vendor: 'No data', count: 0 },
    spendingTrend: [],
    categoryBreakdown: [],
    currency: 'KES'
  };

  const analyticsData = data || defaultData;

  const handleGenerateAnalytics = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/analytics/generate', {
        method: 'POST',
      });

      if (response.ok) {
        // Refresh the page to show updated analytics
        window.location.reload();
      } else {
        console.error('Failed to generate analytics');
      }
    } catch (error) {
      console.error('Error generating analytics:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // Format currency based on the currency type
  const formatCurrency = (amount: number) => {
    if (analyticsData.currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (analyticsData.currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `${analyticsData.currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  return (
    <div className="space-y-4">
      {/* Analytics Header with Generate Button */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Analytics Dashboard</h2>
        <button
          onClick={handleGenerateAnalytics}
          disabled={isGenerating}
          className="flex items-center gap-2 bg-accent-purple hover:bg-purple-700 disabled:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200"
        >
          <RefreshCw className={`h-4 w-4 ${isGenerating ? 'animate-spin' : ''}`} />
          {isGenerating ? 'Generating...' : 'Refresh Analytics'}
        </button>
      </div>

      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Total Revenue"
          value={formatCurrency(analyticsData.monthlySpending)}
          icon={DollarSign}
          description="Total revenue this month"
        />
        <StatsCard
          title="Receipts"
          value={`+${analyticsData.receiptCount}`}
          icon={Receipt}
          description="Total receipts processed this month"
        />
        <StatsCard
          title="Avg. Receipt Value"
          value={formatCurrency(analyticsData.averageReceiptValue)}
          icon={TrendingUp}
          description="Average value per receipt"
        />
        <StatsCard
          title="Top Vendor"
          value={analyticsData.topVendor.vendor}
          icon={VenetianMask}
          description={`${analyticsData.topVendor.count} receipts this month`}
        />
      </div>
      <div className="grid gap-3 md:grid-cols-2">
        <SpendingTrendChart data={analyticsData.spendingTrend} currency={analyticsData.currency} />
        <CategoryBreakdownChart data={analyticsData.categoryBreakdown} currency={analyticsData.currency} />
      </div>
    </div>
  );
}
