import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    const body = await request.json();
    const {
      default_currency,
      auto_process_receipts,
      confidence_threshold,
      notification_email,
      notification_processing,
      data_retention_months,
      export_format
    } = body;

    // Validate input
    if (confidence_threshold && (confidence_threshold < 0.5 || confidence_threshold > 0.95)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Confidence threshold must be between 0.5 and 0.95' 
      }, { status: 400 });
    }

    if (data_retention_months && data_retention_months !== -1 && data_retention_months < 1) {
      return NextResponse.json({ 
        success: false, 
        error: 'Data retention must be at least 1 month or -1 for forever' 
      }, { status: 400 });
    }

    // Update preferences
    const { error: updateError } = await supabase
      .from('user_preferences')
      .update({
        default_currency,
        auto_process_receipts,
        confidence_threshold,
        notification_email,
        notification_processing,
        data_retention_months,
        export_format,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error updating preferences:', updateError);
      return NextResponse.json({ 
        success: false, 
        error: updateError.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Preferences updated successfully'
    });

  } catch (error) {
    console.error('Preferences API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
