'use client';

import { useState, useEffect } from 'react';
import { Building2, TrendingUp, Calendar, BarChart3 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { StatsCard } from '@/components/dashboard/analytics/StatsCard';

interface CompanyData {
  vendor: string;
  totalSpent: number;
  receiptCount: number;
  averageAmount: number;
  monthlyTrend: { month: string; total: number }[];
  categoryBreakdown: { category: string; total: number }[];
  receipts: any[];
}

interface CompanyAnalysisSectionProps {
  onCompanySelect?: (vendor: string | null) => void;
}

export default function CompanyAnalysisSection({ onCompanySelect }: CompanyAnalysisSectionProps) {
  const [companies, setCompanies] = useState<string[]>([]);
  const [selectedCompany, setSelectedCompany] = useState<string>('');
  const [companyData, setCompanyData] = useState<CompanyData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch available companies on component mount
  useEffect(() => {
    fetchCompanies();
  }, []);

  const fetchCompanies = async () => {
    try {
      const response = await fetch('/api/analytics/companies');
      if (!response.ok) throw new Error('Failed to fetch companies');
      
      const data = await response.json();
      setCompanies(data.companies || []);
    } catch (err) {
      console.error('Error fetching companies:', err);
      setError('Failed to load companies');
    }
  };

  const fetchCompanyAnalytics = async (vendor: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/analytics/company', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ vendor })
      });
      
      if (!response.ok) throw new Error('Failed to fetch company analytics');
      
      const data = await response.json();
      setCompanyData(data);
    } catch (err) {
      console.error('Error fetching company analytics:', err);
      setError('Failed to load company analytics');
    } finally {
      setLoading(false);
    }
  };

  const handleCompanyChange = (vendor: string) => {
    setSelectedCompany(vendor);
    if (vendor && vendor !== 'all') {
      fetchCompanyAnalytics(vendor);
      onCompanySelect?.(vendor);
    } else {
      setCompanyData(null);
      onCompanySelect?.(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Company Selector */}
      <Card className="bg-gray-800/50 backdrop-blur-md border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center text-white">
            <Building2 className="w-5 h-5 mr-2 text-accent-purple" />
            Company Analysis
          </CardTitle>
          <CardDescription>
            Analyze spending patterns and trends for specific companies/vendors
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label className="text-sm font-medium text-gray-300 mb-2 block">
                Select Company/Vendor
              </label>
              <Select value={selectedCompany} onValueChange={handleCompanyChange}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Choose a company to analyze..." />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 border-gray-600">
                  <SelectItem value="all" className="text-white">All Companies</SelectItem>
                  {companies.map((company) => (
                    <SelectItem key={company} value={company} className="text-white">
                      {company}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {selectedCompany && selectedCompany !== 'all' && (
              <Button 
                onClick={() => fetchCompanyAnalytics(selectedCompany)}
                disabled={loading}
                className="bg-accent-purple hover:bg-accent-purple/80"
              >
                {loading ? 'Loading...' : 'Analyze'}
              </Button>
            )}
          </div>
          
          {error && (
            <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <p className="text-red-400 text-sm">{error}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Company Analytics Results */}
      {companyData && (
        <div className="space-y-6">
          {/* Company Stats */}
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            <StatsCard
              title="Total Spent"
              value={formatCurrency(companyData.totalSpent)}
              icon={TrendingUp}
              description={`Across ${companyData.receiptCount} receipts`}
            />
            <StatsCard
              title="Receipt Count"
              value={`${companyData.receiptCount}`}
              icon={BarChart3}
              description="Total transactions"
            />
            <StatsCard
              title="Average Amount"
              value={formatCurrency(companyData.averageAmount)}
              icon={Calendar}
              description="Per transaction"
            />
            <StatsCard
              title="Company"
              value={companyData.vendor}
              icon={Building2}
              description="Selected vendor"
            />
          </div>

          {/* Monthly Trend Chart Placeholder */}
          <Card className="bg-gray-800/50 backdrop-blur-md border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Monthly Spending Trend</CardTitle>
              <CardDescription>
                {companyData.vendor} spending over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-gray-400">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Monthly trend chart will be implemented</p>
                  <p className="text-sm">Showing {companyData.monthlyTrend.length} months of data</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Category Breakdown */}
          <Card className="bg-gray-800/50 backdrop-blur-md border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Category Breakdown</CardTitle>
              <CardDescription>
                Spending by category for {companyData.vendor}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {companyData.categoryBreakdown.slice(0, 6).map((category, index) => (
                  <div key={category.category} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: `hsl(${index * 60}, 70%, 60%)` }}
                      />
                      <span className="text-gray-300">{category.category}</span>
                    </div>
                    <span className="text-white font-medium">
                      {formatCurrency(category.total)}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Receipts */}
          <Card className="bg-gray-800/50 backdrop-blur-md border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Recent Receipts</CardTitle>
              <CardDescription>
                Latest transactions from {companyData.vendor}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {companyData.receipts.slice(0, 5).map((receipt) => (
                  <div key={receipt.id} className="flex items-center justify-between p-3 bg-gray-700/30 rounded-lg">
                    <div>
                      <p className="text-white font-medium">{receipt.receipt_date}</p>
                      <p className="text-gray-400 text-sm">{receipt.payment_method}</p>
                    </div>
                    <span className="text-white font-medium">
                      {formatCurrency(receipt.total_amount)}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
