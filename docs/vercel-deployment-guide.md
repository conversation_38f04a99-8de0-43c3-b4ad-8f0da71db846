# ReceiptLabs Vercel Deployment Guide

This guide explains how to deploy the updated ReceiptLabs application to Vercel with all the new changes including privacy policy, terms of service, and updated pricing.

## Prerequisites

Before deploying, ensure you have:
- [ ] Vercel account with appropriate permissions
- [ ] GitHub repository access
- [ ] Environment variables configured
- [ ] Supabase project set up
- [ ] Domain configuration (receiptlabs.com)

## Changes Summary

The following changes have been made and need to be deployed:

### 1. New Legal Pages
- **Privacy Policy**: `/privacy` - Comprehensive privacy policy for Google Console verification
- **Terms of Service**: `/terms` - Detailed terms of service with updated pricing
- **Homepage Footer**: Updated to link to new legal pages

### 2. Updated Pricing
- **Professional Plan**: Updated from KES 2,999 to KES 4,999/month
- **Business Plan**: Updated from KES 4,999 to KES 6,999/month
- **Files Updated**:
  - `src/lib/subscription/tiers.ts`
  - `src/app/page.tsx` (homepage pricing section)
  - `src/app/terms/page.tsx`

### 3. Documentation
- **Google OAuth Scopes Justification**: `docs/google-oauth-scopes-justification.md`
- **Video Script**: `docs/video-script-google-verification.md`

## Deployment Steps

### Step 1: Verify Local Changes

First, ensure all changes work locally:

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Test the application at http://localhost:3000
```

**Test Checklist:**
- [ ] Homepage loads with updated pricing
- [ ] Privacy policy page accessible at `/privacy`
- [ ] Terms of service page accessible at `/terms`
- [ ] Footer links work correctly
- [ ] Pricing displays correctly throughout the app
- [ ] No console errors or broken links

### Step 2: Build and Test Production Build

```bash
# Create production build
npm run build

# Test production build locally
npm start
```

Verify the production build works correctly before deploying.

### Step 3: Commit and Push Changes

```bash
# Add all changes
git add .

# Commit with descriptive message
git commit -m "feat: Add privacy policy, terms of service, and update pricing

- Add comprehensive privacy policy page for Google Console verification
- Add detailed terms of service page
- Update pricing: Professional KES 4,999, Business KES 6,999
- Update homepage footer with legal page links
- Add Google OAuth scopes justification documentation
- Add video script for app verification"

# Push to main branch
git push origin main
```

### Step 4: Deploy to Vercel

#### Option A: Automatic Deployment (Recommended)
If your Vercel project is connected to GitHub:

1. **Push triggers automatic deployment**
2. **Monitor deployment in Vercel dashboard**
3. **Verify deployment completes successfully**

#### Option B: Manual Deployment via Vercel CLI

```bash
# Install Vercel CLI if not already installed
npm i -g vercel

# Login to Vercel
vercel login

# Deploy to production
vercel --prod
```

### Step 5: Verify Deployment

After deployment, verify everything works on the live site:

#### Core Functionality Tests
- [ ] **Homepage**: https://receiptlabs.com
  - [ ] Updated pricing displays correctly
  - [ ] All sections load properly
  - [ ] Footer links to privacy and terms work

- [ ] **Privacy Policy**: https://receiptlabs.com/privacy
  - [ ] Page loads completely
  - [ ] All sections display correctly
  - [ ] Navigation works

- [ ] **Terms of Service**: https://receiptlabs.com/terms
  - [ ] Page loads completely
  - [ ] Updated pricing shown correctly
  - [ ] All legal sections present

#### Application Features
- [ ] **Authentication**: Google OAuth still works
- [ ] **Dashboard**: Loads correctly for authenticated users
- [ ] **Receipt Processing**: Upload and processing functionality
- [ ] **Google Integrations**: Sheets, Drive, Gmail features work
- [ ] **Billing**: Subscription flows with updated pricing

### Step 6: Environment Variables Check

Ensure all required environment variables are set in Vercel:

```bash
# Check Vercel environment variables
vercel env ls
```

**Required Variables:**
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `OPENAI_API_KEY`
- `UPSTASH_REDIS_REST_URL`
- `UPSTASH_REDIS_REST_TOKEN`
- `PAYSTACK_SECRET_KEY`
- `PAYSTACK_PUBLIC_KEY`
- `GOOGLE_CLIENT_ID`
- `GOOGLE_CLIENT_SECRET`

### Step 7: Domain and SSL Configuration

Verify domain configuration:

1. **Custom Domain**: Ensure `receiptlabs.com` points to Vercel
2. **SSL Certificate**: Verify HTTPS is working
3. **Redirects**: Check www redirects work properly

### Step 8: Performance and SEO Verification

After deployment, check:

#### Performance
- [ ] **Page Load Speed**: Use Google PageSpeed Insights
- [ ] **Core Web Vitals**: Verify good scores
- [ ] **Mobile Responsiveness**: Test on mobile devices

#### SEO
- [ ] **Meta Tags**: Privacy and Terms pages have proper meta tags
- [ ] **Sitemap**: Update if necessary
- [ ] **Robots.txt**: Ensure legal pages are indexable

## Post-Deployment Tasks

### 1. Update Google Console Application

With the new privacy policy and terms of service:

1. **Update App Information**:
   - Privacy Policy URL: `https://receiptlabs.com/privacy`
   - Terms of Service URL: `https://receiptlabs.com/terms`

2. **Submit for Verification**:
   - Use the Google OAuth scopes justification document
   - Reference the video script for creating verification video
   - Provide updated app information

### 2. Monitor Application

After deployment, monitor:

- **Error Logs**: Check Vercel function logs
- **User Feedback**: Monitor support channels
- **Performance Metrics**: Track page load times
- **Conversion Rates**: Monitor subscription sign-ups with new pricing

### 3. Update Documentation

- [ ] Update README.md if necessary
- [ ] Update API documentation
- [ ] Notify team of pricing changes
- [ ] Update marketing materials

## Rollback Plan

If issues arise after deployment:

### Quick Rollback
```bash
# Rollback to previous deployment in Vercel dashboard
# Or redeploy previous commit
git revert HEAD
git push origin main
```

### Partial Rollback
If only specific features need rollback:
- Revert specific files
- Deploy incremental fix
- Monitor for stability

## Troubleshooting

### Common Issues

#### Build Failures
- Check for TypeScript errors
- Verify all imports are correct
- Ensure environment variables are set

#### Runtime Errors
- Check Vercel function logs
- Verify Supabase connection
- Test API endpoints

#### Pricing Display Issues
- Clear browser cache
- Check if changes propagated to CDN
- Verify tiers.ts file is correctly updated

### Support Contacts

- **Technical Issues**: <EMAIL>
- **Deployment Issues**: Check Vercel documentation
- **Emergency**: Use Vercel support for critical issues

## Success Criteria

Deployment is successful when:

- [ ] All pages load without errors
- [ ] Updated pricing displays correctly
- [ ] Legal pages are accessible and complete
- [ ] Core application functionality works
- [ ] Google integrations remain functional
- [ ] Performance metrics are acceptable
- [ ] No critical errors in logs

---

**Deployment Checklist Summary:**
1. ✅ Test locally
2. ✅ Build production version
3. ✅ Commit and push changes
4. ✅ Deploy to Vercel
5. ✅ Verify deployment
6. ✅ Check environment variables
7. ✅ Verify domain/SSL
8. ✅ Test performance
9. ✅ Update Google Console
10. ✅ Monitor application

**Next Steps:**
- Submit Google Console verification with new documentation
- Monitor user feedback on pricing changes
- Track conversion metrics
- Plan future feature releases
