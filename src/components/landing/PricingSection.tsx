import Link from 'next/link'
import { CheckCircle } from 'lucide-react'
import { PricingPlan } from '@/lib/types/landing-page'

interface PricingSectionProps {
  pricing: PricingPlan[];
}

export default function PricingSection({ pricing }: PricingSectionProps) {
  const formatPrice = (price: number, currency: string) => {
    if (price === 0) return `${currency} 0`
    return `${currency} ${price.toLocaleString()}`
  }

  return (
    <section id="pricing" className="relative z-10 px-4 py-12 sm:py-20 bg-gray-900/50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6 px-2">
            Simple Pricing for Small Businesses
          </h2>
          <p className="text-base sm:text-xl text-gray-300 max-w-3xl mx-auto px-2">
            Start free and scale as your business grows. No hidden fees, no long-term contracts. 
            Choose the plan that fits your small business needs.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 sm:gap-8 max-w-6xl mx-auto">
          {pricing.map((plan) => (
            <div 
              key={plan.id}
              className={`${
                plan.isPopular 
                  ? 'bg-gradient-to-b from-pink-500/10 to-purple-600/10 backdrop-blur-md border-2 border-pink-500/50' 
                  : 'bg-gray-800/50 backdrop-blur-md border border-gray-700'
              } rounded-xl sm:rounded-2xl p-6 sm:p-8 text-center relative hover:border-pink-500/30 transition-colors duration-300`}
            >
              {plan.isPopular && (
                <div className="absolute -top-3 sm:-top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
                    Most Popular for Small Business
                  </div>
                </div>
              )}

              <h3 className="text-xl sm:text-2xl font-bold text-white mb-4 sm:mb-6 mt-2">
                {plan.name}
              </h3>
              
              <div className="mb-6 sm:mb-8">
                <div className="text-3xl sm:text-4xl font-bold text-white mb-2">
                  {formatPrice(plan.price, plan.currency)}
                </div>
                <div className="text-gray-400 text-sm sm:text-base">{plan.period}</div>
              </div>

              <ul className="text-left space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2 sm:gap-3">
                    <CheckCircle className="w-4 sm:w-5 h-4 sm:h-5 text-green-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-300 text-sm sm:text-base">{feature}</span>
                  </li>
                ))}
              </ul>

              <Link
                href={plan.ctaHref}
                className={`w-full ${
                  plan.isPopular
                    ? 'bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold'
                    : 'bg-gray-700 hover:bg-gray-600 text-white font-medium'
                } py-3 sm:py-4 px-4 sm:px-6 rounded-lg transition-all duration-200 block text-center text-sm sm:text-base`}
              >
                {plan.ctaText}
              </Link>

              {/* Small Business Recommendation */}
              {plan.id === 'free' && (
                <div className="mt-4 p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                  <p className="text-green-300 text-xs sm:text-sm font-medium">
                    Perfect for new small businesses
                  </p>
                </div>
              )}
              
              {plan.id === 'professional' && (
                <div className="mt-4 p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                  <p className="text-purple-300 text-xs sm:text-sm font-medium">
                    Ideal for growing small businesses
                  </p>
                </div>
              )}
              
              {plan.id === 'business' && (
                <div className="mt-4 p-3 bg-orange-500/10 border border-orange-500/20 rounded-lg">
                  <p className="text-orange-300 text-xs sm:text-sm font-medium">
                    Best for established small businesses
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Small Business Value Proposition */}
        <div className="mt-12 sm:mt-16 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 sm:p-8">
          <div className="text-center">
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-4">
              Why Small Businesses Choose ReceiptLabs
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mt-6 sm:mt-8">
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-green-400 mb-2">Free Start</div>
                <div className="text-gray-400 text-xs sm:text-sm">No upfront costs or commitments</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-blue-400 mb-2">Easy Setup</div>
                <div className="text-gray-400 text-xs sm:text-sm">Ready in under 5 minutes</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-purple-400 mb-2">Scale Up</div>
                <div className="text-gray-400 text-xs sm:text-sm">Upgrade as your business grows</div>
              </div>
              <div className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-pink-400 mb-2">Support</div>
                <div className="text-gray-400 text-xs sm:text-sm">Dedicated small business help</div>
              </div>
            </div>
          </div>
        </div>

        {/* Money Back Guarantee */}
        <div className="mt-8 sm:mt-12 text-center">
          <div className="inline-flex items-center gap-2 bg-green-500/10 border border-green-500/20 rounded-full px-4 sm:px-6 py-2 sm:py-3">
            <CheckCircle className="w-4 sm:w-5 h-4 sm:h-5 text-green-400" />
            <span className="text-green-300 text-sm sm:text-base font-medium">
              30-day money-back guarantee • Cancel anytime • No questions asked
            </span>
          </div>
        </div>
      </div>
    </section>
  )
}