'use client';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { ExternalLink, RefreshCw, AlertCircle, RotateCcw } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';

type Receipt = {
  id: string;
  vendor: string | null;
  receipt_date: string | null;
  total_amount: number | null;
  processing_status: string | null;
  google_sheet_row_number: number | null;
  error_message: string | null;
};

interface ReceiptsDataTableProps {
  receipts: Receipt[];
}

const statusColors: { [key: string]: string } = {
  pending: 'bg-yellow-500/20 text-yellow-400',
  queued: 'bg-orange-500/20 text-orange-400',
  processing: 'bg-blue-500/20 text-blue-400',
  completed: 'bg-green-500/20 text-green-400',
  failed: 'bg-red-500/20 text-red-400',
};

const formatCurrency = (amount: number | null): string => {
  if (amount === null || amount === undefined) return 'N/A';
  return new Intl.NumberFormat('en-KE', { 
    style: 'currency', 
    currency: 'KES' 
  }).format(amount);
};

const formatDate = (dateString: string | null): string => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return 'Invalid Date';
  }
};

const getStatusDisplayName = (status: string | null): string => {
  if (!status) return 'Unknown';
  
  const statusMap: { [key: string]: string } = {
    pending: 'Pending',
    queued: 'Queued',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed'
  };
  
  return statusMap[status] || status.charAt(0).toUpperCase() + status.slice(1);
};

export default function ReceiptsDataTable({ receipts }: ReceiptsDataTableProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [retryingReceipts, setRetryingReceipts] = useState<Set<string>>(new Set());

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Trigger a page refresh to get updated data
      window.location.reload();
    } finally {
      // This won't execute because of the reload, but good practice
      setIsRefreshing(false);
    }
  };

  const handleRetryGoogleSheetsExport = async (receiptId: string) => {
    setRetryingReceipts(prev => new Set(prev).add(receiptId));

    try {
      const response = await fetch('/api/retry-google-sheets-export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ receiptId }),
      });

      const result = await response.json();

      if (result.success) {
        // Refresh the page to show updated data
        window.location.reload();
      } else {
        alert(`Retry failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Retry error:', error);
      alert('Failed to retry Google Sheets export. Please try again.');
    } finally {
      setRetryingReceipts(prev => {
        const newSet = new Set(prev);
        newSet.delete(receiptId);
        return newSet;
      });
    }
  };

  const handleProcessPending = async () => {
    try {
      const response = await fetch('/api/pipeline', { 
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ maxJobs: 10 })
      });
      
      if (response.ok) {
        // Refresh the page after processing
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to trigger processing:', error);
    }
  };

  const pendingCount = receipts.filter(r => 
    r.processing_status === 'pending' || 
    r.processing_status === 'queued'
  ).length;

  if (receipts.length === 0) {
    return (
      <div className="rounded-lg border border-gray-800 bg-gray-900/50 p-8 text-center">
        <h3 className="text-lg font-semibold text-white mb-2">No Receipts Yet</h3>
        <p className="text-gray-400 mb-4">
          Upload your first receipt to get started with automatic data extraction.
        </p>
        <Link href="/dashboard/upload">
          <button className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200">
            Upload Receipt
          </button>
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-white">Receipts ({receipts.length})</h2>
          {pendingCount > 0 && (
            <Badge className="bg-yellow-500/20 text-yellow-400">
              {pendingCount} pending
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {pendingCount > 0 && (
            <button
              onClick={handleProcessPending}
              className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 text-sm"
            >
              Process Pending
            </button>
          )}
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center space-x-2 text-sm"
          >
            <RefreshCw className={cn("w-4 h-4", isRefreshing && "animate-spin")} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block rounded-lg border border-gray-800 bg-gray-900/50 overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="border-gray-800">
              <TableHead className="text-white">Vendor</TableHead>
              <TableHead className="text-white">Date</TableHead>
              <TableHead className="text-white text-right">Amount</TableHead>
              <TableHead className="text-white">Status</TableHead>
              <TableHead className="text-white">Google Sheet</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {receipts.map((receipt) => (
              <TableRow key={receipt.id} className="border-gray-800">
                <TableCell className="font-medium">
                  {receipt.vendor || (
                    <span className="text-gray-500 italic">
                      {receipt.processing_status === 'completed' ? 'Unknown Vendor' : 'Processing...'}
                    </span>
                  )}
                </TableCell>
                <TableCell>
                  {receipt.receipt_date ? formatDate(receipt.receipt_date) : (
                    <span className="text-gray-500 italic">
                      {receipt.processing_status === 'completed' ? 'No Date' : 'Processing...'}
                    </span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  {receipt.total_amount !== null ? formatCurrency(receipt.total_amount) : (
                    <span className="text-gray-500 italic">
                      {receipt.processing_status === 'completed' ? 'No Amount' : 'Processing...'}
                    </span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <Badge
                        className={cn(
                          'capitalize',
                          statusColors[receipt.processing_status || ''] || 'bg-gray-500/20 text-gray-400'
                        )}
                      >
                        {getStatusDisplayName(receipt.processing_status)}
                      </Badge>
                      {receipt.error_message && (
                        <AlertCircle
                          className="w-4 h-4 text-red-400 cursor-help"
                          title={receipt.error_message}
                        />
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  {receipt.google_sheet_row_number && receipt.processing_status === 'completed' ? (
                    <Link
                      href="/dashboard/sheets"
                      className="flex items-center text-accent-purple hover:text-accent-pink hover:underline transition-colors duration-200"
                    >
                      <span>Row {receipt.google_sheet_row_number}</span>
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </Link>
                  ) : receipt.processing_status === 'completed' ? (
                    <div className="flex flex-col space-y-1">
                      <span className="text-red-400 text-sm">Export Failed</span>
                      <button
                        onClick={() => handleRetryGoogleSheetsExport(receipt.id)}
                        disabled={retryingReceipts.has(receipt.id)}
                        className="flex items-center space-x-1 text-xs bg-pink-600 hover:bg-pink-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-2 py-1 rounded transition-colors duration-200 w-fit"
                        title="Retry Google Sheets Export"
                      >
                        <RotateCcw className={cn("w-3 h-3", retryingReceipts.has(receipt.id) && "animate-spin")} />
                        <span>{retryingReceipts.has(receipt.id) ? 'Retrying...' : 'Retry'}</span>
                      </button>
                    </div>
                  ) : receipt.processing_status === 'failed' ? (
                    <span className="text-gray-500">-</span>
                  ) : (
                    <span className="text-gray-500 italic">Pending</span>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Cards */}
      <div className="md:hidden space-y-3">
        {receipts.map((receipt) => (
          <div key={receipt.id} className="bg-gray-900/50 border border-gray-800 rounded-lg p-4">
            {/* Header with vendor and amount */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-white truncate">
                  {receipt.vendor || (
                    <span className="text-gray-500 italic">
                      {receipt.processing_status === 'completed' ? 'Unknown Vendor' : 'Processing...'}
                    </span>
                  )}
                </h3>
                <p className="text-sm text-gray-400 mt-1">
                  {receipt.receipt_date ? formatDate(receipt.receipt_date) : (
                    <span className="italic">
                      {receipt.processing_status === 'completed' ? 'No Date' : 'Processing...'}
                    </span>
                  )}
                </p>
              </div>
              <div className="text-right ml-3">
                <p className="font-medium text-white">
                  {receipt.total_amount !== null ? formatCurrency(receipt.total_amount) : (
                    <span className="text-gray-500 italic text-sm">
                      {receipt.processing_status === 'completed' ? 'No Amount' : 'Processing...'}
                    </span>
                  )}
                </p>
              </div>
            </div>

            {/* Status and actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Badge
                  className={cn(
                    'capitalize text-xs',
                    statusColors[receipt.processing_status || ''] || 'bg-gray-500/20 text-gray-400'
                  )}
                >
                  {getStatusDisplayName(receipt.processing_status)}
                </Badge>
                {receipt.error_message && (
                  <AlertCircle
                    className="w-4 h-4 text-red-400 cursor-help"
                    title={receipt.error_message}
                  />
                )}
              </div>

              {/* Google Sheets status */}
              <div className="text-right">
                {receipt.google_sheet_row_number && receipt.processing_status === 'completed' ? (
                  <Link
                    href="/dashboard/sheets"
                    className="flex items-center text-accent-purple hover:text-accent-pink hover:underline transition-colors duration-200 text-sm"
                  >
                    <span>Row {receipt.google_sheet_row_number}</span>
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </Link>
                ) : receipt.processing_status === 'completed' ? (
                  <div className="flex flex-col items-end space-y-1">
                    <span className="text-red-400 text-xs">Export Failed</span>
                    <button
                      onClick={() => handleRetryGoogleSheetsExport(receipt.id)}
                      disabled={retryingReceipts.has(receipt.id)}
                      className="flex items-center space-x-1 text-xs bg-pink-600 hover:bg-pink-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-2 py-1 rounded transition-colors duration-200"
                      title="Retry Google Sheets Export"
                    >
                      <RotateCcw className={cn("w-3 h-3", retryingReceipts.has(receipt.id) && "animate-spin")} />
                      <span>{retryingReceipts.has(receipt.id) ? 'Retrying...' : 'Retry'}</span>
                    </button>
                  </div>
                ) : receipt.processing_status === 'failed' ? (
                  <span className="text-gray-500 text-sm">-</span>
                ) : (
                  <span className="text-gray-500 italic text-sm">Pending</span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer info */}
      <div className="text-sm text-gray-400 text-center">
        <p>
          Receipts are automatically processed and exported to your Google Sheets. 
          {pendingCount > 0 && (
            <> Click "Process Pending" to manually trigger processing for queued receipts.</>
          )}
        </p>
      </div>
    </div>
  );
} 