'use client';

import { useState, useEffect } from 'react';
import { <PERSON>r, <PERSON>ting<PERSON>, CreditCard, Database, Zap, Link } from 'lucide-react';
import { cn } from '@/lib/utils';
import AccountSettings from './AccountSettings';
import IntegrationsSettings from './IntegrationsSettings';
import ProcessingSettings from './ProcessingSettings';
import DataSettings from './DataSettings';
import BillingSettings from './BillingSettings';

export interface UserData {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  google_access_token: string | null;
  google_refresh_token: string | null;
  google_sheets_connected: boolean;
  current_tier: string;
  receipts_processed: number;
  subscription_status: string;
  subscription_end_date: string | null;
  monthly_receipt_limit?: number;
  receipts_used_this_period?: number;
  current_period_start?: string;
  current_period_end?: string;
  next_billing_date?: string;
  data_retention_months?: number;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  default_currency: string;
  auto_process_receipts: boolean;
  confidence_threshold: number;
  notification_email: boolean;
  notification_processing: boolean;
  data_retention_months: number;
  export_format: string;
}

const tabs = [
  {
    id: 'account',
    label: 'Account',
    icon: User,
    description: 'Profile and subscription'
  },
  {
    id: 'integrations',
    label: 'Integrations',
    icon: Link,
    description: 'Google Sheets & Drive'
  },
  {
    id: 'processing',
    label: 'Processing',
    icon: Zap,
    description: 'AI and extraction settings'
  },
  {
    id: 'data',
    label: 'Data',
    icon: Database,
    description: 'Export and backup'
  },
  {
    id: 'billing',
    label: 'Billing',
    icon: CreditCard,
    description: 'Plans and payments'
  }
];

export default function SettingsClient() {
  const [activeTab, setActiveTab] = useState('account');
  const [userData, setUserData] = useState<UserData | null>(null);
  const [userPreferences, setUserPreferences] = useState<UserPreferences | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load user data and preferences
  useEffect(() => {
    const loadUserData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch('/api/user/profile');
        if (!response.ok) {
          throw new Error('Failed to load user data');
        }

        const data = await response.json();
        if (!data.success) {
          throw new Error(data.error || 'Failed to load user data');
        }

        setUserData(data.user);
        setUserPreferences(data.preferences);
      } catch (err) {
        console.error('Error loading user data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load user data');
      } finally {
        setLoading(false);
      }
    };

    loadUserData();
  }, []);

  const refreshUserData = async () => {
    try {
      const response = await fetch('/api/user/profile');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setUserData(data.user);
          setUserPreferences(data.preferences);
        }
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-gray-400">Loading settings...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-4">{error}</div>
        <button
          onClick={() => window.location.reload()}
          className="bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  const renderTabContent = () => {
    if (!userData || !userPreferences) return null;

    switch (activeTab) {
      case 'account':
        return (
          <AccountSettings
            userData={userData}
            onUserDataUpdate={refreshUserData}
          />
        );
      case 'integrations':
        return (
          <IntegrationsSettings
            userData={userData}
            onUserDataUpdate={refreshUserData}
          />
        );
      case 'processing':
        return (
          <ProcessingSettings
            userPreferences={userPreferences}
            onPreferencesUpdate={refreshUserData}
          />
        );
      case 'data':
        return (
          <DataSettings
            userData={userData}
            userPreferences={userPreferences}
            onPreferencesUpdate={refreshUserData}
          />
        );
      case 'billing':
        return (
          <BillingSettings
            userData={userData}
            onUserDataUpdate={refreshUserData}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-700">
        {/* Desktop Navigation */}
        <nav className="hidden sm:flex space-x-8 overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "flex items-center gap-3 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap transition-colors",
                  isActive
                    ? "border-pink-500 text-pink-400"
                    : "border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600"
                )}
              >
                <Icon className="w-5 h-5" />
                <div className="text-left">
                  <div>{tab.label}</div>
                  <div className="text-xs text-gray-500">{tab.description}</div>
                </div>
              </button>
            );
          })}
        </nav>

        {/* Mobile Navigation */}
        <nav className="sm:hidden flex overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={cn(
                  "flex flex-col items-center gap-1 py-3 px-3 border-b-2 font-medium text-xs whitespace-nowrap transition-colors min-w-0 flex-shrink-0",
                  isActive
                    ? "border-pink-500 text-pink-400"
                    : "border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-600"
                )}
              >
                <Icon className="w-4 h-4" />
                <span className="truncate">{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px] sm:min-h-[600px]">
        {renderTabContent()}
      </div>
    </div>
  );
}
