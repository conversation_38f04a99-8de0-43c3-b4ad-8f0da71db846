/**
 * Enhanced error handling and logging for Google Sheets API interactions
 */

export enum GoogleSheetsErrorType {
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  SPREADSHEET_NOT_FOUND = 'SPREADSHEET_NOT_FOUND',
  INVALID_REQUEST = 'INVALID_REQUEST',
  DATABASE_ERROR = 'DATABASE_ERROR',
  TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface GoogleSheetsError {
  type: GoogleSheetsErrorType;
  message: string;
  originalError?: any;
  httpStatus?: number;
  retryAfter?: number; // seconds to wait for rate limiting
  context?: Record<string, any>;
}

/**
 * Enhanced logger for Google Sheets operations
 */
export class GoogleSheetsLogger {
  private static instance: GoogleSheetsLogger;
  
  static getInstance(): GoogleSheetsLogger {
    if (!GoogleSheetsLogger.instance) {
      GoogleSheetsLogger.instance = new GoogleSheetsLogger();
    }
    return GoogleSheetsLogger.instance;
  }

  logOperation(operation: string, userId: string, context?: Record<string, any>) {
    const logData = {
      timestamp: new Date().toISOString(),
      operation,
      userId: this.sanitizeUserId(userId),
      context: this.sanitizeContext(context)
    };
    console.log(`[GoogleSheets:${operation}]`, logData);
  }

  logSuccess(operation: string, userId: string, result?: any) {
    const logData = {
      timestamp: new Date().toISOString(),
      operation,
      userId: this.sanitizeUserId(userId),
      success: true,
      result: this.sanitizeResult(result)
    };
    console.log(`[GoogleSheets:${operation}:SUCCESS]`, logData);
  }

  logError(operation: string, userId: string, error: GoogleSheetsError) {
    const logData = {
      timestamp: new Date().toISOString(),
      operation,
      userId: this.sanitizeUserId(userId),
      error: {
        type: error.type,
        message: error.message,
        httpStatus: error.httpStatus,
        retryAfter: error.retryAfter,
        context: this.sanitizeContext(error.context)
      }
    };
    console.error(`[GoogleSheets:${operation}:ERROR]`, logData);
  }

  logWarning(operation: string, userId: string, message: string, context?: Record<string, any>) {
    const logData = {
      timestamp: new Date().toISOString(),
      operation,
      userId: this.sanitizeUserId(userId),
      warning: message,
      context: this.sanitizeContext(context)
    };
    console.warn(`[GoogleSheets:${operation}:WARNING]`, logData);
  }

  private sanitizeUserId(userId: string): string {
    // Only show first and last 4 characters for privacy
    if (userId.length <= 8) return '*'.repeat(userId.length);
    return `${userId.slice(0, 4)}****${userId.slice(-4)}`;
  }

  private sanitizeContext(context?: Record<string, any>): Record<string, any> | undefined {
    if (!context) return undefined;
    
    const sanitized = { ...context };
    
    // Remove sensitive information
    const sensitiveKeys = ['access_token', 'refresh_token', 'password', 'secret'];
    for (const key of sensitiveKeys) {
      if (sanitized[key]) {
        sanitized[key] = '[REDACTED]';
      }
    }
    
    return sanitized;
  }

  private sanitizeResult(result?: any): any {
    if (!result) return result;
    
    if (typeof result === 'object') {
      const sanitized = { ...result };
      
      // Remove sensitive data from results
      const sensitiveKeys = ['access_token', 'refresh_token'];
      for (const key of sensitiveKeys) {
        if (sanitized[key]) {
          sanitized[key] = '[REDACTED]';
        }
      }
      
      return sanitized;
    }
    
    return result;
  }
}

/**
 * Parse Google Sheets API error responses
 */
export function parseGoogleSheetsError(error: any, operation: string): GoogleSheetsError {
  const logger = GoogleSheetsLogger.getInstance();
  
  // Handle fetch/network errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return {
      type: GoogleSheetsErrorType.NETWORK_ERROR,
      message: 'Network connection failed',
      originalError: error
    };
  }

  // Handle HTTP response errors
  if (error.response || error.status) {
    const status = error.response?.status || error.status;
    const data = error.response?.data || error.data || {};
    
    switch (status) {
      case 401:
        return {
          type: GoogleSheetsErrorType.AUTHENTICATION_FAILED,
          message: 'Authentication failed - token may be expired',
          httpStatus: status,
          originalError: error
        };
      
      case 403:
        if (data.error?.message?.includes('rate') || data.error?.message?.includes('quota')) {
          const retryAfter = parseInt(error.response?.headers?.['retry-after']) || 60;
          return {
            type: GoogleSheetsErrorType.RATE_LIMIT_EXCEEDED,
            message: 'Rate limit exceeded',
            httpStatus: status,
            retryAfter,
            originalError: error
          };
        }
        return {
          type: GoogleSheetsErrorType.PERMISSION_DENIED,
          message: 'Permission denied - insufficient access rights',
          httpStatus: status,
          originalError: error
        };
      
      case 404:
        return {
          type: GoogleSheetsErrorType.SPREADSHEET_NOT_FOUND,
          message: 'Spreadsheet not found',
          httpStatus: status,
          originalError: error
        };
      
      case 400:
        return {
          type: GoogleSheetsErrorType.INVALID_REQUEST,
          message: data.error?.message || 'Invalid request parameters',
          httpStatus: status,
          originalError: error
        };
      
      case 429:
        const retryAfter = parseInt(error.response?.headers?.['retry-after']) || 60;
        return {
          type: GoogleSheetsErrorType.QUOTA_EXCEEDED,
          message: 'API quota exceeded',
          httpStatus: status,
          retryAfter,
          originalError: error
        };
      
      default:
        return {
          type: GoogleSheetsErrorType.UNKNOWN_ERROR,
          message: `HTTP ${status}: ${data.error?.message || 'Unknown error'}`,
          httpStatus: status,
          originalError: error
        };
    }
  }

  // Handle specific error types
  if (error.message?.includes('token') || error.message?.includes('auth')) {
    return {
      type: GoogleSheetsErrorType.AUTHENTICATION_FAILED,
      message: error.message,
      originalError: error
    };
  }

  if (error.message?.includes('database') || error.code === 'PGRST') {
    return {
      type: GoogleSheetsErrorType.DATABASE_ERROR,
      message: 'Database operation failed',
      originalError: error
    };
  }

  // Default unknown error
  return {
    type: GoogleSheetsErrorType.UNKNOWN_ERROR,
    message: error.message || 'An unknown error occurred',
    originalError: error
  };
}

/**
 * Retry logic for transient failures
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000,
  operationName: string = 'operation'
): Promise<T> {
  const logger = GoogleSheetsLogger.getInstance();
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const result = await operation();
      
      if (attempt > 1) {
        logger.logSuccess(`${operationName}:retry`, 'system', { 
          attempt, 
          message: 'Retry successful' 
        });
      }
      
      return result;
    } catch (error) {
      const parsedError = parseGoogleSheetsError(error, operationName);
      
      // Don't retry for certain error types
      const nonRetryableErrors = [
        GoogleSheetsErrorType.AUTHENTICATION_FAILED,
        GoogleSheetsErrorType.PERMISSION_DENIED,
        GoogleSheetsErrorType.SPREADSHEET_NOT_FOUND,
        GoogleSheetsErrorType.INVALID_REQUEST
      ];
      
      if (nonRetryableErrors.includes(parsedError.type) || attempt === maxRetries) {
        logger.logError(`${operationName}:final-failure`, 'system', parsedError);
        throw error;
      }
      
      // Calculate delay (exponential backoff with jitter)
      let delay = baseDelay * Math.pow(2, attempt - 1);
      
      // Use retry-after header if available for rate limiting
      if (parsedError.retryAfter) {
        delay = parsedError.retryAfter * 1000;
      }
      
      // Add jitter (±25%)
      delay = delay + (Math.random() - 0.5) * delay * 0.5;
      
      logger.logWarning(`${operationName}:retry`, 'system', 
        `Attempt ${attempt} failed, retrying in ${Math.round(delay)}ms`, 
        { attempt, delay, errorType: parsedError.type }
      );
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new Error(`All retry attempts failed for ${operationName}`);
}

/**
 * Wrapper for Google Sheets API calls with comprehensive error handling
 */
export async function executeGoogleSheetsOperation<T>(
  operation: () => Promise<T>,
  operationName: string,
  userId: string,
  context?: Record<string, any>
): Promise<{ success: boolean; data?: T; error?: GoogleSheetsError }> {
  const logger = GoogleSheetsLogger.getInstance();
  
  logger.logOperation(operationName, userId, context);
  
  try {
    const result = await withRetry(operation, 3, 1000, operationName);
    
    logger.logSuccess(operationName, userId, { 
      hasResult: !!result,
      resultType: typeof result 
    });
    
    return { success: true, data: result };
  } catch (error) {
    const parsedError = parseGoogleSheetsError(error, operationName);
    logger.logError(operationName, userId, parsedError);
    
    return { success: false, error: parsedError };
  }
} 