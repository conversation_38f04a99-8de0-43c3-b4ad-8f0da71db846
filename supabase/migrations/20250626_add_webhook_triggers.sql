-- Migration to add webhook triggers for event-driven receipt processing
-- This replaces the expensive frontend polling with database-triggered webhooks

-- Enable the http extension for making HTTP requests from triggers
-- Note: This needs to be enabled by Supabase admin in production
CREATE EXTENSION IF NOT EXISTS http;

-- <PERSON>reate function to trigger receipt processing via webhook
CREATE OR REPLACE FUNCTION trigger_receipt_processing()
RETURNS TRIGGER AS $$
DECLARE
  webhook_url TEXT;
  payload JSON;
  response_status INTEGER;
  job_id TEXT;
BEGIN
  -- Only trigger for new receipts that need processing
  IF TG_OP = 'INSERT' AND NEW.processing_status = 'pending' THEN
    -- Generate a unique job ID
    job_id := 'job_' || gen_random_uuid()::text;
    
    -- Get the webhook URL from environment or use default
    -- In production, this should be set via Supabase dashboard
    webhook_url := COALESCE(
      current_setting('app.webhook_url', true),
      'https://your-app-domain.vercel.app/api/webhook/process-receipt'
    );
    
    -- Create payload for webhook
    payload := json_build_object(
      'event_type', 'receipt.uploaded',
      'job_id', job_id,
      'receipt_id', NEW.id,
      'user_id', NEW.user_id,
      'file_path', NEW.file_path,
      'file_name', NEW.original_file_name,
      'file_size', NEW.file_size,
      'mime_type', NEW.mime_type,
      'priority', 'normal',
      'created_at', NEW.created_at
    );
    
    -- Update receipt with job ID
    UPDATE receipts 
    SET redis_job_id = job_id 
    WHERE id = NEW.id;
    
    -- Make HTTP request to webhook (async)
    BEGIN
      SELECT status INTO response_status
      FROM http((
        'POST',
        webhook_url,
        ARRAY[
          http_header('Content-Type', 'application/json'),
          http_header('User-Agent', 'Supabase-Webhook/1.0')
        ],
        'application/json',
        payload::text
      )::http_request);
      
      -- Log successful webhook call
      RAISE NOTICE 'Webhook triggered successfully for receipt %, job %, status: %', NEW.id, job_id, response_status;
      
      -- If webhook failed, we could add the job to Redis queue as fallback
      IF response_status >= 400 THEN
        RAISE WARNING 'Webhook returned error status % for receipt %', response_status, NEW.id;
        -- Could implement fallback logic here
      END IF;
      
    EXCEPTION WHEN OTHERS THEN
      -- Log webhook failure but don't fail the transaction
      RAISE WARNING 'Failed to trigger webhook for receipt %, job %: %', NEW.id, job_id, SQLERRM;
      
      -- Update receipt status to indicate webhook failure
      UPDATE receipts 
      SET error_message = 'Webhook trigger failed: ' || SQLERRM
      WHERE id = NEW.id;
    END;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger that fires after receipt insert
CREATE TRIGGER trigger_receipt_processing_webhook
  AFTER INSERT ON receipts
  FOR EACH ROW
  EXECUTE FUNCTION trigger_receipt_processing();

-- Create function to handle job status updates via Redis pub/sub
CREATE OR REPLACE FUNCTION notify_job_status_change()
RETURNS TRIGGER AS $$
DECLARE
  notification_payload JSON;
BEGIN
  -- Only notify on status changes for processing-related fields
  IF TG_OP = 'UPDATE' AND (
    OLD.processing_status IS DISTINCT FROM NEW.processing_status OR
    OLD.confidence_score IS DISTINCT FROM NEW.confidence_score OR
    OLD.error_message IS DISTINCT FROM NEW.error_message OR
    OLD.google_sheet_row_number IS DISTINCT FROM NEW.google_sheet_row_number
  ) THEN
    
    -- Create notification payload
    notification_payload := json_build_object(
      'event_type', 'job.status_changed',
      'receipt_id', NEW.id,
      'user_id', NEW.user_id,
      'job_id', NEW.redis_job_id,
      'old_status', OLD.processing_status,
      'new_status', NEW.processing_status,
      'confidence_score', NEW.confidence_score,
      'error_message', NEW.error_message,
      'google_sheet_row_number', NEW.google_sheet_row_number,
      'updated_at', NEW.updated_at
    );
    
    -- Send notification via PostgreSQL NOTIFY
    -- This can be picked up by the application for real-time updates
    PERFORM pg_notify(
      'job_status_changes',
      notification_payload::text
    );
    
    -- Also notify user-specific channel
    PERFORM pg_notify(
      'user_' || NEW.user_id || '_job_updates',
      notification_payload::text
    );
    
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for job status notifications
CREATE TRIGGER notify_job_status_change_trigger
  AFTER UPDATE ON receipts
  FOR EACH ROW
  EXECUTE FUNCTION notify_job_status_change();

-- Create index for efficient job status queries
CREATE INDEX IF NOT EXISTS idx_receipts_processing_status 
ON receipts(processing_status, created_at) 
WHERE processing_status IN ('pending', 'processing');

-- Create index for user-specific job queries
CREATE INDEX IF NOT EXISTS idx_receipts_user_status 
ON receipts(user_id, processing_status, created_at);

-- Add comment explaining the webhook system
COMMENT ON FUNCTION trigger_receipt_processing() IS 
'Triggers webhook when new receipts are uploaded, replacing expensive frontend polling';

COMMENT ON FUNCTION notify_job_status_change() IS 
'Sends real-time notifications when job status changes via PostgreSQL NOTIFY';
