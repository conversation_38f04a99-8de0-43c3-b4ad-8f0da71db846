import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    // Get user's Google tokens
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('google_access_token, google_refresh_token, email')
      .eq('id', user.id)
      .single();

    if (userError) {
      return NextResponse.json({ 
        success: false, 
        error: `Failed to get user data: ${userError.message}` 
      }, { status: 500 });
    }

    const hasGoogleAuth = !!(userData?.google_access_token && userData?.google_refresh_token);

    // Test Google Sheets API connection if user has tokens
    let googleSheetsTest = null;
    if (hasGoogleAuth) {
      try {
        // Test the Google Sheets API by making a simple request
        const testResponse = await fetch('https://sheets.googleapis.com/v4/spreadsheets', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${userData.google_access_token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            properties: {
              title: 'Test Sheet - Delete Me'
            }
          })
        });

        if (testResponse.ok) {
          const testSheet = await testResponse.json();
          googleSheetsTest = {
            success: true,
            message: 'Google Sheets API connection successful',
            testSheetId: testSheet.spreadsheetId
          };

          // Clean up test sheet
          try {
            await fetch(`https://www.googleapis.com/drive/v3/files/${testSheet.spreadsheetId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${userData.google_access_token}`
              }
            });
          } catch (cleanupError) {
            console.log('Failed to cleanup test sheet:', cleanupError);
          }
        } else {
          const errorText = await testResponse.text();
          googleSheetsTest = {
            success: false,
            message: `Google Sheets API test failed: ${testResponse.status} ${testResponse.statusText}`,
            error: errorText
          };
        }
      } catch (testError) {
        googleSheetsTest = {
          success: false,
          message: 'Google Sheets API test error',
          error: testError instanceof Error ? testError.message : 'Unknown error'
        };
      }
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: userData?.email
      },
      googleAuth: {
        hasTokens: hasGoogleAuth,
        hasAccessToken: !!userData?.google_access_token,
        hasRefreshToken: !!userData?.google_refresh_token
      },
      googleSheetsTest
    });

  } catch (error) {
    console.error('Test Google Sheets error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
