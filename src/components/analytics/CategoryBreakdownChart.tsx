'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { BarChart3, TrendingUp, Calendar, Eye } from 'lucide-react';
import { useState } from 'react';

interface CategoryBreakdownChartProps {
  data: { category: string; total: number; percentage: number }[];
  currency?: string;
  onCategoryClick?: (category: string) => void;
}

const COLORS = ['#FF006E', '#FB8500', '#8338EC', '#00F5FF', '#FFBE0B', '#8B5CF6', '#10B981', '#F59E0B'];

export default function CategoryBreakdownChart({ data, currency = 'KES', onCategoryClick }: CategoryBreakdownChartProps) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const hasData = data && data.length > 0 && data.some(item => item.total > 0);

  // Format currency based on the currency type
  const formatCurrency = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
  };

  const formatCurrencyDetailed = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  const totalSpending = hasData ? data.reduce((sum, item) => sum + item.total, 0) : 0;

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category === selectedCategory ? null : category);
    onCategoryClick?.(category);
  };

  // Prepare data for horizontal bar chart (sorted by amount)
  const sortedData = hasData ? [...data].sort((a, b) => b.total - a.total) : [];

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-white flex items-center">
            <BarChart3 className="w-5 h-5 mr-2 text-accent-purple" />
            Category Breakdown
          </h3>
          <p className="text-sm text-gray-400">Click categories to view detailed analysis</p>
        </div>
        {hasData && (
          <div className="text-right">
            <p className="text-sm text-gray-400">Total Categories</p>
            <p className="text-lg font-semibold text-white">{data.length}</p>
          </div>
        )}
      </div>

      {hasData ? (
        <>
          {/* Horizontal Bar Chart */}
          <ResponsiveContainer width="100%" height={400}>
            <BarChart
              data={sortedData}
              layout="horizontal"
              margin={{ top: 20, right: 30, left: 80, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis
                type="number"
                stroke="#9CA3AF"
                fontSize={12}
                tick={{ fill: '#9CA3AF' }}
                tickFormatter={(value) => formatCurrency(value)}
              />
              <YAxis
                type="category"
                dataKey="category"
                stroke="#9CA3AF"
                fontSize={11}
                tick={{ fill: '#9CA3AF' }}
                width={70}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '12px',
                  color: '#F9FAFB',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                }}
                formatter={(value: any, name: string, props: any) => [
                  <div key="category-bar-tooltip" className="space-y-1">
                    <div className="font-medium">{props.payload.category}</div>
                    <div>Amount: {formatCurrencyDetailed(value)}</div>
                    <div>Percentage: {props.payload.percentage.toFixed(1)}%</div>
                    <div className="text-xs text-gray-400 mt-2">Click to view detailed analysis</div>
                  </div>
                ]}
                labelFormatter={() => ''}
              />
              <Bar
                dataKey="total"
                fill="#8338EC"
                radius={[0, 4, 4, 0]}
                cursor="pointer"
                onClick={(data) => handleCategoryClick(data.category)}
              />
            </BarChart>
          </ResponsiveContainer>

          {/* Interactive Category List */}
          <div className="mt-6 space-y-3">
            <h4 className="text-white font-medium flex items-center">
              <Eye className="w-4 h-4 mr-2 text-accent-purple" />
              Category Details
            </h4>
            <div className="grid grid-cols-1 gap-2">
              {sortedData.map((category, index) => (
                <button
                  key={category.category}
                  onClick={() => handleCategoryClick(category.category)}
                  className={`flex items-center justify-between p-4 rounded-lg transition-all duration-200 hover:bg-gray-700/50 ${
                    selectedCategory === category.category
                      ? 'bg-accent-purple/20 border border-accent-purple/30'
                      : 'bg-gray-700/30 border border-transparent'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: COLORS[index % COLORS.length] }}
                    />
                    <div className="text-left">
                      <span className="text-white text-sm font-medium">{category.category}</span>
                      <p className="text-xs text-gray-400">Click for detailed analysis</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white text-sm font-medium">{formatCurrency(category.total)}</p>
                    <p className="text-xs text-gray-400">{category.percentage.toFixed(1)}%</p>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Total Spending Summary */}
          {totalSpending > 0 && (
            <div className="mt-6 p-4 bg-gradient-to-r from-accent-purple/10 to-accent-pink/10 border border-accent-purple/20 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2 text-accent-purple" />
                  <span className="text-white font-medium">Total Category Spending</span>
                </div>
                <span className="text-accent-purple font-bold text-xl">{formatCurrency(totalSpending)}</span>
              </div>
            </div>
          )}

          {/* Selected Category Analysis Placeholder */}
          {selectedCategory && (
            <div className="mt-6 p-4 bg-gray-700/30 border border-gray-600 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-white font-medium flex items-center">
                  <Calendar className="w-4 h-4 mr-2 text-accent-purple" />
                  {selectedCategory} Analysis
                </h4>
                <button
                  onClick={() => setSelectedCategory(null)}
                  className="text-gray-400 hover:text-white text-sm"
                >
                  Close
                </button>
              </div>
              <div className="text-gray-300 text-sm">
                <p>Detailed monthly and annual analysis for <strong>{selectedCategory}</strong> will be implemented here.</p>
                <p className="mt-2 text-gray-400">Features coming soon:</p>
                <ul className="mt-1 ml-4 text-gray-400 text-xs">
                  <li>• Monthly spending trends</li>
                  <li>• Year-over-year comparison</li>
                  <li>• Top vendors in this category</li>
                  <li>• Receipt breakdown</li>
                </ul>
              </div>
            </div>
          )}
        </>
      ) : (
        <div className="h-[400px] flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gray-700/30 rounded-full flex items-center justify-center">
              <BarChart3 className="w-10 h-10" />
            </div>
            <p className="text-lg font-medium mb-2">No categories yet</p>
            <p className="text-sm">Upload receipts to see spending by category</p>
          </div>
        </div>
      )}
    </div>
  );
}
