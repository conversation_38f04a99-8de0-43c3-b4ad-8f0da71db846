# Webhook Setup Guide

This guide explains how to set up the event-driven receipt processing system that replaces expensive polling with database webhooks.

## Overview

The new system works as follows:
1. **Upload**: User uploads receipt → Database insert with `processing_status = 'pending'`
2. **Trigger**: Database trigger fires → Webhook called immediately
3. **Process**: Webhook creates job and triggers immediate processing
4. **Fallback**: Lightweight polling every 10+ minutes for stuck jobs only

## Setup Steps

### 1. Enable HTTP Extension in Supabase

In your Supabase SQL editor, run:
```sql
CREATE EXTENSION IF NOT EXISTS http;
```

### 2. Set Webhook URL

You need to configure the webhook URL in Supabase. You have two options:

#### Option A: Set via SQL (Recommended)
```sql
-- Replace with your actual domain
ALTER DATABASE postgres SET app.webhook_url = 'https://your-app-domain.vercel.app/api/webhook/process-receipt';
```

#### Option B: Update the migration file
Edit `supabase/migrations/20250626_add_webhook_triggers.sql` and replace:
```sql
'https://your-app-domain.vercel.app/api/webhook/process-receipt'
```
with your actual domain.

### 3. Apply the Migration

Run the migration:
```bash
supabase db push
```

### 4. Verify Setup

1. Upload a receipt through your app
2. Check the logs to see:
   - Database trigger firing
   - Webhook being called
   - Immediate processing starting

## Cost Savings

### Before (Polling System)
- Frontend polls every 5 seconds
- ~17,280 API calls per day per active user
- Constant Redis operations
- Unnecessary Edge Function invocations
- High Redis costs due to continuous operations

### After (Event-Driven System)
- **Primary**: Database webhooks trigger immediate processing
- **Secondary**: Real-time notifications via SSE/polling
- **Fallback**: Cleanup every 10 minutes, processing checks every 5 minutes
- **Maximum daily operations**: ~432 fallback operations per day
- **Cost reduction**: 99%+ reduction in unnecessary API calls
- **Performance improvement**: Immediate processing vs polling delays

## System Architecture

### Three-Layer Approach
1. **Event-Driven Layer**: Database triggers → Webhooks → Immediate processing
2. **Real-time Layer**: Redis pub/sub → SSE → Live UI updates
3. **Fallback Layer**: Periodic cleanup and stuck job recovery

### Components
- **Database Triggers**: Auto-fire on receipt uploads
- **Webhook Endpoint**: `/api/webhook/process-receipt`
- **Real-time Notifications**: `/api/notifications/stream` (SSE)
- **Fallback Cleanup**: `/api/fallback/cleanup`
- **System Health**: Admin monitoring dashboard

## Monitoring

### Check Webhook Logs
```sql
-- View recent webhook notifications
SELECT * FROM pg_stat_activity WHERE query LIKE '%trigger_receipt_processing%';
```

### Check Processing Status
```sql
-- View receipts and their processing status
SELECT 
  id,
  original_file_name,
  processing_status,
  redis_job_id,
  created_at,
  updated_at
FROM receipts 
WHERE created_at > NOW() - INTERVAL '1 hour'
ORDER BY created_at DESC;
```

## Troubleshooting

### Webhook Not Firing
1. Check if HTTP extension is enabled: `SELECT * FROM pg_extension WHERE extname = 'http';`
2. Verify webhook URL is set correctly
3. Check Supabase logs for trigger errors

### Jobs Not Processing
1. Check if webhook endpoint is accessible
2. Verify Redis connection
3. Check Edge Function logs
4. Fallback polling will catch stuck jobs

### High Costs Still Occurring
1. Verify old polling is disabled
2. Check for other polling components
3. Monitor Redis operation frequency

## Security Considerations

### Webhook Authentication (Optional)
To add webhook signature verification:

1. Set a webhook secret:
```bash
# In your environment
WEBHOOK_SECRET=your-secret-key
```

2. Update the webhook endpoint to verify signatures
3. Update the database trigger to include signatures

### Rate Limiting
The webhook endpoint includes basic rate limiting and validation to prevent abuse.

## Rollback Plan

If you need to rollback to polling:

1. Disable the trigger:
```sql
DROP TRIGGER IF EXISTS trigger_receipt_processing_webhook ON receipts;
```

2. Restore old upload logic (add jobs to queue directly)
3. Reduce polling intervals in QueueProcessor

## Performance Monitoring

Monitor these metrics:
- Webhook response times
- Job processing latency
- Redis operation frequency
- Edge Function invocation count
- Database trigger execution time

The new system should show:
- 99%+ reduction in API calls
- Faster processing (immediate vs polling delay)
- Lower Redis costs
- Reduced Edge Function costs
