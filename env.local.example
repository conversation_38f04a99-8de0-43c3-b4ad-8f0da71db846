# ReceiptLabs – Local development environment variables
# Copy this file to `.env.local` in the project root and replace placeholder values.

# Supabase
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# OpenAI
OPENAI_API_KEY=********************************************************************************************************************************************************************

# Upstash Redis
UPSTASH_REDIS_REST_URL=https://ruling-mantis-43071.upstash.io
UPSTASH_REDIS_REST_TOKEN=Aag_AAIjcDFlNjI3YWMzMmZmNmE0MjhhOTg3NGJhMzIwZTQyMTViZXAxMA

# Google OAuth
GOOGLE_CLIENT_ID=569152335035-8v92u2n7lb039j12au3phtcf4barbp6b.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Google API Key (for Google Picker)
NEXT_PUBLIC_GOOGLE_API_KEY=your_google_api_key_here

# Paystack
PAYSTACK_SECRET_KEY=your_paystack_secret_key 