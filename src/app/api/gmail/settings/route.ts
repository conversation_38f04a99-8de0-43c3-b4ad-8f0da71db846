import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { updateGmailSettings } from '@/lib/gmail/client';

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user has Business tier access
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    if (userData.current_tier !== 'business') {
      return NextResponse.json({ error: 'Business plan required' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    const { processingEnabled, processingTime, keywords } = body;

    // Validate inputs
    if (processingEnabled !== undefined && typeof processingEnabled !== 'boolean') {
      return NextResponse.json({ error: 'Invalid processingEnabled value' }, { status: 400 });
    }

    if (processingTime && !/^\d{2}:\d{2}:\d{2}$/.test(processingTime)) {
      return NextResponse.json({ error: 'Invalid processingTime format (HH:MM:SS)' }, { status: 400 });
    }

    if (keywords && typeof keywords !== 'string') {
      return NextResponse.json({ error: 'Invalid keywords value' }, { status: 400 });
    }

    // Update settings
    const result = await updateGmailSettings(user.id, {
      processingEnabled,
      processingTime,
      keywords: keywords?.trim()
    });

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'Gmail settings updated successfully'
    });
  } catch (error) {
    console.error('Error updating Gmail settings:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update Gmail settings' 
      }, 
      { status: 500 }
    );
  }
}
