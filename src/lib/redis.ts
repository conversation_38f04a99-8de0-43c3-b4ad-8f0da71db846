import { Redis } from '@upstash/redis'

// For local development, make Redis optional
const UPSTASH_URL = process.env.UPSTASH_REDIS_REST_URL;
const UPSTASH_TOKEN = process.env.UPSTASH_REDIS_REST_TOKEN;

// Mock Redis for local development when Upstash credentials aren't available
const createMockRedis = () => {
  // In-memory storage for mock Redis
  const storage = new Map<string, string>();
  const sortedSets = new Map<string, Array<{ score: number; member: string }>>();
  const hashMaps = new Map<string, Record<string, string>>();
  
  return {
    // Basic operations
    get: async (key: string) => storage.get(key) || null,
    set: async (key: string, value: string) => {
      storage.set(key, value);
      return 'OK';
    },
    setex: async (key: string, seconds: number, value: string) => {
      storage.set(key, value);
      // In real implementation, would expire after seconds
      return 'OK';
    },
    del: async (...keys: string[]) => {
      let count = 0;
      keys.forEach(key => {
        if (storage.has(key)) {
          storage.delete(key);
          count++;
        }
        if (sortedSets.has(key)) {
          sortedSets.delete(key);
          count++;
        }
        if (hashMaps.has(key)) {
          hashMaps.delete(key);
          count++;
        }
      });
      return count;
    },
    expire: async (key: string, seconds: number) => {
      // Mock implementation - in real Redis this would set expiration
      return 1;
    },
    keys: async (pattern: string) => {
      // Simple pattern matching for mock
      if (pattern === '*') {
        return [...storage.keys(), ...sortedSets.keys(), ...hashMaps.keys()];
      }
      return [];
    },
    
    // List operations
    lpush: async (key: string, ...values: string[]) => {
      // Mock implementation - not fully implemented for this use case
      return values.length;
    },
    rpop: async (key: string) => null,
    llen: async (key: string) => 0,
    
    // Sorted set operations - Properly implemented mock
    zadd: async (key: string, scoreMembers: { score: number; member: string } | { score: number; member: string }[]) => {
      console.log(`Mock Redis ZADD: key=${key}, scoreMembers=`, scoreMembers);
      
      if (!sortedSets.has(key)) {
        sortedSets.set(key, []);
      }
      
      const set = sortedSets.get(key)!;
      const membersToAdd = Array.isArray(scoreMembers) ? scoreMembers : [scoreMembers];
      
      let added = 0;
      membersToAdd.forEach(({ score, member }) => {
        // Remove existing member if present
        const existingIndex = set.findIndex(item => item.member === member);
        if (existingIndex >= 0) {
          set.splice(existingIndex, 1);
        } else {
          added++;
        }
        
        // Add new member
        set.push({ score, member });
        
        // Keep sorted by score (ascending)
        set.sort((a, b) => a.score - b.score);
      });
      
      console.log(`Mock Redis ZADD result: added=${added}, set size=${set.length}`);
      console.log('Current set contents:', set.slice(0, 2)); // Log first 2 items
      
      return added;
    },
    
    zrem: async (key: string, member: string): Promise<number> => {
      console.log(`Mock Redis ZREM: key=${key}, member=${member.substring(0, 100)}...`);
      
      if (!sortedSets.has(key)) {
        console.log(`Mock Redis ZREM: key ${key} not found, nothing to remove`);
        return 0;
      }
      
      const set = sortedSets.get(key)!;
      const initialLength = set.length;
      
      // Remove all items that match the member
      const filteredSet = set.filter(item => {
        const memberToCompare = typeof item.member === 'string' ? item.member : JSON.stringify(item.member);
        return memberToCompare !== member;
      });
      
      const removedCount = initialLength - filteredSet.length;
      
      if (removedCount > 0) {
        sortedSets.set(key, filteredSet);
        console.log(`Mock Redis ZREM: removed ${removedCount} items from ${key}, new size: ${filteredSet.length}`);
      } else {
        console.log(`Mock Redis ZREM: no items removed from ${key}, member not found`);
      }
      
      return removedCount;
    },
    
    zrange: async (key: string, start: number, stop: number, options?: { withScores?: boolean }) => {
      console.log(`Mock Redis ZRANGE: key=${key}, start=${start}, stop=${stop}, withScores=${options?.withScores}`);
      
      if (!sortedSets.has(key)) {
        console.log(`Mock Redis ZRANGE: key ${key} not found, returning empty array`);
        return [];
      }
      
      const set = sortedSets.get(key)!;
      console.log(`Mock Redis ZRANGE: found set with ${set.length} items`);
      
      const slice = set.slice(start, stop === -1 ? set.length - 1 : stop + 1);
      console.log(`Mock Redis ZRANGE: slice contains ${slice.length} items`);
      
      if (options?.withScores) {
        // Return flat array alternating between member and score (matching real Redis format)
        // The queue expects: [memberString, scoreNumber, memberString, scoreNumber, ...]
        const result: (string | number)[] = [];
        slice.forEach(item => {
          result.push(item.member, item.score);
        });
        console.log(`Mock Redis ZRANGE: returning withScores result, length=${result.length}`);
        return result;
      } else {
        // Return array of members only
        const result = slice.map(item => item.member);
        console.log(`Mock Redis ZRANGE: returning members only, length=${result.length}`);
        return result;
      }
    },
    
    zcard: async (key: string) => {
      const count = sortedSets.has(key) ? sortedSets.get(key)!.length : 0;
      console.log(`Mock Redis ZCARD: key=${key}, count=${count}`);
      return count;
    },
    
    zremrangebyscore: async (key: string, min: number, max: number) => {
      if (!sortedSets.has(key)) return 0;
      
      const set = sortedSets.get(key)!;
      const initialLength = set.length;
      
      // Remove items with scores between min and max
      for (let i = set.length - 1; i >= 0; i--) {
        if (set[i].score >= min && set[i].score <= max) {
          set.splice(i, 1);
        }
      }
      
      return initialLength - set.length;
    },
    
    // Hash operations - Properly implemented mock
    hset: async (key: string, fields: Record<string, string | number>) => {
      if (!hashMaps.has(key)) {
        hashMaps.set(key, {});
      }
      
      const hash = hashMaps.get(key)!;
      let fieldsSet = 0;
      
      Object.entries(fields).forEach(([field, value]) => {
        const wasNew = !(field in hash);
        hash[field] = String(value);
        if (wasNew) fieldsSet++;
      });
      
      return fieldsSet;
    },
    
    hget: async (key: string, field: string) => {
      const hash = hashMaps.get(key);
      return hash?.[field] || null;
    },
    
    hgetall: async (key: string) => {
      return hashMaps.get(key) || {};
    },
  };
};

// Export shared Redis instance - either real or mock with better error handling
export const redis = UPSTASH_URL && UPSTASH_TOKEN
  ? new Redis({
      url: UPSTASH_URL,
      token: UPSTASH_TOKEN,
      retry: {
        retries: 2,
        backoff: (retryCount) => Math.min(retryCount * 100, 1000),
      },
      // Add proper timeout configuration
      automaticDeserialization: false,
      // Add request timeout
      requestTimeout: 10000, // 10 seconds
    })
  : createMockRedis();

// Type helper for Redis operations
export type RedisClient = typeof redis;

// Helper function to check if Redis is available
export const isRedisAvailable = () => Boolean(UPSTASH_URL && UPSTASH_TOKEN); 