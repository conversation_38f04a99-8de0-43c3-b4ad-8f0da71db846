import { NextRequest, NextResponse } from 'next/server';

// Vercel Cron Job for Data Retention Cleanup
// This endpoint is called by Vercel's cron scheduler
export async function GET(request: NextRequest) {
  try {
    // Verify this is a legitimate cron request from Vercel
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      console.error('Unauthorized cron request');
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized' 
      }, { status: 401 });
    }

    console.log('🧹 Starting scheduled data retention cleanup...');

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

    // Call the Supabase Edge Function
    const response = await fetch(`${supabaseUrl}/functions/v1/data-retention-cleanup`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        source: 'vercel-cron',
        timestamp: new Date().toISOString(),
        environment: process.env.VERCEL_ENV || 'development'
      })
    });

    const result = await response.json();

    if (!response.ok) {
      throw new Error(`Cleanup function failed: ${result.error || 'Unknown error'}`);
    }

    console.log('✅ Data retention cleanup completed successfully');
    console.log('🗑️ Cleanup Results:', {
      processedUsers: result.processedUsers,
      deletedReceipts: result.deletedReceipts,
      deletedItems: result.deletedItems,
      deletedFiles: result.deletedFiles,
      executionTime: result.executionTime
    });

    // Send success notification
    if (result.deletedReceipts > 0) {
      console.log(`📧 Cleanup completed: ${result.deletedReceipts} receipts deleted`);
      
      // You could send an email notification here
      // await sendCleanupNotification(result);
    }

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      source: 'vercel-cron',
      result: {
        processedUsers: result.processedUsers,
        deletedReceipts: result.deletedReceipts,
        deletedItems: result.deletedItems,
        deletedFiles: result.deletedFiles,
        executionTime: result.executionTime,
        errors: result.errors
      }
    });

  } catch (error) {
    console.error('❌ Data retention cleanup failed:', error);
    
    // Send error notification
    // await sendErrorNotification(error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      source: 'vercel-cron'
    }, { status: 500 });
  }
}

// Also support POST for manual triggers
export async function POST(request: NextRequest) {
  return GET(request);
}
