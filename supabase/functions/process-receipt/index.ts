import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient, SupabaseClient } from "https://esm.sh/@supabase/supabase-js@2.39.3";
import OpenAI from "https://esm.sh/openai@4.28.0";
import { Redis } from "https://esm.sh/@upstash/redis@1.28.4";
import { corsHeaders } from "../_shared/cors.ts";
import { QueueJob, ExtractedReceiptData, QUEUES } from "../_shared/types.ts";
import { InsertReceiptItem, UpdateReceipt } from "../_shared/database-types.ts";
import {
  validateExtractedData,
  validateInsertReceipt,
  validateInsertReceiptItems,
  sanitizeExtractedData,
  formatValidationErrors,
  ValidationResult
} from "../_shared/validation.ts";
import { PerformanceTimer, classifyError, ErrorCategory, Logger } from "../_shared/logger.ts";
import { exportReceiptToGoogleSheets } from "../_shared/google-sheets.ts";

// Initialize logger
const logger = Logger.getInstance();

// Helper function to match the logMessage calls in the code
function logMessage(level: string, message: string, jobId?: string, receiptId?: string, userId?: string, functionName?: string, ...args: any[]) {
  const context: any = {
    jobId,
    receiptId,
    userId,
    functionName
  };
  
  // Handle additional arguments
  if (args.length > 0) {
    args.forEach((arg, index) => {
      if (typeof arg === 'string') {
        context[`arg${index}`] = arg;
      } else if (typeof arg === 'object') {
        Object.assign(context, arg);
      }
    });
  }
  
  // Call appropriate logger method
  switch (level.toLowerCase()) {
    case 'debug':
      logger.debug(message, context);
      break;
    case 'info':
      logger.info(message, context);
      break;
    case 'warn':
      logger.warn(message, context);
      break;
    case 'error':
      logger.error(message, context);
      break;
    case 'fatal':
      logger.fatal(message, context);
      break;
    default:
      logger.info(message, context);
  }
}

// Environment variables validation
const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'OPENAI_API_KEY',
  'UPSTASH_REDIS_REST_URL',
  'UPSTASH_REDIS_REST_TOKEN',
  'GOOGLE_CLIENT_ID',
  'GOOGLE_CLIENT_SECRET'
];

// Optional environment variables with defaults
const DATABASE_OPERATION_LOG_LEVEL = Deno.env.get('DATABASE_OPERATION_LOG_LEVEL') || 'info';

for (const envVar of requiredEnvVars) {
  if (!Deno.env.get(envVar)) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

// Initialize clients
const supabase = createClient(
  Deno.env.get('SUPABASE_URL')!,
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!, // Use service role for full access
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false
    }
  }
);

const openai = new OpenAI({
  apiKey: Deno.env.get('OPENAI_API_KEY')!,
});

const redis = new Redis({
  url: Deno.env.get('UPSTASH_REDIS_REST_URL')!,
  token: Deno.env.get('UPSTASH_REDIS_REST_TOKEN')!,
});

// Professional-grade OpenAI prompt for accountant-quality extraction
const RECEIPT_EXTRACTION_PROMPT = `You are a professional accountant's assistant specializing in precise receipt data extraction. Accuracy is critical for financial records.

TASK: Extract structured data from this receipt image with 100% accuracy.

REQUIRED OUTPUT FORMAT (JSON only, no markdown):
{
  "vendor": "Exact business name as shown",
  "vendor_tax_id": "Tax ID/PIN/VAT if visible, null if not",
  "date": "YYYY-MM-DD format",
  "currency": "3-letter code (KES/USD/EUR)",
  "payment_method": "Cash/Card/Mobile/M-Pesa/etc",
  "items": [
    {
      "description": "Exact item name from receipt",
      "total": 0.00,
      "category": "MUST be one of the specific categories listed below",
      "quantity": 1.0,
      "unit_price": 0.00
    }
  ],
  "subtotal": 0.00,
  "tax_rate_percent": 0.0,
  "tax_amount": 0.00,
  "total_amount": 0.00,
  "paid_amount": 0.00,
  "confidence": 0.95
}

MANDATORY CATEGORY ASSIGNMENT RULES:
NEVER use "Other" category. Every item MUST be assigned to one of these specific categories:

FOOD & DINING:
- "Food & Dining" - Restaurant meals, takeout, coffee, snacks, groceries, beverages

TRANSPORTATION:
- "Fueling" - Petrol, diesel, gas station purchases, fuel cards
- "Vehicle Repair" - Car parts, vehicle maintenance, repairs, oil changes, tires
- "Public Transport" - Bus, train, taxi, ride-sharing (Uber, Bolt), matatu
- "Parking" - Parking fees, tolls, vehicle registration

BUSINESS & OFFICE:
- "Office Supplies" - Stationery, paper, pens, folders, office equipment
- "Technology" - Computers, software, electronics, phone accessories
- "Professional Services" - Legal, accounting, consulting, business licenses

UTILITIES & SERVICES:
- "Utilities" - Electricity, water, internet, phone bills
- "Banking" - Bank fees, transaction charges, loan payments
- "Insurance" - Vehicle, health, business insurance premiums

RETAIL & PERSONAL:
- "Clothing" - Clothes, shoes, accessories, uniforms
- "Personal Care" - Toiletries, cosmetics, haircuts, pharmacy items
- "Household" - Cleaning supplies, home maintenance, furniture

HEALTHCARE & MEDICAL:
- "Medical" - Doctor visits, prescriptions, medical equipment, hospital bills

ENTERTAINMENT & LEISURE:
- "Entertainment" - Movies, events, sports, hobbies, books

EDUCATION & TRAINING:
- "Education" - School fees, training courses, books, educational materials

CRITICAL EXTRACTION RULES:
1. VENDOR: Extract exact business name, preserve capitalization and punctuation
2. DATE: Parse carefully - common Kenyan formats: DD/MM/YYYY, DD-MM-YYYY, DD.MM.YYYY
3. ITEMS: Extract EVERY line item with exact descriptions, quantities, and individual totals AS SHOWN ON RECEIPT
4. AMOUNTS: Be EXTREMELY careful with decimal places - read amounts exactly as printed
5. CURRENCY: Default to KES for Kenyan receipts unless clearly specified otherwise
6. TOTALS: Find the final "TOTAL" line - this is the authoritative amount, usually at the bottom
7. CONFIDENCE: Rate your extraction confidence (0.0-1.0)
8. CATEGORIES: MANDATORY - Analyze each item description carefully and assign the most appropriate specific category. NEVER use "Other".

CATEGORY ASSIGNMENT EXAMPLES:
- Car parts, brake pads, engine oil, tires → "Vehicle Repair"
- Petrol, diesel, fuel → "Fueling"
- Uber, Bolt, taxi fare → "Public Transport"
- Parking fees, toll charges → "Parking"
- Restaurant meals, coffee, groceries → "Food & Dining"
- Pens, paper, office chairs → "Office Supplies"
- Laptops, phones, software → "Technology"
- Electricity bills, internet → "Utilities"
- Medicine, doctor visits → "Medical"
- Clothes, shoes → "Clothing"

KENYAN RECEIPT SPECIFICS:
- Look for "TOTAL", "CASH PAID", "MPESA PAY", "CHANGE" sections at the bottom
- VAT/Tax rates are typically 16% in Kenya
- Common payment methods: Cash, M-Pesa, Card
- Amounts may be formatted as "1,170.00" or "1.170.00"
- The TOTAL line is usually clearly marked and is the final amount to pay

EXTRACTION PRIORITY:
1. Find the final TOTAL amount first (usually near bottom of receipt)
2. Extract individual item amounts exactly as shown in the rightmost column
3. Ensure item amounts are reasonable (not inflated or incorrect)
4. Double-check that item quantities match what's shown (usually 1.000 PC means quantity 1)

QUALITY STANDARDS:
- If text is unclear, make best reasonable interpretation
- For missing values, use null (not empty strings or zeros)
- Preserve original item descriptions exactly
- Round amounts to 2 decimal places
- If individual items don't add up exactly to total, trust the final total amount
- Extract quantity and unit price when clearly visible

COMMON MISTAKES TO AVOID:
- Don't confuse item codes (like "610411") with prices
- Don't mistake quantity indicators (like "1.000 PC") for prices
- Look for the actual price amounts in the rightmost "Total" column
- The final TOTAL at bottom is usually the correct total amount
- Individual item totals should be reasonable (not thousands for small items)

Return ONLY the JSON object. No explanations, no markdown formatting.`;

// Enhanced fallback category assignment function
function assignFallbackCategory(description: string): string {
  const desc = description.toLowerCase();

  // Vehicle and transportation
  if (desc.includes('petrol') || desc.includes('diesel') || desc.includes('fuel') || desc.includes('gas')) {
    return 'Fueling';
  }
  if (desc.includes('brake') || desc.includes('tire') || desc.includes('oil') || desc.includes('engine') ||
      desc.includes('battery') || desc.includes('repair') || desc.includes('service') || desc.includes('parts')) {
    return 'Vehicle Repair';
  }
  if (desc.includes('uber') || desc.includes('bolt') || desc.includes('taxi') || desc.includes('matatu') ||
      desc.includes('bus') || desc.includes('train')) {
    return 'Public Transport';
  }
  if (desc.includes('parking') || desc.includes('toll')) {
    return 'Parking';
  }

  // Food and dining
  if (desc.includes('food') || desc.includes('meal') || desc.includes('coffee') || desc.includes('tea') ||
      desc.includes('lunch') || desc.includes('dinner') || desc.includes('breakfast') || desc.includes('snack') ||
      desc.includes('restaurant') || desc.includes('cafe') || desc.includes('grocery')) {
    return 'Food & Dining';
  }

  // Office and business
  if (desc.includes('pen') || desc.includes('paper') || desc.includes('folder') || desc.includes('stapler') ||
      desc.includes('office') || desc.includes('stationery')) {
    return 'Office Supplies';
  }
  if (desc.includes('computer') || desc.includes('laptop') || desc.includes('phone') || desc.includes('software') ||
      desc.includes('technology') || desc.includes('electronic')) {
    return 'Technology';
  }

  // Utilities and services
  if (desc.includes('electricity') || desc.includes('water') || desc.includes('internet') || desc.includes('phone bill')) {
    return 'Utilities';
  }
  if (desc.includes('bank') || desc.includes('fee') || desc.includes('charge')) {
    return 'Banking';
  }

  // Medical and personal care
  if (desc.includes('medicine') || desc.includes('doctor') || desc.includes('hospital') || desc.includes('medical') ||
      desc.includes('pharmacy') || desc.includes('prescription')) {
    return 'Medical';
  }
  if (desc.includes('soap') || desc.includes('shampoo') || desc.includes('toothpaste') || desc.includes('cosmetic') ||
      desc.includes('haircut') || desc.includes('beauty')) {
    return 'Personal Care';
  }

  // Clothing and retail
  if (desc.includes('shirt') || desc.includes('trouser') || desc.includes('shoe') || desc.includes('cloth') ||
      desc.includes('dress') || desc.includes('jacket')) {
    return 'Clothing';
  }

  // Default to Food & Dining for most common purchases
  return 'Food & Dining';
}

// Add performance optimization constants at the top
const PERFORMANCE_THRESHOLDS = {
  SLOW_OPERATION: 3000, // 3 seconds
  VERY_SLOW_OPERATION: 5000 // 5 seconds
};

// Enhanced helper function to parse OpenAI responses with better error handling
function parseOpenAIResponse(response: string): any {
  try {
    // First try to parse as-is
    return JSON.parse(response.trim());
  } catch (error) {
    console.log('Direct JSON parse failed, trying enhanced extraction...');

    // Try multiple patterns for code blocks
    const codeBlockPatterns = [
      /```(?:json)?\s*(\{[\s\S]*?\})\s*```/,
      /```(\{[\s\S]*?\})```/,
      /`(\{[\s\S]*?\})`/
    ];

    for (const pattern of codeBlockPatterns) {
      const match = response.match(pattern);
      if (match) {
        try {
          const cleanedJson = match[1].trim();
          console.log('Found JSON in code block, attempting parse...');
          return JSON.parse(cleanedJson);
        } catch (codeBlockError) {
          console.warn('Failed to parse JSON from code block, trying next pattern...');
          continue;
        }
      }
    }

    // Try to find JSON without code blocks with better cleaning
    const jsonStart = response.indexOf('{');
    const jsonEnd = response.lastIndexOf('}');
    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
      try {
        let jsonStr = response.substring(jsonStart, jsonEnd + 1);

        // Clean up common issues
        jsonStr = jsonStr
          .replace(/,\s*}/g, '}')     // Remove trailing commas before }
          .replace(/,\s*]/g, ']')     // Remove trailing commas before ]
          .replace(/\n/g, ' ')        // Replace newlines
          .replace(/\s+/g, ' ')       // Normalize whitespace
          .trim();

        console.log('Attempting to parse cleaned JSON...');
        return JSON.parse(jsonStr);
      } catch (innerError) {
        console.error('Failed to parse extracted JSON:', jsonStr.substring(0, 200));
      }
    }

    // Last resort: manual extraction
    console.log('Attempting manual data extraction...');
    const manualData = extractDataManually(response);
    if (manualData) {
      console.log('Manual extraction successful');
      return manualData;
    }

    console.error('All parsing methods failed. Response sample:', response.substring(0, 500));
    throw new Error('No valid JSON found in OpenAI response after trying all methods');
  }
}

// Manual extraction as fallback
function extractDataManually(response: string): any | null {
  try {
    const result: any = { items: [] };

    // Extract vendor/business name
    const vendorPatterns = [
      /(?:vendor|business|store|company|merchant)["']?\s*:\s*["']([^"']+)["']/i,
      /(?:vendor|business|store|company|merchant)["']?\s*:\s*([^,\n}]+)/i
    ];

    for (const pattern of vendorPatterns) {
      const match = response.match(pattern);
      if (match) {
        result.vendor = match[1].trim();
        break;
      }
    }

    // Extract total amount
    const totalPatterns = [
      /(?:total|amount|sum|total_amount)["']?\s*:\s*["']?([0-9.,]+)["']?/i,
      /(?:total|amount|sum)["']?\s*:\s*([0-9.,]+)/i
    ];

    for (const pattern of totalPatterns) {
      const match = response.match(pattern);
      if (match) {
        result.total_amount = parseFloat(match[1].replace(/,/g, ''));
        break;
      }
    }

    // Extract date
    const datePatterns = [
      /(?:date|receipt_date)["']?\s*:\s*["']([^"']+)["']/i,
      /(?:date|receipt_date)["']?\s*:\s*([^,\n}]+)/i
    ];

    for (const pattern of datePatterns) {
      const match = response.match(pattern);
      if (match) {
        result.date = match[1].trim();
        break;
      }
    }

    // Extract currency
    const currencyMatch = response.match(/(?:currency)["']?\s*:\s*["']([^"']+)["']/i);
    if (currencyMatch) {
      result.currency = currencyMatch[1];
    } else {
      result.currency = 'KES'; // Default for Kenya
    }

    // Only return if we found meaningful data
    if (result.vendor || result.total_amount) {
      console.log('Manual extraction found:', {
        vendor: result.vendor,
        total_amount: result.total_amount,
        date: result.date
      });
      return result;
    }

    return null;
  } catch (error) {
    console.error('Manual extraction failed:', error);
    return null;
  }
}





// Utility function for retries with exponential backoff
async function withRetry<T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000,
  operationName: string = 'operation'
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      console.log(`${operationName} - Attempt ${attempt}/${maxAttempts}`);
      return await operation();
    } catch (error) {
      lastError = error as Error;
      console.error(`${operationName} failed on attempt ${attempt}:`, error);
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      // Exponential backoff with jitter
      const delay = baseDelay * Math.pow(2, attempt - 1);
      const jitter = Math.random() * 0.2 * delay; // ±20% jitter
      const finalDelay = delay + (Math.random() > 0.5 ? jitter : -jitter);
      
      console.log(`Retrying ${operationName} in ${Math.round(finalDelay)}ms...`);
      await new Promise(resolve => setTimeout(resolve, finalDelay));
    }
  }
  
  throw lastError!;
}

// Update job status in Redis with batched operations
async function updateJobStatus(jobId: string, status: string, error?: string): Promise<void> {
  const updateData: Record<string, string> = {
    status,
    updatedAt: new Date().toISOString()
  };

  if (error) {
    updateData.error = error;
  }

  // Use pipeline for better performance if available, otherwise use hset
  await redis.hset(`job:${jobId}`, updateData);
}

// Complete a job in Redis queue with optimized operations
async function completeJob(jobId: string): Promise<void> {
  try {
    const jobKey = `job:${jobId}`;

    // Get original job data and update status in parallel
    const [originalJobData] = await Promise.all([
      redis.hget(jobKey, 'originalData'),
      updateJobStatus(jobId, 'completed')
    ]);

    // Remove from processing queue if original data exists
    if (originalJobData) {
      try {
        await redis.zrem(QUEUES.PROCESSING, originalJobData);
        console.log(`Job ${jobId} removed from processing queue`);
      } catch (removeError) {
        console.error(`Failed to remove job ${jobId} from processing queue:`, removeError);
        // Don't throw here as the job status update succeeded
      }
    }
  } catch (error) {
    console.error(`Failed to complete job ${jobId}:`, error);
    throw error; // Re-throw to ensure calling code handles it
  }
}

// Fail a job in Redis queue
async function failJob(jobId: string, error: string): Promise<void> {
  try {
    console.log(`Failing job ${jobId} with error: ${error}`);
    
    const jobKey = `job:${jobId}`;
    
    // Get current job state with error handling
    let job: any = {};
    try {
      job = await redis.hgetall(jobKey) || {};
    } catch (getError) {
      console.error(`Failed to get job ${jobId} from Redis:`, getError);
    }
    
    const attempts = parseInt((job.attempts as string) || '0') + 1;
    const maxAttempts = 3;
    
    if (attempts < maxAttempts) {
      // Retry job with exponential backoff
      try {
        await redis.hset(jobKey, {
          status: 'retrying',
          attempts: attempts.toString(),
          lastError: error,
          retryAt: new Date(Date.now() + Math.pow(2, attempts) * 1000).toISOString()
        });
        
        console.log(`Job ${jobId} scheduled for retry (attempt ${attempts}/${maxAttempts})`);
        
        // Re-queue with delay (implement in calling code)
        // For now, just log - the actual re-queuing should be handled by a separate process
        
      } catch (retryError) {
        console.error(`Failed to schedule retry for job ${jobId}:`, retryError);
        // Fall through to mark as failed
      }
    } else {
      // Max attempts reached - mark as failed
      try {
        await redis.hset(jobKey, {
          status: 'failed',
          failedAt: new Date().toISOString(),
          finalError: error,
          attempts: attempts.toString()
        });
        
        // Move to failed queue
        const originalJobData = await redis.hget(jobKey, 'originalData');
        if (originalJobData) {
          await redis.zrem(QUEUES.PROCESSING, originalJobData);
          await redis.zadd(QUEUES.FAILED, { score: Date.now(), member: originalJobData });
          console.log(`Job ${jobId} moved to failed queue after ${attempts} attempts`);
        }
        
      } catch (failError) {
        console.error(`Failed to mark job ${jobId} as failed:`, failError);
        // This is critical - we must ensure the job doesn't get stuck
        throw failError;
      }
    }
  } catch (error) {
    console.error(`Critical error in failJob for ${jobId}:`, error);
    // Always ensure job tracking is updated even if other operations fail
    try {
      await redis.hset(`job:${jobId}`, {
        status: 'failed',
        failedAt: new Date().toISOString(),
        criticalError: 'Failed to process job failure properly'
      });
    } catch (criticalError) {
      console.error(`CRITICAL: Unable to update job status for ${jobId}:`, criticalError);
    }
    throw error;
  }
}

// Cache for signed URLs to avoid repeated generation
const signedUrlCache = new Map<string, { url: string; expires: number }>();

// Generate signed URL for image access with caching
async function getSignedImageUrl(filePath: string): Promise<string> {
  // Check cache first (30-minute cache)
  const cached = signedUrlCache.get(filePath);
  if (cached && cached.expires > Date.now()) {
    console.log('Using cached signed URL for:', filePath.substring(0, 50) + '...');
    return cached.url;
  }

  return await withRetry(async () => {
    const { data, error } = await supabase.storage
      .from('receipts')
      .createSignedUrl(filePath, 3600); // 1 hour expiry for better caching

    if (error) {
      throw new Error(`Failed to generate signed URL: ${error.message}`);
    }

    if (!data.signedUrl) {
      throw new Error('No signed URL returned from Supabase');
    }

    // Cache the URL for 30 minutes
    signedUrlCache.set(filePath, {
      url: data.signedUrl,
      expires: Date.now() + 30 * 60 * 1000 // 30 minutes
    });

    // Clean up expired cache entries periodically
    if (signedUrlCache.size > 100) {
      const now = Date.now();
      for (const [key, value] of signedUrlCache.entries()) {
        if (value.expires <= now) {
          signedUrlCache.delete(key);
        }
      }
    }

    return data.signedUrl;
  }, 2, 300, 'Generate signed URL'); // Reduced retries and delay for faster processing
}

// Enhanced extraction function that handles both images and PDFs
async function extractReceiptData(fileUrl: string, fileName: string, mimeType?: string): Promise<ExtractedReceiptData> {
  const startTime = Date.now();
  
  try {
    console.log('🔍 Starting data extraction...', {
      fileName,
      mimeType,
      fileUrl: fileUrl.substring(0, 100) + '...'
    });

    // Determine file type from URL, filename, or mime type
    const fileType = determineFileType(fileUrl, fileName, mimeType);
    
    let extractedData: ExtractedReceiptData;
    
    if (fileType === 'pdf') {
      console.log('📄 Processing PDF receipt...');
      extractedData = await extractFromPDF(fileUrl);
    } else {
      console.log('🖼️ Processing image receipt...');
      extractedData = await extractFromImage(fileUrl);
    }

    const duration = Date.now() - startTime;
    console.log('✅ Data extraction completed', {
      duration,
      vendor: extractedData.vendor,
      itemCount: extractedData.items?.length || 0,
      total: extractedData.total_amount
    });

    return extractedData;

  } catch (error) {
    const duration = Date.now() - startTime;
    console.error('❌ Data extraction failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      duration,
      fileName
    });
    throw error;
  }
}

// Helper function to determine file type
function determineFileType(fileUrl: string, fileName: string, mimeType?: string): 'pdf' | 'image' {
  // Check mime type first
  if (mimeType === 'application/pdf') return 'pdf';
  if (mimeType?.startsWith('image/')) return 'image';
  
  // Check file extension
  const ext = fileName.toLowerCase().split('.').pop();
  if (ext === 'pdf') return 'pdf';
  if (['jpg', 'jpeg', 'png', 'webp', 'gif'].includes(ext || '')) return 'image';
  
  // Check URL extension
  const urlExt = fileUrl.toLowerCase().split('.').pop()?.split('?')[0];
  if (urlExt === 'pdf') return 'pdf';
  if (['jpg', 'jpeg', 'png', 'webp', 'gif'].includes(urlExt || '')) return 'image';
  
  // Default to image for backward compatibility
  return 'image';
}

// Extract data from PDF using text-based OpenAI API with fallback to Vision API
async function extractFromPDF(pdfUrl: string): Promise<ExtractedReceiptData> {
  try {
    console.log('📄 Starting PDF processing with text extraction...');

    // Step 1: Try text extraction first
    let extractedData: ExtractedReceiptData | null = null;
    let textExtractionError: Error | null = null;

    try {
      const pdfText = await Promise.race([
        extractTextFromPDF(pdfUrl),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('PDF text extraction timeout after 15 seconds')), 15000)
        )
      ]);

      if (pdfText && pdfText.trim().length > 10) {
        console.log('📝 Extracted text from PDF:', pdfText.substring(0, 200) + '...');
        console.log('📝 Total text length:', pdfText.length);

        // Use OpenAI text model for processing
        const completion = await Promise.race([
          openai.chat.completions.create({
            model: "gpt-4o", // Better model for text processing
            messages: [
              {
                role: "system",
                content: RECEIPT_EXTRACTION_PROMPT
              },
              {
                role: "user",
                content: `Extract receipt data from this PDF text:\n\n${pdfText.substring(0, 4000)}` // Increased text length
              }
            ],
            max_tokens: 1500,
            temperature: 0.1
          }),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('OpenAI text processing timeout after 25 seconds')), 25000)
          )
        ]);

        const response = completion.choices[0]?.message?.content;
        if (response) {
          console.log('🤖 OpenAI text extraction response:', response.substring(0, 200) + '...');

          try {
            const parsedData = parseOpenAIResponse(response);
            extractedData = validateAndCleanExtractedData(parsedData);
            console.log('✅ Text extraction successful');
          } catch (parseError) {
            console.warn('⚠️ Failed to parse OpenAI text response:', parseError);
            // Don't throw here, let it fall back to Vision API
            extractedData = null;
          }
        }
      } else {
        throw new Error(`Insufficient text extracted from PDF (${pdfText ? pdfText.length : 0} characters) - PDF may be image-based, encrypted, or have complex formatting`);
      }
    } catch (error) {
      textExtractionError = error instanceof Error ? error : new Error(String(error));
      console.error('❌ Text extraction failed with detailed error:', {
        error: textExtractionError.message,
        stack: textExtractionError.stack,
        type: typeof error,
        errorObject: error
      });
    }

    // Step 2: If text extraction failed or produced poor results, fall back to Vision API
    if (!extractedData || !isExtractionValid(extractedData)) {
      console.log('🔄 Falling back to Vision API for PDF processing...');
      console.log('🔍 Fallback reason:', {
        hasExtractedData: !!extractedData,
        isValid: extractedData ? isExtractionValid(extractedData) : false,
        textExtractionError: textExtractionError?.message
      });

      try {
        // Convert PDF to image for Vision API processing
        console.log('📄 Converting PDF to image for Vision API...');
        const imageUrl = await convertPdfToImage(pdfUrl);
        console.log('�️ PDF converted to image, processing with Vision API...');

        extractedData = await extractFromImage(imageUrl);
        console.log('✅ Vision API fallback successful');

        // Log the successful fallback for monitoring
        console.log('📊 Fallback extraction results:', {
          vendor: extractedData.vendor,
          amount: extractedData.total_amount,
          itemCount: extractedData.items?.length || 0,
          method: 'vision_api_fallback_with_conversion'
        });

      } catch (visionError) {
        console.error('❌ Vision API fallback also failed:', visionError);

        // Create a detailed error report for debugging
        const errorReport = {
          textExtractionError: textExtractionError?.message || 'Unknown text extraction error',
          visionApiError: visionError instanceof Error ? visionError.message : String(visionError),
          pdfUrl: pdfUrl.substring(0, 100) + '...',
          timestamp: new Date().toISOString()
        };

        console.error('📋 Complete PDF processing failure report:', errorReport);

        // If both methods failed, provide a more helpful error message
        if (errorReport.visionApiError.includes('unsupported image')) {
          throw new Error(`PDF processing failed: Text extraction unsuccessful and PDF-to-image conversion not available. Text extraction error: ${errorReport.textExtractionError}`);
        } else {
          throw new Error(`PDF processing completely failed. Text extraction: ${errorReport.textExtractionError}. Vision API: ${errorReport.visionApiError}`);
        }
      }
    } else {
      console.log('✅ Text extraction successful, no fallback needed');
    }

    return extractedData;

  } catch (error) {
    console.error('❌ Complete PDF extraction failure:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`PDF processing failed: ${errorMessage}`);
  }
}

// Helper function to validate if extraction produced meaningful results
function isExtractionValid(data: ExtractedReceiptData): boolean {
  const hasVendor = data.vendor && data.vendor !== 'Unknown Vendor' && data.vendor.trim().length > 0;
  const hasAmount = data.total_amount && parseFloat(data.total_amount.toString()) > 0;
  const hasItems = data.items && Array.isArray(data.items) && data.items.length > 0;

  // Consider extraction valid if we have at least vendor + amount, or items with meaningful content
  const hasValidItems = hasItems && data.items ? data.items.some(item =>
    item.description && item.description.length > 3 && item.total > 0
  ) : false;

  return Boolean((hasVendor && hasAmount) || hasValidItems);
}

// Convert PDF to image for Vision API processing
async function convertPdfToImage(pdfUrl: string): Promise<string> {
  try {
    console.log('🔄 PDF to image conversion requested...');

    // For now, we'll implement a simple approach that tells the user about the limitation
    // In a production environment, you would use a service like:
    // - PDF.js with canvas rendering
    // - External API service (like CloudConvert, PDFShift, etc.)
    // - Server-side PDF rendering library

    // Since OpenAI Vision API doesn't support PDFs directly, and we don't have
    // PDF rendering capabilities in this Deno environment, we'll throw a helpful error
    throw new Error('PDF-to-image conversion not implemented. Consider using image formats (PNG, JPEG, GIF, WebP) for better processing reliability.');

  } catch (error) {
    console.error('❌ PDF to image conversion failed:', error);
    throw error;
  }
}

// Extract data from image using OpenAI Vision API with comprehensive debugging
async function extractFromImage(imageUrl: string): Promise<ExtractedReceiptData> {
  try {
    console.log('🤖 Starting OpenAI Vision API extraction...');
    console.log('🔗 Image URL:', imageUrl);

    // Test image URL accessibility first
    try {
      const testResponse = await fetch(imageUrl, { method: 'HEAD' });
      console.log('📸 Image URL test:', {
        status: testResponse.status,
        contentType: testResponse.headers.get('content-type'),
        contentLength: testResponse.headers.get('content-length')
      });

      if (!testResponse.ok) {
        throw new Error(`Image URL not accessible: ${testResponse.status} ${testResponse.statusText}`);
      }
    } catch (urlError) {
      console.error('❌ Image URL accessibility test failed:', urlError);
      const errorMessage = urlError instanceof Error ? urlError.message : String(urlError);
      throw new Error(`Cannot access image URL: ${errorMessage}`);
    }

    // Use gpt-4-vision-preview for maximum accuracy (accountant-grade quality)
    console.log('🧠 Calling OpenAI with gpt-4-vision-preview for maximum accuracy...');

    const completion = await Promise.race([
      openai.chat.completions.create({
        model: "gpt-4o", // Latest model for best vision accuracy
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: RECEIPT_EXTRACTION_PROMPT },
              {
                type: "image_url",
                image_url: {
                  url: imageUrl,
                  detail: "high" // Maximum detail for best accuracy
                }
              }
            ]
          }
        ],
        max_tokens: 1500, // Optimized for faster response
        temperature: 0.1 // Low temperature for consistency with faster processing
      }),
      // 30-second timeout for faster processing
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('OpenAI API timeout after 30 seconds')), 30000)
      )
    ]);

    console.log('✅ OpenAI API call successful');
    console.log('📊 Usage stats:', {
      promptTokens: completion.usage?.prompt_tokens,
      completionTokens: completion.usage?.completion_tokens,
      totalTokens: completion.usage?.total_tokens
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response content from OpenAI Vision API');
    }

    console.log('🤖 Raw OpenAI response length:', response.length);
    console.log('🤖 Raw OpenAI response (first 500 chars):', response.substring(0, 500));
    console.log('🤖 Raw OpenAI response (last 200 chars):', response.substring(Math.max(0, response.length - 200)));

    // Parse and validate the JSON response with detailed error handling
    let extractedData;
    try {
      extractedData = parseOpenAIResponse(response);
      console.log('✅ JSON parsing successful');
      console.log('📝 Parsed data structure:', {
        hasVendor: !!extractedData.vendor,
        hasDate: !!extractedData.date,
        hasTotal: !!extractedData.total_amount,
        itemCount: extractedData.items?.length || 0,
        confidence: extractedData.confidence || 'not provided'
      });
    } catch (parseError) {
      console.error('❌ JSON parsing failed:', parseError);
      console.error('❌ Failed to parse response:', response);
      const errorMessage = parseError instanceof Error ? parseError.message : String(parseError);
      throw new Error(`Failed to parse OpenAI response as JSON: ${errorMessage}`);
    }

    console.log('📝 Complete parsed data:', JSON.stringify(extractedData, null, 2));

    return validateAndCleanExtractedData(extractedData);

  } catch (error) {
    console.error('❌ Image extraction error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorName = error instanceof Error ? error.name : 'UnknownError';
    const errorStack = error instanceof Error ? error.stack : 'No stack trace';

    console.error('❌ Error details:', {
      name: errorName,
      message: errorMessage,
      stack: errorStack
    });
    throw new Error(`Image processing failed: ${errorMessage}`);
  }
}

// Extract text from PDF using enhanced text extraction with detailed logging
async function extractTextFromPDF(pdfUrl: string): Promise<string> {
  try {
    console.log('📥 Downloading PDF from URL...', pdfUrl.substring(0, 100) + '...');

    // Download the PDF file with timeout
    const downloadResponse = await Promise.race([
      fetch(pdfUrl),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('PDF download timeout after 10 seconds')), 10000)
      )
    ]);

    if (!downloadResponse.ok) {
      throw new Error(`Failed to download PDF: ${downloadResponse.status} ${downloadResponse.statusText}`);
    }

    const pdfBuffer = await downloadResponse.arrayBuffer();
    const pdfBytes = new Uint8Array(pdfBuffer);

    console.log('📄 PDF downloaded successfully:', {
      size: pdfBytes.length,
      sizeKB: Math.round(pdfBytes.length / 1024),
      sizeMB: Math.round(pdfBytes.length / 1024 / 1024 * 100) / 100
    });

    // Validate PDF size (5MB limit)
    const maxSizeBytes = 5 * 1024 * 1024; // 5MB
    if (pdfBytes.length > maxSizeBytes) {
      throw new Error(`PDF file too large: ${(pdfBytes.length / 1024 / 1024).toFixed(2)}MB. Maximum allowed: 5MB`);
    }

    // Validate PDF header
    const pdfHeader = new TextDecoder('ascii').decode(pdfBytes.slice(0, 8));
    if (!pdfHeader.startsWith('%PDF-')) {
      throw new Error(`Invalid PDF file: Header is "${pdfHeader}", expected "%PDF-"`);
    }

    console.log('📄 PDF validation passed, header:', pdfHeader);

    // Enhanced PDF text extraction with multiple strategies
    const text = await simplePDFTextExtraction(pdfBytes);

    if (!text || text.trim().length === 0) {
      throw new Error('No readable text found in PDF - the PDF might be image-based, encrypted, or use unsupported text encoding');
    }

    if (text.trim().length < 20) {
      console.warn(`⚠️ Very little text extracted (${text.trim().length} characters):`, text);
      throw new Error(`Minimal text extracted from PDF (${text.trim().length} characters) - likely an image-based or scanned PDF`);
    }

    console.log('📝 Text extraction completed:', {
      textLength: text.length,
      wordCount: text.split(/\s+/).length,
      hasNumbers: /\d/.test(text),
      hasLetters: /[a-zA-Z]/.test(text),
      sample: text.substring(0, 100) + '...'
    });

    return text;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('❌ PDF text extraction error:', {
      error: errorMessage,
      url: pdfUrl.substring(0, 100) + '...'
    });
    throw error;
  }
}

// Enhanced PDF text extraction with multiple strategies
async function simplePDFTextExtraction(pdfBytes: Uint8Array): Promise<string> {
  try {
    console.log('🔍 Starting enhanced PDF text extraction...');

    // Strategy 1: Enhanced text pattern extraction
    let extractedText = await extractTextWithEnhancedPatterns(pdfBytes);
    console.log(`📝 Strategy 1 result: ${extractedText.length} characters`);

    // Strategy 2: If first strategy fails, try stream-based extraction
    if (!extractedText || extractedText.trim().length < 10) {
      console.log('📝 First strategy failed, trying stream-based extraction...');
      extractedText = await extractTextFromStreams(pdfBytes);
      console.log(`📝 Strategy 2 result: ${extractedText.length} characters`);
    }

    // Strategy 3: If both fail, try raw text search
    if (!extractedText || extractedText.trim().length < 10) {
      console.log('📝 Stream extraction failed, trying raw text search...');
      extractedText = await extractRawTextFromPDF(pdfBytes);
      console.log(`📝 Strategy 3 result: ${extractedText.length} characters`);
    }

    console.log('📝 Final extracted text length:', extractedText.length);
    console.log('📝 Sample text:', extractedText.substring(0, 300));

    return extractedText;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Enhanced PDF extraction error:', {
      error: errorMessage,
      strategies: ['enhanced_patterns', 'stream_based', 'raw_text']
    });
    throw new Error(`Failed to extract text from PDF using all strategies: ${errorMessage}`);
  }
}

// Strategy 1: Enhanced pattern-based text extraction
async function extractTextWithEnhancedPatterns(pdfBytes: Uint8Array): Promise<string> {
  try {
    const pdfString = new TextDecoder('latin1').decode(pdfBytes);
    let extractedText = '';

    // Multiple text extraction patterns for different PDF encodings
    const patterns = [
      // Standard text in parentheses
      /\(([^)]+)\)/g,
      // Hex-encoded text
      /<([0-9A-Fa-f\s]+)>/g,
      // Text between BT and ET operators
      /BT\s+(.*?)\s+ET/g,
      // Text with Tj operator
      /\((.*?)\)\s*Tj/g,
      // Text with TJ operator (array format)
      /\[(.*?)\]\s*TJ/g,
      // Text with show operators
      /\((.*?)\)\s*(?:Tj|'|")/g
    ];

    patterns.forEach((pattern, index) => {
      const matches = pdfString.match(pattern) || [];
      console.log(`📝 Pattern ${index + 1} found ${matches.length} matches`);

      matches.forEach(match => {
        let text = '';

        if (pattern.source.includes('\\(')) {
          // Extract from parentheses
          text = match.slice(1, -1);
        } else if (pattern.source.includes('<')) {
          // Extract hex-encoded text
          const hexContent = match.slice(1, -1).replace(/\s/g, '');
          if (hexContent.length % 2 === 0) {
            try {
              text = hexContent.match(/.{2}/g)
                ?.map(hex => String.fromCharCode(parseInt(hex, 16)))
                .join('') || '';
            } catch (e) {
              // Skip invalid hex
            }
          }
        } else {
          // Extract from other patterns
          const textMatch = match.match(/\(([^)]+)\)/);
          if (textMatch) {
            text = textMatch[1];
          }
        }

        // Filter and clean text
        if (text && text.length > 0 && /[a-zA-Z0-9]/.test(text)) {
          // Decode common PDF escape sequences
          text = text
            .replace(/\\n/g, ' ')
            .replace(/\\r/g, ' ')
            .replace(/\\t/g, ' ')
            .replace(/\\b/g, ' ')
            .replace(/\\f/g, ' ')
            .replace(/\\\(/g, '(')
            .replace(/\\\)/g, ')')
            .replace(/\\\\/g, '\\');

          extractedText += text + ' ';
        }
      });
    });

    // Clean up the extracted text
    extractedText = extractedText
      .replace(/\s+/g, ' ')
      .replace(/[^\x20-\x7E]/g, ' ')
      .trim();

    console.log(`📝 Enhanced pattern extraction completed: ${extractedText.length} characters`);
    return extractedText;
  } catch (error) {
    console.error('Enhanced pattern extraction error:', error);
    console.log('📝 Enhanced pattern extraction failed, returning empty string');
    return '';
  }
}

// Strategy 2: Stream-based text extraction
async function extractTextFromStreams(pdfBytes: Uint8Array): Promise<string> {
  try {
    const pdfString = new TextDecoder('latin1').decode(pdfBytes);
    let extractedText = '';

    // Look for stream objects that might contain text
    const streamPattern = /stream\s*(.*?)\s*endstream/g;
    const streams = pdfString.match(streamPattern) || [];

    console.log(`📝 Found ${streams.length} streams in PDF`);

    streams.forEach((stream, index) => {
      try {
        // Extract content between stream markers
        const content = stream.replace(/^stream\s*/, '').replace(/\s*endstream$/, '');

        // Try to find readable text in the stream
        const textMatches = content.match(/[a-zA-Z0-9\s.,!?@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]+/g) || [];

        textMatches.forEach(match => {
          if (match.length > 2 && /[a-zA-Z]/.test(match)) {
            extractedText += match + ' ';
          }
        });
      } catch (e) {
        console.warn(`📝 Error processing stream ${index}:`, e);
      }
    });

    console.log(`📝 Stream extraction completed: ${extractedText.length} characters`);
    return extractedText.trim();
  } catch (error) {
    console.error('Stream extraction error:', error);
    console.log('📝 Stream extraction failed, returning empty string');
    return '';
  }
}

// Strategy 3: Raw text search as last resort
async function extractRawTextFromPDF(pdfBytes: Uint8Array): Promise<string> {
  try {
    // Try different encodings
    const encodings = ['utf-8', 'latin1', 'ascii'];
    let bestText = '';
    let maxReadableChars = 0;

    for (const encoding of encodings) {
      try {
        const decoded = new TextDecoder(encoding).decode(pdfBytes);

        // Extract sequences of readable characters
        const readableText = decoded.match(/[a-zA-Z0-9\s.,!?@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?`~]{3,}/g) || [];
        const joinedText = readableText.join(' ');

        // Count readable characters
        const readableChars = (joinedText.match(/[a-zA-Z0-9]/g) || []).length;

        if (readableChars > maxReadableChars) {
          maxReadableChars = readableChars;
          bestText = joinedText;
        }
      } catch (e) {
        // Skip encoding if it fails
      }
    }

    console.log(`📝 Raw extraction found ${maxReadableChars} readable characters`);
    console.log(`📝 Raw extraction completed: ${bestText.length} characters`);
    return bestText.trim();
  } catch (error) {
    console.error('Raw text extraction error:', error);
    console.log('📝 Raw text extraction failed, returning empty string');
    return '';
  }
}

// Validate and clean extracted data with accountant-grade standards
function validateAndCleanExtractedData(data: any): ExtractedReceiptData {
  console.log('🔍 Starting validation of extracted data...');
  console.log('📊 Raw extracted data:', JSON.stringify(data, null, 2));

  // Quality assessment with detailed logging
  const hasVendor = data.vendor && data.vendor !== 'Unknown Vendor' && data.vendor.trim().length > 0;
  const hasAmount = data.total_amount && parseFloat(data.total_amount) > 0;
  const hasDate = data.date && data.date !== new Date().toISOString().split('T')[0];
  const hasItems = data.items && Array.isArray(data.items) && data.items.length > 0;
  const confidence = data.confidence || 0.5;

  console.log('🔍 Quality assessment:', {
    hasVendor,
    hasAmount,
    hasDate,
    hasItems,
    confidence,
    vendorValue: data.vendor,
    amountValue: data.total_amount,
    dateValue: data.date,
    itemCount: data.items?.length || 0
  });

  // Improved validation - be more flexible with extraction requirements
  if (!hasAmount && !hasItems) {
    console.error('❌ Critical validation failure: No financial data extracted');
    console.error('❌ Validation details:', {
      vendor: data.vendor,
      amount: data.total_amount,
      items: data.items,
      hasVendor,
      hasAmount,
      hasItems
    });

    // If we have a vendor but no amount/items, create a minimal valid receipt
    if (hasVendor) {
      console.log('🔧 Creating minimal receipt with vendor information only');
      // Don't throw error, let it proceed with minimal data
    } else {
      throw new Error('Extraction failed: No amount, items, or vendor information could be extracted from the receipt');
    }
  }

  // Warn but don't fail for missing vendor (some receipts don't have clear vendor names)
  if (!hasVendor) {
    console.warn('⚠️ Warning: No vendor name extracted, using fallback');
  }

  const validated: ExtractedReceiptData = {
    vendor: data.vendor || 'Unknown Vendor',
    vendor_tax_id: data.vendor_tax_id || null,
    date: data.date || new Date().toISOString().split('T')[0],
    currency: data.currency || 'KES',
    payment_method: data.payment_method || 'Unknown',
    subtotal: parseFloat(data.subtotal) || 0,
    tax_rate_percent: parseFloat(data.tax_rate_percent) || 0,
    tax_amount: parseFloat(data.tax_amount) || 0,
    total_amount: parseFloat(data.total_amount) || 0,
    paid_amount: parseFloat(data.paid_amount) || parseFloat(data.total_amount) || 0,
    items: []
  };

  // Process and validate items with accountant-grade precision
  console.log('📋 Processing items...');
  if (data.items && Array.isArray(data.items)) {
    console.log(`📋 Found ${data.items.length} items in extraction`);

    validated.items = data.items
      .map((item: any, index: number) => {
        const itemTotal = parseFloat(item.total) || 0;
        let category = item.category || 'General';

        // Enhanced fallback category assignment if AI didn't assign properly
        if (!category || category === 'Other' || category === 'General') {
          category = assignFallbackCategory(item.description || '');
        }

        const processedItem = {
          description: item.description || `Item ${index + 1}`,
          total: Math.round(itemTotal * 100) / 100, // Round to 2 decimal places
          category: category
        };

        console.log(`📋 Item ${index + 1}:`, processedItem);
        return processedItem;
      })
      .filter((item: any) => {
        // Keep items that have either a meaningful description or a positive total
        const hasDescription = item.description && item.description !== 'Item' && item.description.trim().length > 0;
        const hasAmount = item.total > 0;
        return hasDescription || hasAmount;
      });

    console.log(`📋 Processed ${validated.items.length} valid items`);
  } else {
    console.log('📋 No items array found in extraction');
    validated.items = [];
  }

  // Create default item if no items were extracted but we have a total
  if (validated.items && validated.items.length === 0 && validated.total_amount > 0) {
    console.log('📋 Creating default item for receipt total');
    // Try to assign category based on vendor name
    const vendorCategory = assignFallbackCategory(validated.vendor || '');
    validated.items = [{
      description: 'Receipt Total',
      total: validated.total_amount,
      category: vendorCategory
    }];
  }

  // Accountant-grade total reconciliation with auto-correction
  if (validated.items && validated.items.length > 0) {
    const itemsTotal = validated.items.reduce((sum, item) => sum + item.total, 0);
    const roundedItemsTotal = Math.round(itemsTotal * 100) / 100;
    const roundedReceiptTotal = Math.round(validated.total_amount * 100) / 100;
    const totalDifference = Math.abs(roundedItemsTotal - roundedReceiptTotal);

    console.log('💰 Financial reconciliation:', {
      itemsTotal: roundedItemsTotal,
      receiptTotal: roundedReceiptTotal,
      difference: totalDifference,
      percentageDiff: roundedReceiptTotal > 0 ? (totalDifference / roundedReceiptTotal * 100).toFixed(2) + '%' : '0%'
    });

    // Auto-correction for major extraction errors
    if (roundedReceiptTotal > 0) {
      const percentageDiff = totalDifference / roundedReceiptTotal;

      // If items total is way off (>50% difference), likely extraction error
      if (percentageDiff > 0.50) {
        console.warn(`🔧 Major extraction error detected. Auto-correcting items to match receipt total.`);

        // Distribute receipt total evenly across items as fallback
        const itemCount = validated.items.length;
        const averageItemTotal = roundedReceiptTotal / itemCount;

        validated.items = validated.items.map((item) => ({
          ...item,
          total: Math.round(averageItemTotal * 100) / 100
        }));

        console.log(`🔧 Auto-corrected ${itemCount} items to average ${averageItemTotal} each`);
      }
      // Auto-adjust for smaller differences (< 25%)
      else if (percentageDiff > 0.05 && percentageDiff < 0.25 && validated.items.length > 0) {
        const adjustment = roundedReceiptTotal - roundedItemsTotal;
        validated.items[0].total = Math.round((validated.items[0].total + adjustment) * 100) / 100;
        console.log(`🔧 Auto-adjusted first item by ${adjustment} to match receipt total`);
      }
      else if (percentageDiff <= 0.05) {
        console.log('✅ Items total matches receipt total within acceptable range');
      }
    }
  }

  console.log('✅ Validated data:', JSON.stringify(validated, null, 2));
  return validated;
}

// Calculate confidence score based on data completeness
function calculateConfidenceScore(data: ExtractedReceiptData): number {
  let score = 0;
  const weights = {
    vendor: 20,
    date: 15,
    total_amount: 25,
    items: 30,
    vendor_tax_id: 10
  };
  
  if (data.vendor) score += weights.vendor;
  if (data.date) score += weights.date;
  if (data.total_amount > 0) score += weights.total_amount;
  if (data.items && data.items.length > 0) score += weights.items;
  if (data.vendor_tax_id) score += weights.vendor_tax_id;
  
  return score / 100;
}

// Save extracted data to the database with validation
async function saveExtractedData(
  receiptId: string, 
  userId: string, 
  extractedData: ExtractedReceiptData, 
  jobId: string,
  originalFileName: string,
  filePath: string,
  fileSize?: number,
  mimeType?: string
): Promise<void> {
  console.log(`Saving extracted data for receipt ${receiptId}`);
  
  // Step 1: Sanitize and validate extracted data
  const sanitizedData = sanitizeExtractedData(extractedData);
  
  // Fix missing total fields in items
  if (sanitizedData.items && Array.isArray(sanitizedData.items)) {
    sanitizedData.items = sanitizedData.items.map((item: any) => {
      // If item has no total but has unit_price and quantity, calculate it
      if ((!item.total || item.total === 0) && item.unit_price && item.quantity) {
        item.total = item.unit_price * item.quantity;
      }
      // If still no total, distribute receipt total evenly across items
      else if (!item.total || item.total === 0) {
        if (sanitizedData.total_amount && sanitizedData.items.length > 0) {
          item.total = sanitizedData.total_amount / sanitizedData.items.length;
        } else {
          item.total = 0; // Default fallback
        }
      }
      return item;
    });
  }
  
  const extractedValidation = validateExtractedData(sanitizedData);

  if (!extractedValidation.success) {
    const errorMsg = `Extracted data validation failed: ${formatValidationErrors(extractedValidation.errors!)}`;
    console.error(errorMsg);
    console.error('Sanitized data:', JSON.stringify(sanitizedData, null, 2));
    throw new Error(errorMsg);
  }

  console.log('Extracted data validation passed');

  // Use the validated data
  const validatedData = extractedValidation.data!;

  // Calculate confidence score
  const confidenceScore = calculateConfidenceScore(validatedData);
  
  // Step 2: Prepare all database operations for batch execution
  console.log(`Preparing batch database operations for receipt ${receiptId}...`);

  const updateData = {
    vendor: validatedData.vendor,
    vendor_tax_id: validatedData.vendor_tax_id,
    receipt_date: validatedData.date,
    currency: validatedData.currency || 'KES',
    payment_method: validatedData.payment_method,
    subtotal: validatedData.subtotal,
    tax_rate_percent: validatedData.tax_rate_percent,
    tax_amount: validatedData.tax_amount,
    total_amount: validatedData.total_amount,
    paid_amount: validatedData.paid_amount,
    processing_status: 'completed',
    confidence_score: confidenceScore,
    extraction_method: 'openai',
    redis_job_id: jobId,
    error_message: null // Clear any previous error messages
  };

  // Prepare receipt items for batch insert
  let receiptItems: InsertReceiptItem[] = [];
  if (validatedData.items && validatedData.items.length > 0) {
    receiptItems = validatedData.items.map((item: any) => ({
      receipt_id: receiptId, // Use receiptId directly
      description: item.description,
      quantity: item.quantity || 1,
      unit_price: item.unit_price,
      total_price: item.total,
      category: item.category
    }));

    // Validate receipt items before insertion
    const itemsValidation = validateInsertReceiptItems(receiptItems);
    if (!itemsValidation.success) {
      const errorMsg = `Receipt items validation failed: ${formatValidationErrors(itemsValidation.errors!)}`;
      console.error(errorMsg);
      throw new Error(errorMsg);
    }
    receiptItems = itemsValidation.data || [];
    console.log('Receipt items validation passed');
  }

  // Step 3: Execute database operations in parallel for better performance
  const [receiptResult, itemsResult] = await Promise.allSettled([
    // Update receipt
    supabase
      .from('receipts')
      .update(updateData)
      .eq('id', receiptId)
      .select()
      .single(),

    // Insert items (if any)
    receiptItems.length > 0
      ? supabase.from('receipt_items').insert(receiptItems)
      : Promise.resolve({ data: null, error: null })
  ]);

  // Check receipt update result
  if (receiptResult.status === 'rejected') {
    console.error('Failed to update receipt:', receiptResult.reason);
    throw new Error(`Failed to update receipt: ${receiptResult.reason}`);
  }

  const { data: receipt, error: receiptError } = receiptResult.value;
  if (receiptError) {
    console.error('Failed to update receipt:', receiptError);
    throw new Error(`Failed to update receipt: ${receiptError.message}`);
  }

  if (!receipt) {
    throw new Error(`Receipt ${receiptId} not found for update`);
  }

  console.log(`Receipt ${receipt.id} successfully updated with extracted data`);

  // Check items insert result
  if (receiptItems.length > 0) {
    if (itemsResult.status === 'rejected') {
      console.error('Failed to save receipt items:', itemsResult.reason);
      throw new Error(`Failed to save receipt items: ${itemsResult.reason}`);
    }

    const { error: itemsError } = itemsResult.value;
    if (itemsError) {
      throw new Error(`Failed to save receipt items: ${itemsError.message}`);
    }

    console.log(`Saved ${receiptItems.length} receipt items`);
  }

  // Increment user's receipt usage count for successful processing
  try {
    // First get current values
    const { data: userData, error: fetchError } = await supabase
      .from('users')
      .select('receipts_used_this_period, receipts_processed')
      .eq('id', userId)
      .single();

    if (!fetchError && userData) {
      const { error: updateError } = await supabase
        .from('users')
        .update({
          receipts_used_this_period: (userData.receipts_used_this_period || 0) + 1,
          receipts_processed: (userData.receipts_processed || 0) + 1,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) {
        console.error('Failed to increment receipt usage:', updateError);
      } else {
        console.log('Successfully incremented receipt usage for user:', userId);
      }
    } else {
      console.error('Failed to fetch user data for usage increment:', fetchError);
    }
  } catch (error) {
    console.error('Error incrementing receipt usage:', error);
    // Don't throw here as the receipt was processed successfully
  }

  console.log('Database save completed successfully with full validation');
}

// Update receipt processing status
async function updateReceiptStatus(
  receiptId: string, 
  status: 'pending' | 'processing' | 'completed' | 'failed',
  errorMessage?: string
): Promise<void> {
  const updateData: UpdateReceipt = {
    processing_status: status
  };
  
  if (errorMessage) {
    updateData.error_message = errorMessage;
  }
  
  const { error } = await supabase
    .from('receipts')
    .update(updateData)
    .eq('id', receiptId);
  
  if (error) {
    console.error(`Failed to update receipt status: ${error.message}`);
    // Don't throw here to avoid cascading failures
  }
}

// Main job processing function
async function processJob(job: QueueJob): Promise<{ success: boolean; message: string }> {
  const startTime = Date.now();
  
  logMessage('DEBUG', 'Job Processing started', job.id, job.receiptId, job.userId, 'timer_start');
  logMessage('INFO', 'Job processing started', job.id, job.receiptId, job.userId, 'processJob', 'job_start');
  
  try {
    // Update job status to processing
    logMessage('INFO', 'Updating job status to processing', job.id, job.receiptId, job.userId, 'processJob');
    await updateJobStatus(job.id, 'processing');
    
    // **Handle test/debug jobs differently**
    if (job.receiptId.startsWith('test-receipt-') || job.id.startsWith('debug-test-')) {
      logMessage('INFO', 'Processing test/debug job - using mock data', job.id, job.receiptId, job.userId, 'processJob', 'test_job');
      
      // Return mock success for test/debug jobs without database operations
      await completeJob(job.id);
      
      const duration = Date.now() - startTime;
      logMessage('INFO', `Test/debug job completed in ${duration}ms`, job.id, job.receiptId, job.userId, 'processJob', 'test_job_completed', 'performance');
      
      return {
        success: true,
        message: `Test/debug job ${job.id} processed successfully`
      };
    }
    
    // **Real job processing starts here**
    
    // Step 1: Update receipt status to processing (only for real receipts)
    logMessage('INFO', 'Updating receipt status to processing', job.id, job.receiptId, job.userId, 'processJob');
    
    const { error: statusError } = await supabase
      .from('receipts')
      .update({ 
        processing_status: 'processing',
        redis_job_id: job.id 
      })
      .eq('id', job.receiptId);
      
    if (statusError) {
      logMessage('ERROR', `Failed to update receipt status: ${statusError.message}`, job.id, job.receiptId, job.userId, 'processJob');
      throw new Error(`Database update failed: ${statusError.message}`);
    }
    
    logMessage(DATABASE_OPERATION_LOG_LEVEL, 'Database update on receipts succeeded', job.id, job.receiptId, job.userId, 'processJob', 'status_update_processing', 'receipts', 'database_operation');
    
    // Step 2: Start parallel operations for better performance
    logMessage('INFO', 'Starting parallel operations for optimal performance', job.id, job.receiptId, job.userId, 'processJob', 'parallel_start');

    // Start signed URL generation and AI extraction in parallel
    const urlTimer = new PerformanceTimer('Generate Signed URL', {
      filePath: job.data.imageUrl
    });

    const extractionTimer = new PerformanceTimer('AI Data Extraction');

    // Execute URL generation and AI extraction in parallel
    const [signedUrl, extractedData] = await Promise.all([
      // Generate signed URL from storage path
      (async () => {
        logMessage('INFO', 'Generating signed URL for receipt image', job.id, job.receiptId, job.userId, 'processJob', 'generate_signed_url');
        const url = await getSignedImageUrl(job.data.imageUrl);
        urlTimer.end();
        return url;
      })(),

      // Start AI extraction immediately after URL is ready
      (async () => {
        logMessage('INFO', 'Starting AI data extraction', job.id, job.receiptId, job.userId, 'processJob', 'ai_data_extraction');
        const url = await getSignedImageUrl(job.data.imageUrl); // Get URL for AI processing
        const data = await extractReceiptData(url, job.data.fileName, job.data.mimeType);
        const extractionDuration = extractionTimer.end();
        logMessage('INFO', 'AI extraction completed', job.id, job.receiptId, job.userId, 'processJob', 'ai_data_extraction_completed', 'performance');
        return data;
      })()
    ]);

    // Step 3: Save extracted data to database
    const saveTimer = new PerformanceTimer('Database Save Operation');
    logMessage('INFO', 'Starting database save operation', job.id, job.receiptId, job.userId, 'processJob', 'database_save_operation');

    await saveExtractedData(
      job.receiptId,
      job.userId,
      extractedData,
      job.id,
      job.data.fileName,
      job.data.imageUrl,
      job.data.fileSize,
      job.data.mimeType
    );

    const saveDuration = saveTimer.end();
    
    // Step 5: Export to Google Sheets (using shared implementation with token refresh)
    logMessage('INFO', 'Starting Google Sheets export', job.id, job.receiptId, job.userId, 'processJob', 'google_sheets_export');

    try {
      const exportResult = await exportReceiptToGoogleSheets(
        job.userId,
        job.receiptId,
        extractedData,
        job.data.fileName || ''
      );

      if (exportResult.success && exportResult.rowNumber) {
        // Update receipt with Google Sheets row number
        const { error: updateError } = await supabase
          .from('receipts')
          .update({
            google_sheet_row_number: exportResult.rowNumber,
            error_message: null // Clear any previous error message
          })
          .eq('id', job.receiptId);

        if (updateError) {
          logMessage('ERROR', `Failed to update receipt with Google Sheets row number: ${updateError.message}`, job.id, job.receiptId, job.userId, 'processJob', 'google_sheets_update_failed');
        } else {
          logMessage('INFO', `Google Sheets export successful - row ${exportResult.rowNumber}`, job.id, job.receiptId, job.userId, 'processJob', 'google_sheets_export_successful');
        }
      } else {
        // Update receipt with error message
        const errorMsg = exportResult.error || 'Unknown export error';
        await supabase
          .from('receipts')
          .update({
            error_message: `Google Sheets export failed: ${errorMsg}`
          })
          .eq('id', job.receiptId);

        logMessage('WARN', `Google Sheets export failed: ${errorMsg}`, job.id, job.receiptId, job.userId, 'processJob', 'google_sheets_export_failed');
      }
    } catch (exportError) {
      const errorMsg = exportError instanceof Error ? exportError.message : 'Unknown error';

      // Update receipt with error message
      await supabase
        .from('receipts')
        .update({
          error_message: `Google Sheets export error: ${errorMsg}`
        })
        .eq('id', job.receiptId);

      logMessage('WARN', `Google Sheets export failed: ${errorMsg}`, job.id, job.receiptId, job.userId, 'processJob', 'google_sheets_export_failed');

      // Don't fail the entire job for Google Sheets export issues
      // Just log the error and continue
    }

    // Calculate confidence score for final logging
    const confidenceScore = calculateConfidenceScore(extractedData);

    // Step 6: Mark job as completed
    await completeJob(job.id);

    const totalDuration = Date.now() - startTime;
    logMessage('INFO', `Job completed successfully - Total: ${totalDuration}ms, Confidence: ${confidenceScore}%`, job.id, job.receiptId, job.userId, 'processJob', 'job_completed');
    
    return {
      success: true,
      message: `Job ${job.id} processed successfully`
    };
    
  } catch (error) {
    const classifiedError = classifyError(error);
    
    logMessage('ERROR', 'Job failed', job.id, job.receiptId, job.userId, 'processJob', 'job_failed', 'performance');
    
    // Update receipt status to failed with error details
    try {
      await updateReceiptStatus(job.receiptId, 'failed', classifiedError.message);
      logMessage(DATABASE_OPERATION_LOG_LEVEL, 'Database update on receipts succeeded', job.id, job.receiptId, job.userId, 'processJob', 'status_update_failed', 'receipts', 'database_operation');
    } catch (statusError) {
      const errorMessage = statusError instanceof Error ? statusError.message : 'Unknown error';
      logMessage('ERROR', `Failed to update receipt status: ${errorMessage}`, job.id, job.receiptId, job.userId, 'processJob');
    }
    
    // Mark job as failed with enhanced error information
    try {
      await failJob(job.id, classifiedError.message);
    } catch (failError) {
      logMessage('FATAL', 'Failed to mark job as failed', job.id, job.receiptId, job.userId, 'processJob', 'job_failed', 'performance');
    }
    
    // Don't re-throw the error to prevent Edge Function crashes
    const duration = Date.now() - startTime;
    logMessage('INFO', `Job failed in ${duration}ms`, job.id, job.receiptId, job.userId, 'processJob', 'job_failed', 'performance');
    
    return {
      success: false,
      message: classifiedError.message || 'Internal server error'
    };
  }
}

// Main Edge Function handler
serve(async (req: Request) => {
  console.log(`${req.method} ${req.url}`);
  
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  
  try {
    const { job } = await req.json();
    
    if (!job) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Missing job data in request body' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }
    
    // Validate job structure
    if (!job.id || !job.receiptId || !job.userId || !job.data?.imageUrl) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid job structure' 
        }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }
    
    // Process the job
    const result = await processJob(job);
    
    return new Response(
      JSON.stringify(result),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
    
  } catch (error) {
    console.error('Edge Function error:', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
}); 