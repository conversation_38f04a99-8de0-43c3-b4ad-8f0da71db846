import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { 
  getGmailProcessingStats, 
  getRecentGmailProcessingLogs, 
  getUserGmailProcessingStatus,
  checkGmailProcessingHealth 
} from '@/lib/gmail/monitoring';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Check if user has Business tier access (for their own data) or admin access (for all data)
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status')
      .eq('id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'stats';
    const userId = searchParams.get('userId');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Parse dates if provided
    let dateFromObj: Date | undefined;
    let dateToObj: Date | undefined;

    if (dateFrom) {
      dateFromObj = new Date(dateFrom);
      if (isNaN(dateFromObj.getTime())) {
        return NextResponse.json({ error: 'Invalid dateFrom format' }, { status: 400 });
      }
    }

    if (dateTo) {
      dateToObj = new Date(dateTo);
      if (isNaN(dateToObj.getTime())) {
        return NextResponse.json({ error: 'Invalid dateTo format' }, { status: 400 });
      }
    }

    switch (action) {
      case 'stats':
        // Get overall Gmail processing statistics
        const stats = await getGmailProcessingStats(dateFromObj, dateToObj);
        return NextResponse.json({
          success: true,
          data: stats
        });

      case 'logs':
        // Get recent processing logs
        const limit = parseInt(searchParams.get('limit') || '50');
        const logs = await getRecentGmailProcessingLogs(limit);
        return NextResponse.json({
          success: true,
          data: logs
        });

      case 'user-status':
        // Get processing status for specific user (only their own data unless admin)
        const targetUserId = userId || user.id;
        
        if (targetUserId !== user.id && userData.current_tier !== 'admin') {
          return NextResponse.json({ error: 'Access denied' }, { status: 403 });
        }

        const userStatus = await getUserGmailProcessingStatus(targetUserId);
        return NextResponse.json({
          success: true,
          data: userStatus
        });

      case 'health':
        // Check Gmail processing health
        const health = await checkGmailProcessingHealth();
        return NextResponse.json({
          success: true,
          data: health
        });

      default:
        return NextResponse.json({ 
          error: 'Invalid action. Supported actions: stats, logs, user-status, health' 
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Error in Gmail monitoring API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get Gmail monitoring data',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}
