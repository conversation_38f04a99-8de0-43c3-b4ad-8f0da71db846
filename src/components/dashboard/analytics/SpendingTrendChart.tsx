'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

interface SpendingTrendChartProps {
  data: { month: string; total: number }[];
  currency?: string;
}

export function SpendingTrendChart({ data, currency = 'KES' }: SpendingTrendChartProps) {
  const hasData = data && data.length > 0 && data.some(item => item.total > 0);

  // Format currency based on the currency type
  const formatCurrency = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
  };

  const formatCurrencyDetailed = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-lg p-4">
      <h3 className="text-base font-semibold text-white mb-3">Spending Trend</h3>

      {hasData ? (
        <ResponsiveContainer width="100%" height={240}>
          <LineChart data={data} margin={{ top: 10, right: 15, left: 10, bottom: 10 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis
              dataKey="month"
              stroke="#9CA3AF"
              fontSize={11}
              tick={{ fontSize: 11 }}
            />
            <YAxis
              stroke="#9CA3AF"
              fontSize={11}
              tick={{ fontSize: 11 }}
              tickFormatter={(value) => formatCurrency(value)}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: '#1F2937',
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F9FAFB'
              }}
              formatter={(value: any) => [formatCurrencyDetailed(value), 'Total']}
            />
            <Line
              type="monotone"
              dataKey="total"
              stroke="#FF006E"
              strokeWidth={2}
              dot={{ fill: '#FF006E', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: '#FF006E', strokeWidth: 2 }}
            />
          </LineChart>
        </ResponsiveContainer>
      ) : (
        <div className="h-[240px] flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="w-14 h-14 mx-auto mb-4 bg-gray-700/30 rounded-full flex items-center justify-center">
              <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <p className="text-sm font-medium mb-1">No spending data yet</p>
            <p className="text-xs">Upload some receipts to see your spending trends</p>
          </div>
        </div>
      )}
    </div>
  );
} 