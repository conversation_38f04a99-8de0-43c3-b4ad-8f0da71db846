import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Google Sheets API types
interface GoogleSheetsClient {
  spreadsheets: {
    create: (params: any) => Promise<any>;
    values: {
      append: (params: any) => Promise<any>;
      update: (params: any) => Promise<any>;
    };
    batchUpdate: (params: any) => Promise<any>;
  };
}

interface ExtractedReceiptData {
  vendor?: string;
  vendor_tax_id?: string;
  date?: string;
  currency?: string;
  payment_method?: string;
  items?: Array<{
    description: string;
    total: number;
    category?: string;
  }>;
  subtotal?: number;
  tax_rate_percent?: number;
  tax_amount?: number;
  total_amount?: number;
}

interface GoogleSheetInfo {
  id: string;
  url: string;
  name: string;
  year: number;
}

/**
 * Create authenticated Google Sheets client for a user
 */
async function createAuthenticatedSheetsClient(userId: string): Promise<{ sheets: GoogleSheetsClient }> {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  // Get user's Google tokens
  const { data: user } = await supabase
    .from('users')
    .select('google_access_token, google_refresh_token')
    .eq('id', userId)
    .single();

  if (!user?.google_access_token) {
    throw new Error('User has not connected Google account');
  }

  // Initialize Google Sheets API client (simplified for Edge Function)
  const googleApiUrl = 'https://sheets.googleapis.com/v4/spreadsheets';
  const headers = {
    'Authorization': `Bearer ${user.google_access_token}`,
    'Content-Type': 'application/json'
  };

  // Return a mock client that makes direct API calls
  const sheets: GoogleSheetsClient = {
    spreadsheets: {
      create: async (params: any) => {
        const response = await fetch(googleApiUrl, {
          method: 'POST',
          headers,
          body: JSON.stringify(params.resource)
        });
        return await response.json();
      },
      values: {
        append: async (params: any) => {
          const url = `${googleApiUrl}/${params.spreadsheetId}/values/${params.range}:append?valueInputOption=${params.valueInputOption}`;
          const response = await fetch(url, {
            method: 'POST',
            headers,
            body: JSON.stringify(params.requestBody)
          });
          return await response.json();
        },
        update: async (params: any) => {
          const url = `${googleApiUrl}/${params.spreadsheetId}/values/${params.range}?valueInputOption=${params.valueInputOption}`;
          const response = await fetch(url, {
            method: 'PUT',
            headers,
            body: JSON.stringify(params.requestBody)
          });
          return await response.json();
        }
      },
      batchUpdate: async (params: any) => {
        const url = `${googleApiUrl}/${params.spreadsheetId}:batchUpdate`;
        const response = await fetch(url, {
          method: 'POST',
          headers,
          body: JSON.stringify(params.requestBody)
        });
        return await response.json();
      }
    }
  };

  return { sheets };
}

/**
 * Get or create current year's Google Sheet for user
 */
async function getOrCreateCurrentYearSheet(userId: string): Promise<GoogleSheetInfo> {
  const currentYear = new Date().getFullYear();
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );

  // Check if sheet exists for current year
  const { data: existingSheet } = await supabase
    .from('google_sheets')
    .select('*')
    .eq('user_id', userId)
    .eq('year', currentYear)
    .single();

  if (existingSheet) {
    return {
      id: existingSheet.sheet_id,
      url: existingSheet.sheet_url,
      name: existingSheet.sheet_name,
      year: existingSheet.year
    };
  }

  // Create new sheet
  const { sheets } = await createAuthenticatedSheetsClient(userId);
  
  // Get user info for sheet naming
  const { data: user } = await supabase
    .from('users')
    .select('full_name, email')
    .eq('id', userId)
    .single();

  const userName = user?.full_name || user?.email?.split('@')[0] || 'User';
  const sheetName = `Receipts ${currentYear} - ${userName}`;

  // Create the spreadsheet
  const spreadsheet = await sheets.spreadsheets.create({
    resource: {
      properties: {
        title: sheetName
      },
      sheets: [{
        properties: {
          title: 'Receipts',
          gridProperties: {
            rowCount: 1000,
            columnCount: 14
          }
        }
      }]
    }
  });

  // Add headers
  const headers = [
    'Receipt ID', 'Vendor', 'Vendor Tax ID', 'Date', 'Currency',
    'Payment Method', 'Item Description', 'Item Category', 'Item Total',
    'Subtotal', 'Tax Rate %', 'Tax Amount', 'Total Amount', 'Source File'
  ];

  await sheets.spreadsheets.values.update({
    spreadsheetId: spreadsheet.spreadsheetId,
    range: 'Receipts!A1:N1',
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: [headers]
    }
  });

  // Style the header row
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId: spreadsheet.spreadsheetId,
    requestBody: {
      requests: [{
        repeatCell: {
          range: {
            sheetId: 0,
            startRowIndex: 0,
            endRowIndex: 1,
            startColumnIndex: 0,
            endColumnIndex: 14
          },
          cell: {
            userEnteredFormat: {
              backgroundColor: { red: 0.2, green: 0.2, blue: 0.2 },
              textFormat: {
                foregroundColor: { red: 1, green: 1, blue: 1 },
                bold: true
              }
            }
          },
          fields: 'userEnteredFormat(backgroundColor,textFormat)'
        }
      }]
    }
  });

  // Save to database
  const { data: newSheet } = await supabase
    .from('google_sheets')
    .insert({
      user_id: userId,
      year: currentYear,
      sheet_id: spreadsheet.spreadsheetId,
      sheet_url: spreadsheet.spreadsheetUrl,
      sheet_name: sheetName,
      last_row_number: 1,
      total_receipts: 0
    })
    .select()
    .single();

  return {
    id: spreadsheet.spreadsheetId,
    url: spreadsheet.spreadsheetUrl,
    name: sheetName,
    year: currentYear
  };
}

/**
 * Export extracted receipt data to the user's Google Sheet
 */
export async function exportReceiptToGoogleSheets(
  userId: string,
  receiptId: string,
  extractedData: ExtractedReceiptData,
  originalFileName: string
): Promise<{ success: boolean; rowNumber?: number; error?: string }> {
  try {
    console.log(`Exporting receipt ${receiptId} to Google Sheets for user ${userId}`);
    
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    // Get user's Google tokens
    const { data: user } = await supabase
      .from('users')
      .select('google_access_token, google_refresh_token')
      .eq('id', userId)
      .single();

    if (!user?.google_access_token) {
      console.log('User has not connected Google account, skipping Google Sheets export');
      return { success: false, error: 'User has not connected Google account' };
    }

    // Get current year
    const currentYear = new Date().getFullYear();
    
    // Check if sheet exists for current year
    const { data: existingSheet } = await supabase
      .from('google_sheets')
      .select('*')
      .eq('user_id', userId)
      .eq('year', currentYear)
      .single();

    let sheetId = existingSheet?.sheet_id;
    let lastRow = existingSheet?.last_row_number || 1;

    // If no sheet exists, create one
    if (!sheetId) {
      console.log('Creating new Google Sheet for user');
      
      // Get user info for sheet naming
      const { data: userInfo } = await supabase
        .from('users')
        .select('full_name, email')
        .eq('id', userId)
        .single();

      const userName = userInfo?.full_name || userInfo?.email?.split('@')[0] || 'User';
      const sheetName = `Receipts ${currentYear} - ${userName}`;

      // Create spreadsheet via Google Sheets API
      const createResponse = await fetch('https://sheets.googleapis.com/v4/spreadsheets', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.google_access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          properties: {
            title: sheetName
          },
          sheets: [{
            properties: {
              title: 'Receipts',
              gridProperties: {
                rowCount: 1000,
                columnCount: 14
              }
            }
          }]
        })
      });

      const spreadsheet = await createResponse.json();
      sheetId = spreadsheet.spreadsheetId;

      // Add headers
      const headers = [
        'Receipt ID', 'Vendor', 'Vendor Tax ID', 'Date', 'Currency',
        'Payment Method', 'Item Description', 'Item Category', 'Item Total',
        'Subtotal', 'Tax Rate %', 'Tax Amount', 'Total Amount', 'Source File'
      ];

      await fetch(`https://sheets.googleapis.com/v4/spreadsheets/${sheetId}/values/Receipts!A1:N1?valueInputOption=USER_ENTERED`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${user.google_access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          values: [headers]
        })
      });

      // Save to database
      await supabase
        .from('google_sheets')
        .insert({
          user_id: userId,
          year: currentYear,
          sheet_id: sheetId,
          sheet_url: spreadsheet.spreadsheetUrl,
          sheet_name: sheetName,
          last_row_number: 1,
          total_receipts: 0
        });

      lastRow = 1;
    }

    const nextRow = lastRow + 1;
    
    // Prepare data rows
    const rows: any[][] = [];
    
    if (extractedData.items && extractedData.items.length > 0) {
      for (let i = 0; i < extractedData.items.length; i++) {
        const item = extractedData.items[i];
        const isFirstItem = i === 0;

        const row = [
          receiptId,
          extractedData.vendor || '',
          extractedData.vendor_tax_id || '',
          extractedData.date || '',
          extractedData.currency || 'KES',
          extractedData.payment_method || '',
          item.description || '',
          item.category || '',
          item.total || 0,
          // Only include subtotal, tax, and total for the first item to avoid duplication
          isFirstItem ? (extractedData.subtotal || 0) : '',
          isFirstItem ? (extractedData.tax_rate_percent || 0) : '',
          isFirstItem ? (extractedData.tax_amount || 0) : '',
          isFirstItem ? (extractedData.total_amount || 0) : '',
          originalFileName
        ];
        rows.push(row);
      }
    } else {
      // No items, export receipt data as single row
      const row = [
        receiptId,
        extractedData.vendor || '',
        extractedData.vendor_tax_id || '',
        extractedData.date || '',
        extractedData.currency || 'KES',
        extractedData.payment_method || '',
        'No items extracted',
        '',
        extractedData.total_amount || 0,
        extractedData.subtotal || 0,
        extractedData.tax_rate_percent || 0,
        extractedData.tax_amount || 0,
        extractedData.total_amount || 0,
        originalFileName
      ];
      rows.push(row);
    }
    
    // Append data to sheet
    await fetch(`https://sheets.googleapis.com/v4/spreadsheets/${sheetId}/values/Receipts!A:N:append?valueInputOption=USER_ENTERED`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${user.google_access_token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        values: rows
      })
    });
    
    // Update last row number and receipt count
    const newLastRow = nextRow + rows.length - 1;
    await supabase
      .from('google_sheets')
      .update({
        last_row_number: newLastRow,
        total_receipts: (existingSheet?.total_receipts || 0) + 1
      })
      .eq('user_id', userId)
      .eq('year', currentYear);
    
    console.log(`Exported ${rows.length} rows to Google Sheets`);
    return { success: true, rowNumber: nextRow };
    
  } catch (error) {
    console.error('Error exporting to Google Sheets:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Update the Google Sheet row number in the receipt record
 */
export async function updateReceiptSheetRowNumber(
  receiptId: string,
  rowNumber: number
): Promise<void> {
  const supabase = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  );
  
  const { error } = await supabase
    .from('receipts')
    .update({ google_sheet_row_number: rowNumber })
    .eq('id', receiptId);
  
  if (error) {
    console.error('Error updating receipt sheet row number:', error);
    throw new Error('Failed to update receipt sheet row number');
  }
} 