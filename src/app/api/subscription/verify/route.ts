import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { subscriptionManager } from '@/lib/subscription/manager';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Not authenticated' 
      }, { status: 401 });
    }

    const body = await request.json();
    const { reference } = body;

    if (!reference) {
      return NextResponse.json({ 
        success: false, 
        error: 'Payment reference is required' 
      }, { status: 400 });
    }

    // Verify payment and upgrade subscription
    const result = await subscriptionManager.verifyAndUpgradeSubscription(reference);

    if (!result.success) {
      return NextResponse.json({ 
        success: false, 
        error: result.error || 'Payment verification failed' 
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: result.user?.id,
          current_tier: result.user?.current_tier,
          subscription_status: result.user?.subscription_status,
          monthly_receipt_limit: result.user?.monthly_receipt_limit,
          receipts_used_this_period: result.user?.receipts_used_this_period,
          next_billing_date: result.user?.next_billing_date
        },
        transaction: {
          id: result.transaction?.id,
          amount: result.transaction?.amount,
          tier: result.transaction?.tier,
          status: 'success'
        }
      },
      message: 'Subscription upgraded successfully!'
    });

  } catch (error) {
    console.error('Error verifying subscription payment:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Internal server error' 
    }, { status: 500 });
  }
}
