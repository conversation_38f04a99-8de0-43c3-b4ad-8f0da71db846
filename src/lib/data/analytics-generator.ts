import { createClient } from '@/lib/supabase/server';

/**
 * Generate and store analytics data for a specific user and month
 */
export async function generateMonthlyAnalytics(userId: string, monthYear: string): Promise<void> {
  const supabase = await createClient();
  
  console.log(`Generating analytics for user ${userId} for month ${monthYear}`);
  
  // Get receipts for the specified month
  const startOfMonth = `${monthYear}-01`;
  const endOfMonth = new Date(new Date(startOfMonth).getFullYear(), new Date(startOfMonth).getMonth() + 1, 0).toISOString().slice(0, 10);
  
  const { data: receipts, error: receiptsError } = await supabase
    .from('receipts')
    .select(`
      id,
      total_amount,
      vendor,
      receipt_date,
      currency,
      receipt_items (
        total_price,
        category
      )
    `)
    .eq('user_id', userId)
    .gte('receipt_date', startOfMonth)
    .lte('receipt_date', endOfMonth)
    .eq('processing_status', 'completed');

  if (receiptsError) {
    console.error('Error fetching receipts for analytics:', receiptsError);
    return;
  }

  const receiptList = receipts || [];
  console.log(`Found ${receiptList.length} receipts for analytics`);

  // Calculate basic metrics
  const totalReceipts = receiptList.length;
  const totalSpent = receiptList.reduce((sum, receipt) => sum + (receipt.total_amount || 0), 0);
  const averageReceiptAmount = totalReceipts > 0 ? totalSpent / totalReceipts : 0;

  // Find top vendor
  const vendorCounts = receiptList.reduce((acc, receipt) => {
    if (receipt.vendor) {
      acc[receipt.vendor] = (acc[receipt.vendor] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  const topVendorEntry = Object.entries(vendorCounts)
    .sort(([,a], [,b]) => b - a)[0];
  const topVendor = topVendorEntry ? topVendorEntry[0] : null;

  // Calculate category breakdown
  const categoryTotals: Record<string, number> = {};
  receiptList.forEach(receipt => {
    if (receipt.receipt_items && receipt.receipt_items.length > 0) {
      receipt.receipt_items.forEach((item: any) => {
        const category = item.category || 'Other';
        categoryTotals[category] = (categoryTotals[category] || 0) + (item.total_price || 0);
      });
    } else {
      // If no items, use the receipt total under 'General' category
      categoryTotals['General'] = (categoryTotals['General'] || 0) + (receipt.total_amount || 0);
    }
  });

  const topCategoryEntry = Object.entries(categoryTotals)
    .sort(([,a], [,b]) => b - a)[0];
  const topCategory = topCategoryEntry ? topCategoryEntry[0] : null;

  // Calculate vendor breakdown
  const vendorTotals = receiptList.reduce((acc, receipt) => {
    if (receipt.vendor) {
      acc[receipt.vendor] = (acc[receipt.vendor] || 0) + (receipt.total_amount || 0);
    }
    return acc;
  }, {} as Record<string, number>);

  // Prepare analytics data
  const analyticsData = {
    user_id: userId,
    month_year: monthYear,
    total_receipts: totalReceipts,
    total_spent: totalSpent,
    top_vendor: topVendor,
    top_category: topCategory,
    average_receipt_amount: averageReceiptAmount,
    category_breakdown: categoryTotals,
    vendor_breakdown: vendorTotals,
  };

  console.log('Analytics data calculated:', analyticsData);

  // Upsert analytics data
  const { error: upsertError } = await supabase
    .from('user_analytics')
    .upsert(analyticsData, {
      onConflict: 'user_id,month_year'
    });

  if (upsertError) {
    console.error('Error upserting analytics data:', upsertError);
    throw upsertError;
  }

  console.log(`Analytics generated successfully for ${userId} - ${monthYear}`);
}

/**
 * Generate analytics for all months that have completed receipts for a user
 */
export async function generateAllAnalyticsForUser(userId: string): Promise<void> {
  const supabase = await createClient();
  
  console.log(`Generating all analytics for user ${userId}`);
  
  // Get all unique months that have completed receipts
  const { data: monthsData, error } = await supabase
    .from('receipts')
    .select('receipt_date')
    .eq('user_id', userId)
    .eq('processing_status', 'completed')
    .not('receipt_date', 'is', null);

  if (error) {
    console.error('Error fetching receipt months:', error);
    return;
  }

  // Extract unique months
  const uniqueMonths = new Set<string>();
  (monthsData || []).forEach(receipt => {
    if (receipt.receipt_date) {
      const monthYear = receipt.receipt_date.slice(0, 7); // YYYY-MM
      uniqueMonths.add(monthYear);
    }
  });

  console.log(`Found ${uniqueMonths.size} unique months with receipts:`, Array.from(uniqueMonths));

  // Generate analytics for each month
  for (const monthYear of uniqueMonths) {
    try {
      await generateMonthlyAnalytics(userId, monthYear);
    } catch (error) {
      console.error(`Failed to generate analytics for ${monthYear}:`, error);
    }
  }
}

/**
 * API endpoint to trigger analytics generation
 */
export async function triggerAnalyticsGeneration(): Promise<{ success: boolean; message: string }> {
  try {
    const supabase = await createClient();
    
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { success: false, message: 'User not authenticated' };
    }

    await generateAllAnalyticsForUser(user.id);
    
    return { success: true, message: 'Analytics generated successfully' };
  } catch (error) {
    console.error('Error in triggerAnalyticsGeneration:', error);
    return { success: false, message: 'Failed to generate analytics' };
  }
}
