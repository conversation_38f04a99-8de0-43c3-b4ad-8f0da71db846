'use client';

import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from 'recharts';
import { TrendingUp, TrendingDown } from 'lucide-react';

interface EnhancedSpendingChartProps {
  data: { month: string; total: number }[];
  currency?: string;
}

export default function EnhancedSpendingChart({ data, currency = 'KES' }: EnhancedSpendingChartProps) {
  const hasData = data && data.length > 0 && data.some(item => item.total > 0);

  // Format currency based on the currency type
  const formatCurrency = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
  };

  const formatCurrencyDetailed = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  // Calculate trend
  const calculateTrend = () => {
    if (!hasData || data.length < 2) return { trend: 'neutral', percentage: 0 };
    
    const lastTwo = data.slice(-2);
    const previous = lastTwo[0].total;
    const current = lastTwo[1].total;
    
    if (previous === 0) return { trend: 'neutral', percentage: 0 };
    
    const percentage = ((current - previous) / previous) * 100;
    const trend = percentage > 0 ? 'up' : percentage < 0 ? 'down' : 'neutral';
    
    return { trend, percentage: Math.abs(percentage) };
  };

  const { trend, percentage } = calculateTrend();

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-white">Spending Trend</h3>
          <p className="text-sm text-gray-400">Track your spending over time</p>
        </div>
        {hasData && (
          <div className="flex items-center space-x-2">
            {trend === 'up' ? (
              <TrendingUp className="w-5 h-5 text-red-400" />
            ) : trend === 'down' ? (
              <TrendingDown className="w-5 h-5 text-green-400" />
            ) : null}
            <span className={`text-sm font-medium ${
              trend === 'up' ? 'text-red-400' : trend === 'down' ? 'text-green-400' : 'text-gray-400'
            }`}>
              {percentage > 0 ? `${percentage.toFixed(1)}%` : 'No change'}
            </span>
          </div>
        )}
      </div>
      
      {hasData ? (
        <ResponsiveContainer width="100%" height={350}>
          <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="spendingGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#FF006E" stopOpacity={0.3}/>
                <stop offset="95%" stopColor="#FF006E" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis 
              dataKey="month" 
              stroke="#9CA3AF"
              fontSize={12}
              tick={{ fill: '#9CA3AF' }}
            />
            <YAxis
              stroke="#9CA3AF"
              fontSize={12}
              tick={{ fill: '#9CA3AF' }}
              tickFormatter={(value) => formatCurrency(value)}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: '#1F2937',
                border: '1px solid #374151',
                borderRadius: '12px',
                color: '#F9FAFB',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
              }}
              formatter={(value: any) => [formatCurrencyDetailed(value), 'Spending']}
              labelStyle={{ color: '#F9FAFB' }}
            />
            <Area
              type="monotone"
              dataKey="total"
              stroke="#FF006E"
              strokeWidth={3}
              fill="url(#spendingGradient)"
              dot={{ fill: '#FF006E', strokeWidth: 2, r: 5 }}
              activeDot={{ r: 7, stroke: '#FF006E', strokeWidth: 3, fill: '#1F2937' }}
            />
          </AreaChart>
        </ResponsiveContainer>
      ) : (
        <div className="h-[350px] flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gray-700/30 rounded-full flex items-center justify-center">
              <TrendingUp className="w-10 h-10" />
            </div>
            <p className="text-lg font-medium mb-2">No spending data yet</p>
            <p className="text-sm">Upload some receipts to see your spending trends</p>
          </div>
        </div>
      )}
    </div>
  );
}
