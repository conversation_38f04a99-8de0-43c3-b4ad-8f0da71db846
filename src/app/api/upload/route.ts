import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    console.log('Upload API endpoint called')
    
    const supabase = await createClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      console.error('Authentication failed:', authError)
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      )
    }
    
    console.log(`Upload request from user: ${user.id}`)

    // Check user tier limits before processing
    const { data: userLimits, error: limitsError } = await supabase
      .rpc('check_tier_limits', { user_id_param: user.id })

    if (limitsError) {
      console.error('Error checking tier limits:', limitsError)
      return NextResponse.json(
        { success: false, error: 'Failed to check tier limits' },
        { status: 500 }
      )
    }

    const limits = userLimits?.[0]
    if (!limits?.can_process_receipt) {
      console.error('User has exceeded receipt limit:', {
        user_id: user.id,
        receipts_remaining: limits?.receipts_remaining || 0
      })

      return NextResponse.json(
        {
          success: false,
          error: 'Receipt limit exceeded for current tier. Please upgrade your subscription.',
          code: 'LIMIT_EXCEEDED',
          receipts_remaining: limits?.receipts_remaining || 0
        },
        { status: 429 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      )
    }
    
    console.log(`Processing file: ${file.name}, size: ${file.size}, type: ${file.type}`)
    
    // Validate file - images and PDFs are allowed (PDFs are converted to images on frontend)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'File type not supported. Please upload JPG, PNG, WebP, or PDF files.' },
        { status: 400 }
      )
    }
    
    // Different size limits for different file types
    const maxSize = file.type === 'application/pdf' ? 5 * 1024 * 1024 : 10 * 1024 * 1024 // 5MB for PDFs, 10MB for images
    if (file.size > maxSize) {
      const limitMB = file.type === 'application/pdf' ? '5MB' : '10MB'
      return NextResponse.json(
        { success: false, error: `File size exceeds ${limitMB} limit` },
        { status: 400 }
      )
    }
    
    // Generate unique filename
    const timestamp = Date.now()
    const fileExtension = file.name.split('.').pop()
    const uniqueFileName = `receipt_${timestamp}_${Math.random().toString(36).substring(2)}.${fileExtension}`
    const filePath = `${user.id}/${new Date().getFullYear()}/${uniqueFileName}`
    
    console.log(`Uploading to storage path: ${filePath}`)
    
    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('receipts')
      .upload(filePath, file, {
        contentType: file.type,
        upsert: false
      })
    
    if (uploadError) {
      console.error('Storage upload failed:', uploadError)
      return NextResponse.json(
        { success: false, error: `Upload failed: ${uploadError.message}` },
        { status: 500 }
      )
    }
    
    console.log('File uploaded to storage successfully')
    
    // Create receipt record in database
    const { data: receipt, error: dbError } = await supabase
      .from('receipts')
      .insert({
        user_id: user.id,
        original_file_name: file.name,
        file_path: uploadData.path,
        file_size: file.size,
        mime_type: file.type,
        processing_status: 'pending'
      })
      .select()
      .single()
    
    if (dbError) {
      console.error('Database insert failed:', dbError)
      
      // Clean up uploaded file on DB error
      await supabase.storage
        .from('receipts')
        .remove([uploadData.path])
      
      return NextResponse.json(
        { success: false, error: `Database error: ${dbError.message}` },
        { status: 500 }
      )
    }
    
    console.log(`Receipt record created with ID: ${receipt.id}`)

    // Increment user's receipt usage counter
    const { error: usageError } = await supabase.rpc('increment_user_receipt_count', {
      user_id_param: user.id
    })

    if (usageError) {
      console.error('Failed to increment usage counter:', usageError)
      // Don't fail the upload for this, but log it
    } else {
      console.log('Receipt usage counter incremented')
    }

    // Note: Google Drive backup upload is handled separately via the Google Drive import feature

    // Check if we're in local development mode
    const isLocalDev = process.env.NODE_ENV === 'development' ||
                      process.env.VERCEL_ENV === undefined;

    // OPTIMIZATION: Always create backup job for reliability
    // This ensures processing even if webhooks fail
    console.log('🔄 Creating backup job for upload reliability...');

    try {
      // Import here to avoid issues if not needed
      const { createProcessReceiptJob } = await import('@/lib/jobs');
      const { addJobToQueue } = await import('@/lib/queue');

      // Create job for processing queue (backup for webhook reliability)
      const job = createProcessReceiptJob({
        receiptId: receipt.id,
        userId: user.id,
        priority: 'normal',
        imageUrl: uploadData.path,
        fileName: file.name,
        fileSize: file.size
      });

      // Add job to Redis queue as backup
      await addJobToQueue(job);

      // Update receipt with job ID
      await supabase
        .from('receipts')
        .update({ redis_job_id: job.id })
        .eq('id', receipt.id);

      console.log(`✅ Backup job created for upload: ${job.id}`);

      // For production, also trigger immediate processing as backup
      if (!isLocalDev) {
        try {
          const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
          const pipelineResponse = await fetch(`${baseUrl}/api/pipeline`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'Upload-Backup/1.0'
            }
          });

          if (pipelineResponse.ok) {
            console.log('⚡ Backup processing triggered for upload');
          }
        } catch (triggerError) {
          console.warn('⚠️ Backup processing trigger failed:', triggerError);
        }
      }

    } catch (jobError) {
      console.error('❌ Failed to create backup job for upload:', jobError);
      // Continue anyway - webhook should handle it
    }

    // Production: Job creation and queue addition is handled by database trigger
    // The trigger_receipt_processing() function will automatically create and queue
    // a job when the receipt is inserted with processing_status = 'pending'
    console.log(`Receipt created: ${receipt.id} - processing will be triggered by database webhook`);
    
    console.log('Upload process completed successfully')
    
    return NextResponse.json({
      success: true,
      message: 'Receipt uploaded and processing triggered via webhook',
      data: {
        receiptId: receipt.id,
        fileName: file.name,
        filePath: uploadData.path,
        status: 'pending' // Will be updated to 'processing' by webhook
      }
    })
    
  } catch (error) {
    console.error('Upload API error:', error)
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
} 