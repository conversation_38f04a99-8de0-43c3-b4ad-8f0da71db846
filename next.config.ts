import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has TypeScript errors.
    ignoreBuildErrors: true,
  },
  // Suppress hydration warnings in development (common with browser extensions)
  reactStrictMode: true,
  // Environment variables
  env: {
    NEXT_PUBLIC_GEMINI_API_KEY: process.env.GEMINI_API_KEY,
  },
  // Experimental features for better SSR handling
  experimental: {
    // Remove esmExternals as it's not supported by Turbopack
  },
  // Headers for proper MIME types
  async headers() {
    return [
      {
        source: '/pdf.worker.min.mjs',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/javascript',
          },
        ],
      },
    ];
  },
  // PDF.js configuration - only apply when not using Turbopack
  ...(process.env.NODE_ENV === 'development' && process.env.TURBOPACK ? {} : {
    webpack: (config, { isServer }) => {
      // Handle PDF.js worker and prevent SSR issues
      if (!isServer) {
        config.resolve.alias = {
          ...config.resolve.alias,
          canvas: false,
        };

        // Ensure PDF.js worker is handled correctly
        config.resolve.fallback = {
          ...config.resolve.fallback,
          fs: false,
          path: false,
          crypto: false,
        };
      } else {
        // Prevent server-side bundling of PDF.js and related modules
        config.resolve.alias = {
          ...config.resolve.alias,
          'pdfjs-dist': false,
          canvas: false,
        };

        // Add externals to prevent bundling on server side
        config.externals = config.externals || [];
        config.externals.push('pdfjs-dist');
      }

      return config;
    },
  }),
};

export default nextConfig;
