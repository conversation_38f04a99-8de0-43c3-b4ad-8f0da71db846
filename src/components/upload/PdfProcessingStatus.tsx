'use client'

import React from 'react'
import { AlertCircle, CheckCircle, Loader2, FileText } from 'lucide-react'

interface PdfProcessingStatusProps {
  isProcessing: boolean
  error?: string | null
  fileName?: string
  onRetry?: () => void
}

export default function PdfProcessingStatus({
  isProcessing,
  error,
  fileName,
  onRetry
}: PdfProcessingStatusProps) {
  if (!isProcessing && !error) return null

  return (
    <div className="mt-4 p-4 bg-gray-800/50 border border-gray-700 rounded-lg">
      <div className="flex items-start space-x-3">
        {isProcessing ? (
          <Loader2 className="w-5 h-5 text-pink-400 animate-spin flex-shrink-0 mt-0.5" />
        ) : error ? (
          <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
        ) : (
          <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0 mt-0.5" />
        )}
        
        <div className="flex-1 min-w-0">
          {isProcessing ? (
            <div>
              <p className="text-white text-sm font-medium">
                Converting PDF to Image
              </p>
              <p className="text-gray-400 text-xs mt-1">
                {fileName ? `Processing ${fileName}...` : 'This may take a few seconds...'}
              </p>
            </div>
          ) : error ? (
            <div>
              <p className="text-red-400 text-sm font-medium">
                PDF Conversion Failed
              </p>
              <p className="text-gray-400 text-xs mt-1">
                {error}
              </p>
              {onRetry && (
                <button
                  onClick={onRetry}
                  className="mt-2 text-xs bg-pink-600 hover:bg-pink-700 text-white px-3 py-1 rounded transition-colors"
                >
                  Try Again
                </button>
              )}
              <div className="mt-2 text-xs text-gray-500">
                <p>Alternative options:</p>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  <li>Convert your PDF to JPG/PNG using online tools</li>
                  <li>Take a screenshot of your receipt</li>
                  <li>Use a different browser (Chrome, Firefox, Safari)</li>
                </ul>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  )
}
