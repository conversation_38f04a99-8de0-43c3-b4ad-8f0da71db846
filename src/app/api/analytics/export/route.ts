import { NextRequest, NextResponse } from 'next/server';
import { getEnhancedAnalyticsData, AnalyticsFilters } from '@/lib/data/analytics';
import { createClient } from '@/lib/supabase/server';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

export async function POST(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const format = url.searchParams.get('format') || 'csv';
    
    const filters: AnalyticsFilters = await request.json();
    
    // Validate format
    if (!['csv', 'pdf'].includes(format)) {
      return NextResponse.json(
        { error: 'Invalid format. Use csv or pdf' },
        { status: 400 }
      );
    }

    // Get user authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get analytics data
    const analyticsData = await getEnhancedAnalyticsData(filters);
    
    if (format === 'csv') {
      const csvContent = generateCSVReport(analyticsData, filters);
      
      return new NextResponse(csvContent, {
        status: 200,
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="analytics-${new Date().toISOString().slice(0, 10)}.csv"`,
        },
      });
    } else if (format === 'pdf') {
      const pdfBuffer = generatePDFReport(analyticsData, filters);

      return new NextResponse(pdfBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="receiptlabs-analytics-${new Date().toISOString().slice(0, 10)}.pdf"`,
        },
      });
    }
    
    return NextResponse.json(
      { error: 'Unsupported format' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Analytics export error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function generateCSVReport(data: any, filters: AnalyticsFilters): string {
  const lines = [];
  
  // Header
  lines.push('**Receipt**Labs Analytics Report');
  lines.push(`Generated: ${new Date().toLocaleString()}`);
  lines.push(`Period: ${filters.period.toUpperCase()}`);
  if (filters.startDate && filters.endDate) {
    lines.push(`Date Range: ${filters.startDate} to ${filters.endDate}`);
  }
  if (filters.vendor) {
    lines.push(`Vendor Filter: ${filters.vendor}`);
  }
  if (filters.category) {
    lines.push(`Category Filter: ${filters.category}`);
  }
  lines.push('');

  // Executive Summary
  lines.push('EXECUTIVE SUMMARY');
  lines.push('Metric,Value');
  lines.push(`Total Spending,${data.totalSpending} ${data.currency}`);
  lines.push(`Receipt Count,${data.receiptCount}`);
  lines.push(`Average Receipt Value,${data.averageReceiptValue} ${data.currency}`);
  lines.push(`Top Vendor,${data.topVendor.vendor} (${data.topVendor.count} receipts)`);
  lines.push(`Processing Success Rate,${data.processingStats.successRate}%`);
  lines.push('');
  
  // Spending trend
  lines.push('SPENDING TREND');
  lines.push('Month,Total');
  data.spendingTrend.forEach((item: any) => {
    lines.push(`${item.month},${item.total}`);
  });
  lines.push('');
  
  // Category breakdown
  lines.push('CATEGORY BREAKDOWN');
  lines.push('Category,Total,Percentage');
  data.topCategories.forEach((item: any) => {
    lines.push(`${item.category},${item.total},${item.percentage}%`);
  });
  lines.push('');
  
  // Vendor analysis
  lines.push('VENDOR ANALYSIS');
  lines.push('Vendor,Total Spent,Receipt Count,Average Amount');
  data.vendorAnalysis.forEach((item: any) => {
    lines.push(`${item.vendor},${item.totalSpent},${item.receiptCount},${item.averageAmount}`);
  });
  lines.push('');
  
  // Payment methods
  lines.push('PAYMENT METHODS');
  lines.push('Method,Total,Count');
  data.paymentMethodBreakdown.forEach((item: any) => {
    lines.push(`${item.method},${item.total},${item.count}`);
  });
  lines.push('');
  
  // Period Comparison
  lines.push('PERIOD COMPARISON');
  lines.push('Metric,Value');
  lines.push(`Current Period,${data.monthlyComparison.currentMonth} ${data.currency}`);
  lines.push(`Previous Period,${data.monthlyComparison.previousMonth} ${data.currency}`);
  lines.push(`Change,${data.monthlyComparison.percentageChange.toFixed(1)}%`);
  lines.push('');

  // Processing Statistics
  lines.push('PROCESSING STATISTICS');
  lines.push('Metric,Value');
  lines.push(`Total Processed,${data.processingStats.totalProcessed}`);
  lines.push(`Successfully Processed,${data.processingStats.totalProcessed - data.processingStats.failed - data.processingStats.pending}`);
  lines.push(`Failed,${data.processingStats.failed}`);
  lines.push(`Pending,${data.processingStats.pending}`);
  lines.push(`Success Rate,${data.processingStats.successRate}%`);
  lines.push('');

  // Recent Activity
  if (data.recentActivity && data.recentActivity.length > 0) {
    lines.push('RECENT ACTIVITY (Last 7 Days)');
    lines.push('Date,Receipts,Amount');
    data.recentActivity.forEach((activity: any) => {
      lines.push(`${activity.date},${activity.receipts},${activity.amount} ${data.currency}`);
    });
    lines.push('');
  }

  // Report Footer
  lines.push('');
  lines.push('Report generated by **Receipt**Labs');
  lines.push('AI-Powered Receipt Management Platform');
  lines.push(`Report ID: RPT-${Date.now()}`);

  return lines.join('\n');
}

function generatePDFReport(data: any, filters: AnalyticsFilters): Buffer {
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  const pageWidth = pdf.internal.pageSize.getWidth();
  const margin = 20;
  let yPosition = 30;

  // Helper function to add text with formatting
  const addText = (text: string, x: number, y: number, options: any = {}) => {
    const { fontSize = 10, bold = false, color = '#000000', align = 'left' } = options;

    pdf.setFontSize(fontSize);
    pdf.setFont('helvetica', bold ? 'bold' : 'normal');
    pdf.setTextColor(color);

    if (align === 'center') {
      pdf.text(text, x, y, { align: 'center' });
    } else if (align === 'right') {
      pdf.text(text, x, y, { align: 'right' });
    } else {
      pdf.text(text, x, y);
    }
  };

  // Helper function to add a section separator
  const addSeparator = () => {
    yPosition += 5;
    pdf.setDrawColor(200, 200, 200);
    pdf.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 10;
  };

  // Header
  addText('**Receipt**Labs Analytics Report', pageWidth / 2, yPosition, {
    fontSize: 20,
    bold: true,
    color: '#8338EC',
    align: 'center'
  });
  yPosition += 15;

  addText(`Generated: ${new Date().toLocaleString()}`, pageWidth / 2, yPosition, {
    fontSize: 10,
    align: 'center'
  });
  yPosition += 8;

  addText(`Period: ${filters.period.toUpperCase()}`, pageWidth / 2, yPosition, {
    fontSize: 10,
    align: 'center'
  });
  yPosition += 5;

  if (filters.startDate && filters.endDate) {
    addText(`Date Range: ${filters.startDate} to ${filters.endDate}`, pageWidth / 2, yPosition, {
      fontSize: 10,
      align: 'center'
    });
    yPosition += 5;
  }

  addSeparator();

  // Executive Summary
  addText('EXECUTIVE SUMMARY', margin, yPosition, { fontSize: 14, bold: true });
  yPosition += 10;

  const summaryData = [
    ['Total Spending', `${data.totalSpending} ${data.currency}`],
    ['Receipt Count', data.receiptCount.toString()],
    ['Average Receipt Value', `${data.averageReceiptValue} ${data.currency}`],
    ['Top Vendor', `${data.topVendor.vendor} (${data.topVendor.count} receipts)`],
    ['Processing Success Rate', `${data.processingStats.successRate}%`]
  ];

  summaryData.forEach(([label, value]) => {
    addText(label + ':', margin, yPosition, { fontSize: 10, bold: true });
    addText(value, margin + 60, yPosition);
    yPosition += 6;
  });

  // Category Breakdown
  addText('CATEGORY BREAKDOWN', margin, yPosition, { fontSize: 14, bold: true });
  yPosition += 10;

  if (data.topCategories && data.topCategories.length > 0) {
    // Create table for categories
    const categoryTableData = data.topCategories.slice(0, 8).map((category: any, index: number) => [
      (index + 1).toString(),
      category.category,
      `${category.total} ${data.currency}`,
      `${category.percentage.toFixed(1)}%`
    ]);

    (pdf as any).autoTable({
      startY: yPosition,
      head: [['Rank', 'Category', 'Amount', 'Percentage']],
      body: categoryTableData,
      theme: 'grid',
      headStyles: { fillColor: [131, 56, 236], textColor: 255 },
      margin: { left: margin, right: margin },
      styles: { fontSize: 9 }
    });

    yPosition = (pdf as any).lastAutoTable.finalY + 15;
  }

  // Check if we need a new page
  if (yPosition > 250) {
    pdf.addPage();
    yPosition = 30;
  }

  // Vendor Analysis
  addText('TOP VENDORS', margin, yPosition, { fontSize: 14, bold: true });
  yPosition += 10;

  if (data.vendorAnalysis && data.vendorAnalysis.length > 0) {
    const vendorTableData = data.vendorAnalysis.slice(0, 8).map((vendor: any, index: number) => [
      (index + 1).toString(),
      vendor.vendor,
      `${vendor.totalSpent} ${data.currency}`,
      vendor.receiptCount.toString(),
      `${vendor.averageAmount} ${data.currency}`
    ]);

    (pdf as any).autoTable({
      startY: yPosition,
      head: [['Rank', 'Vendor', 'Total Spent', 'Receipts', 'Avg Amount']],
      body: vendorTableData,
      theme: 'grid',
      headStyles: { fillColor: [131, 56, 236], textColor: 255 },
      margin: { left: margin, right: margin },
      styles: { fontSize: 9 }
    });

    yPosition = (pdf as any).lastAutoTable.finalY + 15;
  }

  // Period Comparison
  if (yPosition > 220) {
    pdf.addPage();
    yPosition = 30;
  }

  addText('PERIOD COMPARISON', margin, yPosition, { fontSize: 14, bold: true });
  yPosition += 10;

  const comparisonData = [
    ['Current Period', `${data.monthlyComparison.currentMonth} ${data.currency}`],
    ['Previous Period', `${data.monthlyComparison.previousMonth} ${data.currency}`],
    ['Change', `${data.monthlyComparison.percentageChange.toFixed(1)}%`]
  ];

  comparisonData.forEach(([label, value]) => {
    addText(label + ':', margin, yPosition, { fontSize: 10, bold: true });
    addText(value, margin + 60, yPosition);
    yPosition += 6;
  });

  addSeparator();

  // Processing Statistics
  addText('PROCESSING STATISTICS', margin, yPosition, { fontSize: 14, bold: true });
  yPosition += 10;

  const processingData = [
    ['Total Processed', data.processingStats.totalProcessed.toString()],
    ['Successfully Processed', (data.processingStats.totalProcessed - data.processingStats.failed - data.processingStats.pending).toString()],
    ['Failed', data.processingStats.failed.toString()],
    ['Pending', data.processingStats.pending.toString()],
    ['Success Rate', `${data.processingStats.successRate}%`]
  ];

  processingData.forEach(([label, value]) => {
    addText(label + ':', margin, yPosition, { fontSize: 10, bold: true });
    addText(value, margin + 60, yPosition);
    yPosition += 6;
  });

  // Footer
  const footerY = pdf.internal.pageSize.getHeight() - 20;
  addText('Generated by **Receipt**Labs - AI-Powered Receipt Management', pageWidth / 2, footerY, {
    fontSize: 8,
    color: '#666666',
    align: 'center'
  });

  return Buffer.from(pdf.output('arraybuffer'));
}
