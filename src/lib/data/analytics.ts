import { createClient } from '@/lib/supabase/server';

export interface AnalyticsData {
  monthlySpending: number;
  receiptCount: number;
  averageReceiptValue: number;
  topVendor: { vendor: string; count: number };
  spendingTrend: { month: string; total: number }[];
  categoryBreakdown: { category: string; total: number }[];
  currency: string;
}

export interface EnhancedAnalyticsData extends AnalyticsData {
  totalSpending: number;
  processingStats: {
    successRate: number;
    totalProcessed: number;
    failed: number;
    pending: number;
  };
  vendorAnalysis: {
    vendor: string;
    totalSpent: number;
    receiptCount: number;
    averageAmount: number;
  }[];
  paymentMethodBreakdown: { method: string; total: number; count: number }[];
  monthlyComparison: {
    currentMonth: number;
    previousMonth: number;
    percentageChange: number;
  };
  topCategories: { category: string; total: number; percentage: number }[];
  recentActivity: {
    date: string;
    receipts: number;
    amount: number;
  }[];
}

export type TimePeriod = 'month' | 'quarter' | 'year' | 'all';

export interface AnalyticsFilters {
  period: TimePeriod;
  startDate?: string;
  endDate?: string;
  vendor?: string;
  category?: string;
}

export async function getAnalyticsData(): Promise<AnalyticsData> {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return getDefaultAnalyticsData();
  }

  try {
    // First try to get data from user_analytics table for current month
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
    const { data: analyticsRecord } = await supabase
      .from('user_analytics')
      .select('*')
      .eq('user_id', user.id)
      .eq('month_year', currentMonth)
      .single();

    if (analyticsRecord) {
      console.log('Analytics: Using cached analytics data from user_analytics table');

      // Convert stored analytics to our format
      const spendingTrend = await getSpendingTrend(user.id);
      const categoryBreakdown = Object.entries(analyticsRecord.category_breakdown || {})
        .map(([category, total]) => ({ category, total: total as number }))
        .sort((a, b) => b.total - a.total);

      return {
        monthlySpending: analyticsRecord.total_spent || 0,
        receiptCount: analyticsRecord.total_receipts || 0,
        averageReceiptValue: analyticsRecord.average_receipt_amount || 0,
        topVendor: {
          vendor: analyticsRecord.top_vendor || 'No data',
          count: 0 // We don't store count in analytics table
        },
        spendingTrend,
        categoryBreakdown,
        currency: 'KES', // Default to KES for now
      };
    }

    console.log('Analytics: No cached data found, calculating from receipts');
    // Get current month receipts (reuse currentMonth from above)
    const startOfMonth = `${currentMonth}-01`;
    const endOfMonth = new Date(new Date(startOfMonth).getFullYear(), new Date(startOfMonth).getMonth() + 1, 0).toISOString().slice(0, 10);

    console.log('Analytics: Fetching receipts for period:', { startOfMonth, endOfMonth, userId: user.id });

    // Fetch monthly receipts with more detailed logging
    const { data: monthlyReceipts, error } = await supabase
      .from('receipts')
      .select('total_amount, vendor, receipt_date, currency, processing_status')
      .eq('user_id', user.id)
      .gte('receipt_date', startOfMonth)
      .lte('receipt_date', endOfMonth)
      .eq('processing_status', 'completed');

    if (error) {
      console.error('Error fetching monthly receipts:', error);
      return getDefaultAnalyticsData();
    }

    console.log('Analytics: Found monthly receipts:', monthlyReceipts?.length || 0, monthlyReceipts);

    let receipts = monthlyReceipts || [];

    // If no receipts in current month, get all completed receipts for better analytics
    if (receipts.length === 0) {
      console.log('Analytics: No receipts in current month, fetching all completed receipts');
      const { data: allReceipts } = await supabase
        .from('receipts')
        .select('total_amount, vendor, receipt_date, currency, processing_status')
        .eq('user_id', user.id)
        .eq('processing_status', 'completed')
        .order('receipt_date', { ascending: false })
        .limit(100); // Limit to last 100 receipts for performance

      receipts = allReceipts || [];
      console.log('Analytics: Found all-time receipts:', receipts.length);
      console.log('Analytics: Sample receipts data:', receipts.slice(0, 3));
    }

    const monthlySpending = receipts.reduce((sum, receipt) => sum + (receipt.total_amount || 0), 0);
    const receiptCount = receipts.length;
    const averageReceiptValue = receiptCount > 0 ? monthlySpending / receiptCount : 0;

    // Get the most common currency from receipts, default to KES
    const currencies = receipts.map(r => r.currency).filter(Boolean);
    const currency = currencies.length > 0 ? currencies[0] : 'KES';

    console.log('Analytics: Calculated values:', {
      monthlySpending,
      receiptCount,
      averageReceiptValue,
      currency
    });

    // Calculate top vendor
    const vendorCounts = receipts.reduce((acc, receipt) => {
      if (receipt.vendor) {
        acc[receipt.vendor] = (acc[receipt.vendor] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const topVendorEntry = Object.entries(vendorCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    const topVendor = topVendorEntry 
      ? { vendor: topVendorEntry[0], count: topVendorEntry[1] }
      : { vendor: 'No data', count: 0 };

    // Get spending trend for last 6 months
    const spendingTrend = await getSpendingTrend(user.id);

    // Get category breakdown
    const categoryBreakdown = await getCategoryBreakdown(user.id);

    return {
      monthlySpending,
      receiptCount,
      averageReceiptValue,
      topVendor,
      spendingTrend,
      categoryBreakdown,
      currency,
    };
  } catch (error) {
    console.error('Error fetching analytics data:', error);
    return getDefaultAnalyticsData();
  }
}

async function getSpendingTrend(userId: string): Promise<{ month: string; total: number }[]> {
  const supabase = await createClient();
  const months = [];
  
  // Generate last 6 months
  for (let i = 5; i >= 0; i--) {
    const date = new Date();
    date.setMonth(date.getMonth() - i);
    const monthKey = date.toISOString().slice(0, 7); // YYYY-MM format
    const monthName = date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
    months.push({ key: monthKey, name: monthName });
  }

  const trendData = await Promise.all(
    months.map(async ({ key, name }) => {
      const startOfMonth = `${key}-01`;
      const endOfMonth = new Date(new Date(startOfMonth).getFullYear(), new Date(startOfMonth).getMonth() + 1, 0).toISOString().slice(0, 10);

      const { data } = await supabase
        .from('receipts')
        .select('total_amount')
        .eq('user_id', userId)
        .gte('receipt_date', startOfMonth)
        .lte('receipt_date', endOfMonth)
        .eq('processing_status', 'completed');

      const total = data?.reduce((sum, receipt) => sum + (receipt.total_amount || 0), 0) || 0;
      
      return {
        month: name,
        total: total,
      };
    })
  );

  return trendData;
}

/**
 * Enhanced category breakdown calculation from receipts with improved accuracy
 */
function calculateCategoryBreakdownFromReceipts(receipts: any[]): { category: string; total: number; percentage: number }[] {
  const completedReceipts = receipts.filter(r => r.processing_status === 'completed');
  const allItems = completedReceipts.flatMap(r => r.receipt_items || []);

  // Enhanced category calculation with better data validation
  const categoryTotals = allItems.reduce((acc, item) => {
    const category = (item.category?.trim() || 'Other');
    const price = parseFloat(item.total_price) || 0;

    // Only include items with valid prices
    if (price > 0) {
      acc[category] = (acc[category] || 0) + price;
    }
    return acc;
  }, {} as Record<string, number>);

  const totalSpending = Object.values(categoryTotals).reduce((sum, total) => sum + total, 0);

  return Object.entries(categoryTotals)
    .map(([category, total]) => ({
      category,
      total: Math.round(total * 100) / 100,
      percentage: totalSpending > 0 ?
        Math.round((total / totalSpending) * 10000) / 100 : 0,
    }))
    .sort((a, b) => b.total - a.total)
    .slice(0, 10); // Top 10 categories for main breakdown
}

async function getCategoryBreakdown(userId: string): Promise<{ category: string; total: number }[]> {
  const supabase = await createClient();

  // Get receipt items with categories
  const { data: receiptItems } = await supabase
    .from('receipt_items')
    .select(`
      total_price,
      category,
      receipt_id,
      receipts!inner(user_id, processing_status)
    `)
    .eq('receipts.user_id', userId)
    .eq('receipts.processing_status', 'completed');

  if (!receiptItems || receiptItems.length === 0) {
    // If no receipt items with categories, return default categories
    return [
      { category: 'Food & Dining', total: 0 },
      { category: 'Transportation', total: 0 },
      { category: 'Shopping', total: 0 },
      { category: 'Other', total: 0 },
    ];
  }

  // Group by category
  const categoryTotals = receiptItems.reduce((acc, item) => {
    const category = item.category || 'Other';
    acc[category] = (acc[category] || 0) + (item.total_price || 0);
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(categoryTotals)
    .map(([category, total]) => ({ category, total }))
    .sort((a, b) => b.total - a.total)
    .slice(0, 6); // Top 6 categories
}

function getDefaultAnalyticsData(): AnalyticsData {
  return {
    monthlySpending: 0,
    receiptCount: 0,
    averageReceiptValue: 0,
    topVendor: { vendor: 'No data', count: 0 },
    spendingTrend: [],
    categoryBreakdown: [],
    currency: 'KES',
  };
}

/**
 * Get enhanced analytics data with time period filtering
 */
export async function getEnhancedAnalyticsData(filters: AnalyticsFilters): Promise<EnhancedAnalyticsData> {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return getDefaultEnhancedAnalyticsData();
  }

  try {
    const { startDate, endDate } = getDateRange(filters.period, filters.startDate, filters.endDate);

    // Fetch receipts for the specified period
    let query = supabase
      .from('receipts')
      .select(`
        id,
        total_amount,
        vendor,
        receipt_date,
        currency,
        payment_method,
        processing_status,
        created_at,
        receipt_items (
          total_price,
          category,
          description
        )
      `)
      .eq('user_id', user.id);

    if (startDate) query = query.gte('receipt_date', startDate);
    if (endDate) query = query.lte('receipt_date', endDate);
    if (filters.vendor) query = query.eq('vendor', filters.vendor);

    const { data: receipts, error } = await query;

    if (error) {
      console.error('Error fetching enhanced analytics data:', error);
      return getDefaultEnhancedAnalyticsData();
    }

    const receiptList = receipts || [];

    // Calculate basic analytics
    const basicAnalytics = await calculateBasicAnalytics(receiptList, user.id, filters);

    // Calculate enhanced metrics
    const processingStats = calculateProcessingStats(receiptList);
    const vendorAnalysis = calculateVendorAnalysis(receiptList);
    const paymentMethodBreakdown = calculatePaymentMethodBreakdown(receiptList);
    const monthlyComparison = await calculateMonthlyComparison(user.id, filters.period);
    const topCategories = calculateTopCategories(receiptList);
    const recentActivity = await calculateRecentActivity(user.id);

    return {
      ...basicAnalytics,
      totalSpending: receiptList
        .filter(r => r.processing_status === 'completed')
        .reduce((sum, receipt) => sum + (receipt.total_amount || 0), 0),
      processingStats,
      vendorAnalysis,
      paymentMethodBreakdown,
      monthlyComparison,
      topCategories,
      recentActivity,
    };
  } catch (error) {
    console.error('Error fetching enhanced analytics data:', error);
    return getDefaultEnhancedAnalyticsData();
  }
}

function getDefaultEnhancedAnalyticsData(): EnhancedAnalyticsData {
  const basicData = getDefaultAnalyticsData();
  return {
    ...basicData,
    totalSpending: 0,
    processingStats: {
      successRate: 0,
      totalProcessed: 0,
      failed: 0,
      pending: 0,
    },
    vendorAnalysis: [],
    paymentMethodBreakdown: [],
    monthlyComparison: {
      currentMonth: 0,
      previousMonth: 0,
      percentageChange: 0,
    },
    topCategories: [],
    recentActivity: [],
  };
}

/**
 * Get date range based on time period with improved accuracy
 */
function getDateRange(period: TimePeriod, customStart?: string, customEnd?: string): { startDate: string | null; endDate: string | null } {
  if (customStart && customEnd) {
    // Validate custom dates
    const start = new Date(customStart);
    const end = new Date(customEnd);
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      console.warn('Invalid custom date range provided, falling back to period-based calculation');
    } else {
      return { startDate: customStart, endDate: customEnd };
    }
  }

  const now = new Date();
  let startDate: string | null = null;
  let endDate: string | null = null;

  switch (period) {
    case 'month':
      // Current month from 1st to last day
      startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().slice(0, 10);
      endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().slice(0, 10);
      break;
    case 'quarter':
      // Current quarter
      const quarterStart = Math.floor(now.getMonth() / 3) * 3;
      startDate = new Date(now.getFullYear(), quarterStart, 1).toISOString().slice(0, 10);
      endDate = new Date(now.getFullYear(), quarterStart + 3, 0).toISOString().slice(0, 10);
      break;
    case 'year':
      // Current year from Jan 1 to Dec 31
      startDate = new Date(now.getFullYear(), 0, 1).toISOString().slice(0, 10);
      endDate = new Date(now.getFullYear(), 11, 31).toISOString().slice(0, 10);
      break;
    case 'all':
      // No date filtering - get all data
      startDate = null;
      endDate = null;
      break;
  }

  return { startDate, endDate };
}

/**
 * Calculate basic analytics from receipt data with improved accuracy
 */
async function calculateBasicAnalytics(receipts: any[], userId: string, filters: AnalyticsFilters): Promise<AnalyticsData> {
  // Filter for completed receipts only
  const completedReceipts = receipts.filter(r => r.processing_status === 'completed');

  // Calculate spending with proper null handling and currency conversion
  const spendingByReceipt = completedReceipts.map(receipt => {
    const amount = parseFloat(receipt.total_amount) || 0;
    return { amount, currency: receipt.currency || 'KES' };
  });

  // For now, sum all amounts (future: implement currency conversion)
  const monthlySpending = spendingByReceipt.reduce((sum, item) => sum + item.amount, 0);
  const receiptCount = completedReceipts.length;

  // More robust average calculation
  const averageReceiptValue = receiptCount > 0 ?
    Math.round((monthlySpending / receiptCount) * 100) / 100 : 0;

  // Determine primary currency (most frequently used)
  const currencyFrequency = spendingByReceipt.reduce((acc, item) => {
    acc[item.currency] = (acc[item.currency] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const currency = Object.entries(currencyFrequency)
    .sort(([,a], [,b]) => b - a)[0]?.[0] || 'KES';

  // Calculate top vendor with better data validation
  const vendorCounts = completedReceipts.reduce((acc, receipt) => {
    const vendor = receipt.vendor?.trim();
    if (vendor && vendor.length > 0) {
      acc[vendor] = (acc[vendor] || 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  const topVendorEntry = Object.entries(vendorCounts)
    .sort(([,a], [,b]) => b - a)[0];

  const topVendor = topVendorEntry
    ? { vendor: topVendorEntry[0], count: topVendorEntry[1] }
    : { vendor: 'No data', count: 0 };

  // Get spending trend with improved period handling
  const spendingTrend = await getSpendingTrendForPeriod(userId, filters.period);

  // Get category breakdown with enhanced calculation
  const categoryBreakdown = calculateCategoryBreakdownFromReceipts(receipts);

  return {
    monthlySpending: Math.round(monthlySpending * 100) / 100,
    receiptCount,
    averageReceiptValue,
    topVendor,
    spendingTrend,
    categoryBreakdown,
    currency,
  };
}

/**
 * Calculate processing statistics
 */
function calculateProcessingStats(receipts: any[]) {
  const totalProcessed = receipts.length;
  const completed = receipts.filter(r => r.processing_status === 'completed').length;
  const failed = receipts.filter(r => r.processing_status === 'failed').length;
  const pending = receipts.filter(r => r.processing_status === 'pending').length;

  const successRate = totalProcessed > 0 ? (completed / totalProcessed) * 100 : 0;

  return {
    successRate: Math.round(successRate * 100) / 100,
    totalProcessed,
    failed,
    pending,
  };
}

/**
 * Calculate vendor analysis with improved accuracy
 */
function calculateVendorAnalysis(receipts: any[]) {
  const completedReceipts = receipts.filter(r => r.processing_status === 'completed');

  const vendorStats = completedReceipts.reduce((acc, receipt) => {
    const vendor = receipt.vendor?.trim();
    if (vendor && vendor.length > 0) {
      if (!acc[vendor]) {
        acc[vendor] = { totalSpent: 0, receiptCount: 0 };
      }
      const amount = parseFloat(receipt.total_amount) || 0;
      acc[vendor].totalSpent += amount;
      acc[vendor].receiptCount += 1;
    }
    return acc;
  }, {} as Record<string, { totalSpent: number; receiptCount: number }>);

  return Object.entries(vendorStats)
    .map(([vendor, stats]) => ({
      vendor,
      totalSpent: Math.round(stats.totalSpent * 100) / 100,
      receiptCount: stats.receiptCount,
      averageAmount: stats.receiptCount > 0 ?
        Math.round((stats.totalSpent / stats.receiptCount) * 100) / 100 : 0,
    }))
    .sort((a, b) => b.totalSpent - a.totalSpent)
    .slice(0, 10); // Top 10 vendors
}

/**
 * Calculate payment method breakdown
 */
function calculatePaymentMethodBreakdown(receipts: any[]) {
  const completedReceipts = receipts.filter(r => r.processing_status === 'completed');

  const methodStats = completedReceipts.reduce((acc, receipt) => {
    const method = receipt.payment_method || 'Unknown';
    if (!acc[method]) {
      acc[method] = { total: 0, count: 0 };
    }
    acc[method].total += receipt.total_amount || 0;
    acc[method].count += 1;
    return acc;
  }, {} as Record<string, { total: number; count: number }>);

  return Object.entries(methodStats)
    .map(([method, stats]) => ({
      method,
      total: stats.total,
      count: stats.count,
    }))
    .sort((a, b) => b.total - a.total);
}

/**
 * Calculate monthly comparison
 */
async function calculateMonthlyComparison(userId: string, period: TimePeriod) {
  const supabase = await createClient();

  const now = new Date();
  const currentMonth = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().slice(0, 7);
  const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().slice(0, 7);

  const [currentData, previousData] = await Promise.all([
    supabase
      .from('receipts')
      .select('total_amount')
      .eq('user_id', userId)
      .gte('receipt_date', `${currentMonth}-01`)
      .lte('receipt_date', new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().slice(0, 10))
      .eq('processing_status', 'completed'),
    supabase
      .from('receipts')
      .select('total_amount')
      .eq('user_id', userId)
      .gte('receipt_date', `${previousMonth}-01`)
      .lte('receipt_date', new Date(now.getFullYear(), now.getMonth(), 0).toISOString().slice(0, 10))
      .eq('processing_status', 'completed')
  ]);

  const currentTotal = currentData.data?.reduce((sum, r) => {
    const amount = parseFloat(r.total_amount) || 0;
    return sum + amount;
  }, 0) || 0;

  const previousTotal = previousData.data?.reduce((sum, r) => {
    const amount = parseFloat(r.total_amount) || 0;
    return sum + amount;
  }, 0) || 0;

  // Handle edge cases for percentage calculation
  let percentageChange = 0;
  if (previousTotal > 0) {
    percentageChange = ((currentTotal - previousTotal) / previousTotal) * 100;
  } else if (currentTotal > 0) {
    percentageChange = 100; // 100% increase from 0
  }

  return {
    currentMonth: Math.round(currentTotal * 100) / 100,
    previousMonth: Math.round(previousTotal * 100) / 100,
    percentageChange: Math.round(percentageChange * 100) / 100,
  };
}

/**
 * Calculate top categories with percentages
 */
function calculateTopCategories(receipts: any[]) {
  const completedReceipts = receipts.filter(r => r.processing_status === 'completed');
  const allItems = completedReceipts.flatMap(r => r.receipt_items || []);

  // Enhanced category calculation with better data validation
  const categoryTotals = allItems.reduce((acc, item) => {
    const category = (item.category?.trim() || 'Other');
    const price = parseFloat(item.total_price) || 0;

    // Only include items with valid prices
    if (price > 0) {
      acc[category] = (acc[category] || 0) + price;
    }
    return acc;
  }, {} as Record<string, number>);

  const totalSpending = Object.values(categoryTotals).reduce((sum, total) => sum + total, 0);

  return Object.entries(categoryTotals)
    .map(([category, total]) => ({
      category,
      total: Math.round(total * 100) / 100,
      percentage: totalSpending > 0 ?
        Math.round((total / totalSpending) * 10000) / 100 : 0, // More precise percentage
    }))
    .sort((a, b) => b.total - a.total)
    .slice(0, 8); // Top 8 categories
}

/**
 * Calculate recent activity (last 7 days)
 */
async function calculateRecentActivity(userId: string) {
  const supabase = await createClient();

  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

  const { data: recentReceipts } = await supabase
    .from('receipts')
    .select('receipt_date, total_amount, processing_status')
    .eq('user_id', userId)
    .gte('receipt_date', sevenDaysAgo.toISOString().slice(0, 10))
    .eq('processing_status', 'completed')
    .order('receipt_date', { ascending: true });

  // Group by date
  const dailyActivity = (recentReceipts || []).reduce((acc, receipt) => {
    const date = receipt.receipt_date;
    if (!acc[date]) {
      acc[date] = { receipts: 0, amount: 0 };
    }
    acc[date].receipts += 1;
    acc[date].amount += receipt.total_amount || 0;
    return acc;
  }, {} as Record<string, { receipts: number; amount: number }>);

  // Fill in missing dates with zero values
  const activity = [];
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().slice(0, 10);

    activity.push({
      date: dateStr,
      receipts: dailyActivity[dateStr]?.receipts || 0,
      amount: dailyActivity[dateStr]?.amount || 0,
    });
  }

  return activity;
}

/**
 * Get spending trend for specific period
 */
async function getSpendingTrendForPeriod(userId: string, period: TimePeriod): Promise<{ month: string; total: number }[]> {
  const supabase = await createClient();

  let months = [];

  switch (period) {
    case 'month':
      // Last 6 months
      for (let i = 5; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        const monthKey = date.toISOString().slice(0, 7);
        const monthName = date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
        months.push({ key: monthKey, name: monthName });
      }
      break;
    case 'quarter':
      // Last 4 quarters
      for (let i = 3; i >= 0; i--) {
        const date = new Date();
        date.setMonth(date.getMonth() - (i * 3));
        const quarterStart = Math.floor(date.getMonth() / 3) * 3;
        date.setMonth(quarterStart);
        const monthKey = date.toISOString().slice(0, 7);
        const quarterName = `Q${Math.floor(quarterStart / 3) + 1} ${date.getFullYear().toString().slice(-2)}`;
        months.push({ key: monthKey, name: quarterName });
      }
      break;
    case 'year':
      // Last 3 years
      for (let i = 2; i >= 0; i--) {
        const date = new Date();
        date.setFullYear(date.getFullYear() - i);
        const monthKey = date.getFullYear().toString();
        months.push({ key: monthKey, name: monthKey });
      }
      break;
    default:
      return getSpendingTrend(userId);
  }

  const trendData = await Promise.all(
    months.map(async ({ key, name }) => {
      let startDate, endDate;

      if (period === 'year') {
        startDate = `${key}-01-01`;
        endDate = `${key}-12-31`;
      } else {
        startDate = `${key}-01`;
        endDate = new Date(new Date(startDate).getFullYear(), new Date(startDate).getMonth() + 1, 0).toISOString().slice(0, 10);
      }

      const { data } = await supabase
        .from('receipts')
        .select('total_amount')
        .eq('user_id', userId)
        .gte('receipt_date', startDate)
        .lte('receipt_date', endDate)
        .eq('processing_status', 'completed');

      const total = data?.reduce((sum, receipt) => sum + (receipt.total_amount || 0), 0) || 0;

      return { month: name, total };
    })
  );

  return trendData;
}

/**
 * Calculate basic category breakdown from receipts (without percentages)
 */
function calculateBasicCategoryBreakdownFromReceipts(receipts: any[]): { category: string; total: number }[] {
  const completedReceipts = receipts.filter(r => r.processing_status === 'completed');
  const allItems = completedReceipts.flatMap(r => r.receipt_items || []);

  if (allItems.length === 0) {
    return [
      { category: 'Food & Dining', total: 0 },
      { category: 'Transportation', total: 0 },
      { category: 'Shopping', total: 0 },
      { category: 'Other', total: 0 },
    ];
  }

  const categoryTotals = allItems.reduce((acc, item) => {
    const category = item.category || 'Other';
    acc[category] = (acc[category] || 0) + (item.total_price || 0);
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(categoryTotals)
    .map(([category, total]) => ({ category, total }))
    .sort((a, b) => b.total - a.total)
    .slice(0, 6); // Top 6 categories
}