'use client';

import { useState } from 'react';
import { User, Mail, Calendar, Crown, AlertTriangle, Edit2, Save, X, Trash2, LogOut } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { UserData } from './SettingsClient';
import DeleteAccountModal from './DeleteAccountModal';

interface AccountSettingsProps {
  userData: UserData;
  onUserDataUpdate: () => void;
}

export default function AccountSettings({ userData, onUserDataUpdate }: AccountSettingsProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editedName, setEditedName] = useState(userData.full_name || '');
  const [saving, setSaving] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTierInfo = (tier: string) => {
    switch (tier) {
      case 'free':
        return {
          name: 'Free',
          color: 'bg-gray-500/20 text-gray-400',
          limit: '10 receipts/month'
        };
      case 'tier1':
        return {
          name: 'Pro',
          color: 'bg-pink-500/20 text-pink-400',
          limit: '100 receipts/month'
        };
      case 'tier2':
        return {
          name: 'Business',
          color: 'bg-purple-500/20 text-purple-400',
          limit: 'Unlimited receipts'
        };
      default:
        return {
          name: 'Unknown',
          color: 'bg-gray-500/20 text-gray-400',
          limit: 'Unknown'
        };
    }
  };

  const getSubscriptionStatus = (status: string) => {
    switch (status) {
      case 'active':
        return {
          label: 'Active',
          color: 'bg-green-500/20 text-green-400'
        };
      case 'cancelled':
        return {
          label: 'Cancelled',
          color: 'bg-yellow-500/20 text-yellow-400'
        };
      case 'expired':
        return {
          label: 'Expired',
          color: 'bg-red-500/20 text-red-400'
        };
      default:
        return {
          label: 'Inactive',
          color: 'bg-gray-500/20 text-gray-400'
        };
    }
  };

  const handleSaveProfile = async () => {
    try {
      setSaving(true);

      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          full_name: editedName.trim() || null
        }),
      });

      const result = await response.json();

      if (result.success) {
        setIsEditing(false);
        onUserDataUpdate();
      } else {
        throw new Error(result.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      alert('Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setEditedName(userData.full_name || '');
    setIsEditing(false);
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);

      // Use the existing logout route
      const response = await fetch('/auth/signout', {
        method: 'POST',
      });

      if (response.ok) {
        // Redirect to homepage
        window.location.href = '/';
      } else {
        throw new Error('Logout failed');
      }
    } catch (error) {
      console.error('Error logging out:', error);
      alert('Failed to log out. Please try again.');
    } finally {
      setIsLoggingOut(false);
    }
  };

  const tierInfo = getTierInfo(userData.current_tier);
  const subscriptionInfo = getSubscriptionStatus(userData.subscription_status);

  return (
    <div className="space-y-8">
      {/* Profile Information */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white">Profile Information</h3>
          {!isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
            >
              <Edit2 className="w-4 h-4" />
              Edit
            </button>
          )}
        </div>

        <div className="flex items-start gap-6">
          {/* Avatar */}
          <div className="flex-shrink-0">
            <div className="w-20 h-20 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center">
              {userData.avatar_url ? (
                <img
                  src={userData.avatar_url}
                  alt="Profile"
                  className="w-20 h-20 rounded-full object-cover"
                />
              ) : (
                <User className="w-10 h-10 text-white" />
              )}
            </div>
          </div>

          {/* Profile Details */}
          <div className="flex-1 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                Full Name
              </label>
              {isEditing ? (
                <div className="flex items-center gap-3">
                  <input
                    type="text"
                    value={editedName}
                    onChange={(e) => setEditedName(e.target.value)}
                    placeholder="Enter your full name"
                    className="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  />
                  <button
                    onClick={handleSaveProfile}
                    disabled={saving}
                    className="flex items-center gap-2 bg-pink-600 hover:bg-pink-700 disabled:bg-pink-600/50 text-white px-3 py-2 rounded-lg transition-colors"
                  >
                    <Save className="w-4 h-4" />
                    {saving ? 'Saving...' : 'Save'}
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    disabled={saving}
                    className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-600/50 text-white px-3 py-2 rounded-lg transition-colors"
                  >
                    <X className="w-4 h-4" />
                    Cancel
                  </button>
                </div>
              ) : (
                <p className="text-white text-lg">
                  {userData.full_name || 'Not set'}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                <Mail className="w-4 h-4 inline mr-1" />
                Email Address
              </label>
              <p className="text-white">{userData.email}</p>
              <p className="text-gray-500 text-sm mt-1">
                Email cannot be changed. Contact support if needed.
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                <Calendar className="w-4 h-4 inline mr-1" />
                Member Since
              </label>
              <p className="text-white">{formatDate(userData.created_at)}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Information */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <h3 className="text-xl font-semibold text-white mb-6">Subscription</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              <Crown className="w-4 h-4 inline mr-1" />
              Current Plan
            </label>
            <div className="flex items-center gap-3">
              <Badge className={tierInfo.color}>
                {tierInfo.name}
              </Badge>
              <span className="text-gray-400 text-sm">{tierInfo.limit}</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              Status
            </label>
            <Badge className={subscriptionInfo.color}>
              {subscriptionInfo.label}
            </Badge>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              Receipts Processed
            </label>
            <p className="text-white text-lg">{userData.receipts_processed}</p>
          </div>

          {userData.subscription_end_date && (
            <div>
              <label className="block text-sm font-medium text-gray-400 mb-2">
                {userData.subscription_status === 'active' ? 'Next Billing Date' : 'Expires On'}
              </label>
              <p className="text-white">{formatDate(userData.subscription_end_date)}</p>
            </div>
          )}
        </div>

        {userData.current_tier === 'free' && (
          <div className="mt-6 p-4 bg-gradient-to-r from-pink-500/10 to-purple-500/10 border border-pink-500/20 rounded-lg">
            <div className="flex items-start gap-3">
              <Crown className="w-5 h-5 text-pink-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-pink-200 font-medium">Upgrade to Pro</p>
                <p className="text-pink-300 text-sm mt-1">
                  Process more receipts, get priority support, and unlock advanced features.
                </p>
                <button className="mt-3 bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg text-sm transition-colors">
                  View Plans
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Account Actions */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <h3 className="text-xl font-semibold text-white mb-6">Account Actions</h3>

        <div className="space-y-4">
          {/* Logout */}
          <div className="flex items-center justify-between p-4 bg-gray-700/30 border border-gray-600 rounded-lg">
            <div className="flex items-start gap-3">
              <LogOut className="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-white font-medium">Sign Out</p>
                <p className="text-gray-400 text-sm mt-1">
                  Sign out of your account. You can sign back in anytime.
                </p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-600/50 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <LogOut className="w-4 h-4" />
              {isLoggingOut ? 'Signing Out...' : 'Sign Out'}
            </button>
          </div>

          {/* Delete Account */}
          <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-red-200 font-medium">Delete Account</p>
                <p className="text-red-300 text-sm mt-1">
                  Permanently delete your account and all associated data. This action cannot be undone.
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowDeleteModal(true)}
              className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              <Trash2 className="w-4 h-4" />
              Delete Account
            </button>
          </div>
        </div>
      </div>

      {/* Delete Account Modal */}
      {showDeleteModal && (
        <DeleteAccountModal
          userData={userData}
          onClose={() => setShowDeleteModal(false)}
        />
      )}
    </div>
  );
}
