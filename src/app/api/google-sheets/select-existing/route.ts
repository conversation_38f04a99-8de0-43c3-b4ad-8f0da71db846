import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized' 
      }, { status: 401 })
    }

    // Parse request body
    const { sheetId, sheetName, sheetUrl, year } = await request.json()
    
    if (!sheetId || !sheetName || !sheetUrl) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing required fields: sheetId, sheetName, sheetUrl' 
      }, { status: 400 })
    }

    const targetYear = year || new Date().getFullYear()

    // Check if a sheet already exists for this user and year
    const { data: existingSheet } = await supabase
      .from('google_sheets')
      .select('*')
      .eq('user_id', user.id)
      .eq('year', targetYear)
      .single()

    if (existingSheet) {
      // Update existing record
      const { error: updateError } = await supabase
        .from('google_sheets')
        .update({
          sheet_id: sheetId,
          sheet_url: sheetUrl,
          sheet_name: sheetName,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .eq('year', targetYear)

      if (updateError) {
        console.error('Error updating existing sheet:', updateError)
        return NextResponse.json({ 
          success: false, 
          error: 'Failed to update sheet record' 
        }, { status: 500 })
      }
    } else {
      // Create new record
      const { error: insertError } = await supabase
        .from('google_sheets')
        .insert({
          user_id: user.id,
          year: targetYear,
          sheet_id: sheetId,
          sheet_url: sheetUrl,
          sheet_name: sheetName,
          last_row_number: 1, // Assume header row exists
          total_receipts: 0,
        })

      if (insertError) {
        console.error('Error creating sheet record:', insertError)
        return NextResponse.json({ 
          success: false, 
          error: 'Failed to create sheet record' 
        }, { status: 500 })
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Sheet selected successfully',
      data: {
        sheetId,
        sheetName,
        sheetUrl,
        year: targetYear
      }
    })

  } catch (error) {
    console.error('Error in select-existing sheet API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
