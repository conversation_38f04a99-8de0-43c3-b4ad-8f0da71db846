'use client';

import { Calendar, DollarSign, Building, ArrowUpDown, X } from 'lucide-react';
import { Receipt } from './ReceiptHistoryClient';

interface FilterState {
  search: string;
  status: string;
  dateFrom: string;
  dateTo: string;
  amountMin: string;
  amountMax: string;
  vendor: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface ReceiptFiltersProps {
  filters: FilterState;
  onFilterChange: (key: keyof FilterState, value: string) => void;
  receipts: Receipt[];
}

const statusOptions = [
  { value: '', label: 'All Statuses' },
  { value: 'pending', label: 'Pending' },
  { value: 'processing', label: 'Processing' },
  { value: 'completed', label: 'Completed' },
  { value: 'failed', label: 'Failed' },
];

const sortOptions = [
  { value: 'created_at', label: 'Date Created' },
  { value: 'receipt_date', label: 'Receipt Date' },
  { value: 'vendor', label: 'Vendor' },
  { value: 'total_amount', label: 'Amount' },
  { value: 'processing_status', label: 'Status' },
];

export default function ReceiptFilters({ filters, onFilterChange, receipts }: ReceiptFiltersProps) {
  // Get unique vendors for dropdown
  const uniqueVendors = Array.from(
    new Set(receipts.map(r => r.vendor).filter(Boolean))
  ).sort();

  const clearFilters = () => {
    onFilterChange('status', '');
    onFilterChange('dateFrom', '');
    onFilterChange('dateTo', '');
    onFilterChange('amountMin', '');
    onFilterChange('amountMax', '');
    onFilterChange('vendor', '');
  };

  const hasActiveFilters = filters.status || filters.dateFrom || filters.dateTo || 
                          filters.amountMin || filters.amountMax || filters.vendor;

  return (
    <div className="bg-gray-800/50 border border-gray-700 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Filters</h3>
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="flex items-center gap-2 text-gray-400 hover:text-white transition-colors text-sm"
          >
            <X className="w-4 h-4" />
            Clear All
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Status
          </label>
          <select
            value={filters.status}
            onChange={(e) => onFilterChange('status', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          >
            {statusOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Date From */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Calendar className="w-4 h-4 inline mr-1" />
            Date From
          </label>
          <input
            type="date"
            value={filters.dateFrom}
            onChange={(e) => onFilterChange('dateFrom', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          />
        </div>

        {/* Date To */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Calendar className="w-4 h-4 inline mr-1" />
            Date To
          </label>
          <input
            type="date"
            value={filters.dateTo}
            onChange={(e) => onFilterChange('dateTo', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          />
        </div>

        {/* Vendor Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <Building className="w-4 h-4 inline mr-1" />
            Vendor
          </label>
          <select
            value={filters.vendor}
            onChange={(e) => onFilterChange('vendor', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          >
            <option value="">All Vendors</option>
            {uniqueVendors.map((vendor) => (
              <option key={vendor} value={vendor}>
                {vendor}
              </option>
            ))}
          </select>
        </div>

        {/* Amount Min */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <DollarSign className="w-4 h-4 inline mr-1" />
            Min Amount
          </label>
          <input
            type="number"
            step="0.01"
            placeholder="0.00"
            value={filters.amountMin}
            onChange={(e) => onFilterChange('amountMin', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          />
        </div>

        {/* Amount Max */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <DollarSign className="w-4 h-4 inline mr-1" />
            Max Amount
          </label>
          <input
            type="number"
            step="0.01"
            placeholder="0.00"
            value={filters.amountMax}
            onChange={(e) => onFilterChange('amountMax', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          />
        </div>

        {/* Sort By */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            <ArrowUpDown className="w-4 h-4 inline mr-1" />
            Sort By
          </label>
          <select
            value={filters.sortBy}
            onChange={(e) => onFilterChange('sortBy', e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          >
            {sortOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Sort Order */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Order
          </label>
          <select
            value={filters.sortOrder}
            onChange={(e) => onFilterChange('sortOrder', e.target.value as 'asc' | 'desc')}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          >
            <option value="desc">Descending</option>
            <option value="asc">Ascending</option>
          </select>
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-700">
          <div className="flex flex-wrap gap-2">
            {filters.status && (
              <span className="inline-flex items-center gap-1 bg-pink-600/20 text-pink-400 px-2 py-1 rounded text-xs">
                Status: {statusOptions.find(s => s.value === filters.status)?.label}
                <button
                  onClick={() => onFilterChange('status', '')}
                  className="hover:text-pink-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.dateFrom && (
              <span className="inline-flex items-center gap-1 bg-pink-600/20 text-pink-400 px-2 py-1 rounded text-xs">
                From: {filters.dateFrom}
                <button
                  onClick={() => onFilterChange('dateFrom', '')}
                  className="hover:text-pink-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.dateTo && (
              <span className="inline-flex items-center gap-1 bg-pink-600/20 text-pink-400 px-2 py-1 rounded text-xs">
                To: {filters.dateTo}
                <button
                  onClick={() => onFilterChange('dateTo', '')}
                  className="hover:text-pink-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.vendor && (
              <span className="inline-flex items-center gap-1 bg-pink-600/20 text-pink-400 px-2 py-1 rounded text-xs">
                Vendor: {filters.vendor}
                <button
                  onClick={() => onFilterChange('vendor', '')}
                  className="hover:text-pink-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.amountMin && (
              <span className="inline-flex items-center gap-1 bg-pink-600/20 text-pink-400 px-2 py-1 rounded text-xs">
                Min: ${filters.amountMin}
                <button
                  onClick={() => onFilterChange('amountMin', '')}
                  className="hover:text-pink-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            {filters.amountMax && (
              <span className="inline-flex items-center gap-1 bg-pink-600/20 text-pink-400 px-2 py-1 rounded text-xs">
                Max: ${filters.amountMax}
                <button
                  onClick={() => onFilterChange('amountMax', '')}
                  className="hover:text-pink-300"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
