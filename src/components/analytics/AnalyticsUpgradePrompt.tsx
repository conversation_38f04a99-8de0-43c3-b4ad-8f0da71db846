'use client';

import { useState } from 'react';
import { BarChart3, Crown, TrendingUp, Pie<PERSON>hart, Calendar, ArrowRight, Loader2 } from 'lucide-react';
import Link from 'next/link';

interface AnalyticsUpgradePromptProps {
  currentTier: string;
}

export default function AnalyticsUpgradePrompt({ currentTier }: AnalyticsUpgradePromptProps) {
  const [upgrading, setUpgrading] = useState(false);

  const handleUpgrade = async () => {
    try {
      setUpgrading(true);

      // Initialize payment for Professional tier
      const response = await fetch('/api/subscription/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ targetTier: 'professional' }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();

      if (result.success && result.data?.authorization_url) {
        // Redirect to Paystack payment page
        window.location.href = result.data.authorization_url;
      } else {
        throw new Error(result.error || 'Failed to get payment URL');
      }
    } catch (error) {
      console.error('Upgrade error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to start upgrade process';
      alert(`Upgrade failed: ${errorMessage}`);
    } finally {
      setUpgrading(false);
    }
  };

  const analyticsFeatures = [
    {
      icon: BarChart3,
      title: 'Spending Trends',
      description: 'Track your spending patterns over time with detailed charts and insights'
    },
    {
      icon: PieChart,
      title: 'Category Breakdown',
      description: 'See where your money goes with comprehensive category analysis'
    },
    {
      icon: TrendingUp,
      title: 'Monthly Reports',
      description: 'Get detailed monthly summaries and year-over-year comparisons'
    },
    {
      icon: Calendar,
      title: 'Custom Date Ranges',
      description: 'Analyze any time period with flexible date range selection'
    }
  ];

  return (
    <div className="max-w-4xl mx-auto">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <div className="w-20 h-20 bg-gradient-to-br from-pink-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
          <BarChart3 className="w-10 h-10 text-white" />
        </div>
        <h2 className="text-3xl font-bold text-white mb-4">
          Unlock Powerful Analytics
        </h2>
        <p className="text-xl text-gray-400 max-w-2xl mx-auto">
          Get deep insights into your spending patterns, track trends, and make data-driven financial decisions with our advanced analytics suite.
        </p>
      </div>

      {/* Features Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
        {analyticsFeatures.map((feature, index) => (
          <div key={index} className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
            <div className="w-12 h-12 bg-pink-600/20 rounded-lg flex items-center justify-center mb-4">
              <feature.icon className="w-6 h-6 text-pink-400" />
            </div>
            <h3 className="text-lg font-semibold text-white mb-2">
              {feature.title}
            </h3>
            <p className="text-gray-400">
              {feature.description}
            </p>
          </div>
        ))}
      </div>

      {/* Upgrade CTA */}
      <div className="bg-gradient-to-r from-pink-600/20 to-purple-600/20 border border-pink-500/30 rounded-xl p-8 text-center">
        <Crown className="w-12 h-12 text-pink-400 mx-auto mb-4" />
        <h3 className="text-2xl font-bold text-white mb-2">
          Upgrade to Professional
        </h3>
        <p className="text-gray-300 mb-6">
          Get access to analytics plus 500 receipts per month, 6-month data retention, and priority support.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            onClick={handleUpgrade}
            disabled={upgrading}
            className="flex items-center gap-2 bg-pink-600 hover:bg-pink-700 disabled:bg-pink-600/50 text-white font-medium px-8 py-3 rounded-lg transition-colors"
          >
            {upgrading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Crown className="w-5 h-5" />
                Upgrade Now - KES 2,999/month
                <ArrowRight className="w-5 h-5" />
              </>
            )}
          </button>
          
          <Link
            href="/dashboard/settings?tab=billing"
            className="text-gray-400 hover:text-white transition-colors"
          >
            View all plans
          </Link>
        </div>
      </div>

      {/* Preview Section */}
      <div className="mt-12 bg-gray-800/30 border border-gray-700 rounded-xl p-8">
        <h3 className="text-xl font-semibold text-white mb-4 text-center">
          Preview: What You'll Get
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
          <div>
            <div className="text-3xl font-bold text-pink-400 mb-2">📊</div>
            <p className="text-gray-300">Interactive Charts</p>
          </div>
          <div>
            <div className="text-3xl font-bold text-purple-400 mb-2">📈</div>
            <p className="text-gray-300">Trend Analysis</p>
          </div>
          <div>
            <div className="text-3xl font-bold text-blue-400 mb-2">📋</div>
            <p className="text-gray-300">Detailed Reports</p>
          </div>
        </div>
      </div>

      {/* Current Tier Info */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500">
          Current plan: <span className="capitalize font-medium">{currentTier}</span>
        </p>
      </div>
    </div>
  );
}
