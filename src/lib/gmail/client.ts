import { gmail_v1 } from 'googleapis';
import { createAuthenticatedGmailClient } from './auth';
import { createClient } from '@/lib/supabase/server';

export interface GmailMessage {
  id: string;
  threadId: string;
  subject: string;
  sender: string;
  receivedDate: Date;
  snippet: string;
  attachments: GmailAttachment[];
  bodyText?: string;
}

export interface GmailAttachment {
  attachmentId: string;
  filename: string;
  mimeType: string;
  size: number;
}

export interface GmailSearchResult {
  messages: GmailMessage[];
  nextPageToken?: string;
  totalCount: number;
}

export interface GmailProcessingResult {
  success: boolean;
  processedCount: number;
  errors: string[];
  receiptsCreated: string[]; // Receipt IDs
}

/**
 * Search Gmail for messages containing receipt-related content
 */
export async function searchGmailForReceipts(
  userId: string,
  options: {
    keywords?: string[];
    dateAfter?: Date;
    dateBefore?: Date;
    maxResults?: number;
    pageToken?: string;
  } = {}
): Promise<GmailSearchResult> {
  try {
    const { gmail } = await createAuthenticatedGmailClient(userId);
    
    // Build search query
    const queryParts: string[] = [];
    
    // Add keywords (default receipt-related terms)
    const keywords = options.keywords || ['receipt', 'invoice', 'purchase', 'order confirmation'];
    if (keywords.length > 0) {
      queryParts.push(`(${keywords.map(k => `"${k}"`).join(' OR ')})`);
    }
    
    // Add date filters
    if (options.dateAfter) {
      const dateStr = options.dateAfter.toISOString().split('T')[0]; // YYYY-MM-DD
      queryParts.push(`after:${dateStr}`);
    }
    
    if (options.dateBefore) {
      const dateStr = options.dateBefore.toISOString().split('T')[0];
      queryParts.push(`before:${dateStr}`);
    }
    
    // Only search in inbox and exclude sent items
    queryParts.push('in:inbox');
    queryParts.push('-in:sent');
    
    // Look for messages with attachments (likely to have receipt files)
    queryParts.push('has:attachment');
    
    const query = queryParts.join(' ');
    console.log('Gmail search query:', query);
    
    // Search messages
    const searchResponse = await gmail.users.messages.list({
      userId: 'me',
      q: query,
      maxResults: options.maxResults || 50,
      pageToken: options.pageToken,
    });
    
    const messageIds = searchResponse.data.messages || [];
    const messages: GmailMessage[] = [];
    
    // Get detailed message data
    for (const messageRef of messageIds) {
      if (!messageRef.id) continue;
      
      try {
        const messageDetail = await gmail.users.messages.get({
          userId: 'me',
          id: messageRef.id,
          format: 'full',
        });
        
        const message = parseGmailMessage(messageDetail.data);
        if (message) {
          messages.push(message);
        }
      } catch (error) {
        console.error(`Error fetching message ${messageRef.id}:`, error);
      }
    }
    
    return {
      messages,
      nextPageToken: searchResponse.data.nextPageToken || undefined,
      totalCount: messages.length,
    };
  } catch (error) {
    console.error('Error searching Gmail for receipts:', error);
    throw error;
  }
}

/**
 * Parse Gmail message data into our format
 */
function parseGmailMessage(message: gmail_v1.Schema$Message): GmailMessage | null {
  try {
    if (!message.id || !message.threadId) return null;
    
    const headers = message.payload?.headers || [];
    const subject = headers.find(h => h.name?.toLowerCase() === 'subject')?.value || '';
    const from = headers.find(h => h.name?.toLowerCase() === 'from')?.value || '';
    const date = headers.find(h => h.name?.toLowerCase() === 'date')?.value || '';
    
    // Parse sender email from "Name <email>" format
    const senderMatch = from.match(/<([^>]+)>/) || from.match(/([^\s]+@[^\s]+)/);
    const sender = senderMatch ? senderMatch[1] : from;
    
    // Parse date
    const receivedDate = date ? new Date(date) : new Date();
    
    // Extract attachments
    const attachments = extractAttachments(message.payload);
    
    // Only process messages with attachments (likely to have receipt files)
    if (attachments.length === 0) {
      return null;
    }
    
    return {
      id: message.id,
      threadId: message.threadId,
      subject,
      sender,
      receivedDate,
      snippet: message.snippet || '',
      attachments,
    };
  } catch (error) {
    console.error('Error parsing Gmail message:', error);
    return null;
  }
}

/**
 * Extract attachments from Gmail message payload
 */
function extractAttachments(payload: gmail_v1.Schema$MessagePart | undefined): GmailAttachment[] {
  const attachments: GmailAttachment[] = [];
  
  if (!payload) return attachments;
  
  // Check current part for attachment
  if (payload.body?.attachmentId && payload.filename) {
    const mimeType = payload.mimeType || 'application/octet-stream';
    
    // Only include image and PDF attachments (likely receipts)
    if (mimeType.startsWith('image/') || mimeType === 'application/pdf') {
      attachments.push({
        attachmentId: payload.body.attachmentId,
        filename: payload.filename,
        mimeType,
        size: payload.body.size || 0,
      });
    }
  }
  
  // Recursively check parts
  if (payload.parts) {
    for (const part of payload.parts) {
      attachments.push(...extractAttachments(part));
    }
  }
  
  return attachments;
}

/**
 * Download Gmail attachment
 */
export async function downloadGmailAttachment(
  userId: string,
  messageId: string,
  attachmentId: string
): Promise<{ success: boolean; buffer?: Buffer; mimeType?: string; error?: string }> {
  try {
    const { gmail } = await createAuthenticatedGmailClient(userId);
    
    const attachment = await gmail.users.messages.attachments.get({
      userId: 'me',
      messageId,
      id: attachmentId,
    });
    
    if (!attachment.data.data) {
      return {
        success: false,
        error: 'No attachment data received'
      };
    }
    
    // Decode base64url data
    const buffer = Buffer.from(attachment.data.data, 'base64url');
    
    return {
      success: true,
      buffer,
      mimeType: attachment.data.mimeType || undefined
    };
  } catch (error) {
    console.error('Error downloading Gmail attachment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get user's Gmail connection status
 */
export async function getGmailStatus(userId: string) {
  try {
    const supabase = await createClient();
    const { data: user, error } = await supabase
      .from('users')
      .select(`
        gmail_connected,
        gmail_email,
        gmail_processing_enabled,
        gmail_processing_time,
        gmail_last_processed_date,
        gmail_keywords
      `)
      .eq('id', userId)
      .single();

    if (error || !user) {
      return {
        connected: false,
        email: null,
        processingEnabled: false,
        processingTime: '09:00:00',
        lastProcessedDate: null,
        keywords: 'receipt,invoice,purchase,order confirmation'
      };
    }

    return {
      connected: user.gmail_connected || false,
      email: user.gmail_email,
      processingEnabled: user.gmail_processing_enabled || false,
      processingTime: user.gmail_processing_time || '09:00:00',
      lastProcessedDate: user.gmail_last_processed_date,
      keywords: user.gmail_keywords || 'receipt,invoice,purchase,order confirmation'
    };
  } catch (error) {
    console.error('Error getting Gmail status:', error);
    return {
      connected: false,
      email: null,
      processingEnabled: false,
      processingTime: '09:00:00',
      lastProcessedDate: null,
      keywords: 'receipt,invoice,purchase,order confirmation'
    };
  }
}

/**
 * Update Gmail processing settings
 */
export async function updateGmailSettings(
  userId: string,
  settings: {
    processingEnabled?: boolean;
    processingTime?: string;
    keywords?: string;
  }
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();
    
    const updateData: any = {};
    if (settings.processingEnabled !== undefined) {
      updateData.gmail_processing_enabled = settings.processingEnabled;
    }
    if (settings.processingTime !== undefined) {
      updateData.gmail_processing_time = settings.processingTime;
    }
    if (settings.keywords !== undefined) {
      updateData.gmail_keywords = settings.keywords;
    }
    
    const { error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', userId);

    if (error) {
      throw error;
    }

    return { success: true };
  } catch (error) {
    console.error('Error updating Gmail settings:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Test Gmail connection by making a simple API call
 */
export async function testGmailConnection(userId: string): Promise<boolean> {
  try {
    const { gmail } = await createAuthenticatedGmailClient(userId);
    
    // Try to get user profile (lightweight test)
    await gmail.users.getProfile({ userId: 'me' });
    
    return true;
  } catch (error) {
    console.error('Gmail connection test failed:', error);
    return false;
  }
}
