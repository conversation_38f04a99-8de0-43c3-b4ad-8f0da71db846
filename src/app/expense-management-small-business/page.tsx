import { createClient } from '@/lib/supabase/server'
import { redirect } from 'next/navigation'
import { Metadata } from 'next'
import Header from '@/components/landing/Header'
import HeroSection from '@/components/landing/HeroSection'
import FeaturesSection from '@/components/landing/FeaturesSection'
import BenefitsSection from '@/components/landing/BenefitsSection'
import HowItWorksSection from '@/components/landing/HowItWorksSection'
import TestimonialsSection from '@/components/landing/TestimonialsSection'
import PricingSection from '@/components/landing/PricingSection'
import FAQSection from '@/components/landing/FAQSection'
import CTASection from '@/components/landing/CTASection'
import Footer from '@/components/landing/Footer'
import FloatingCTA from '@/components/homepage/FloatingCTA'
import { expenseManagementContent } from '@/lib/data/expense-management-content'

export const metadata: Metadata = {
  title: "Streamline Expense Management for Your Small Business | ReceiptLabs",
  description: "Transform your small business expense tracking with AI-powered receipt processing. Automatically extract data and organize into Google Sheets. 99% accuracy, instant processing. Start free!",
  keywords: "expense management for small business, small business expense tracking, automated receipt processing, Google Sheets expense tracker, small business accounting, receipt scanner for business, AI receipt processing, expense automation",
  openGraph: {
    title: "Streamline Expense Management for Your Small Business | ReceiptLabs",
    description: "Stop manual data entry. AI-powered expense management specifically designed for small businesses. Free plan available.",
    type: "website",
    url: "https://receiptlabs.com/expense-management-small-business",
    siteName: "ReceiptLabs",
    images: [
      {
        url: "https://receiptlabs.com/og-expense-management.jpg",
        width: 1200,
        height: 630,
        alt: "ReceiptLabs Expense Management for Small Business"
      }
    ]
  },
  twitter: {
    card: "summary_large_image",
    title: "Expense Management for Small Business | ReceiptLabs",
    description: "Transform your small business expense tracking with AI. Automatically extract receipt data and organize into Google Sheets.",
    images: ["https://receiptlabs.com/og-expense-management.jpg"]
  },
  alternates: {
    canonical: "https://receiptlabs.com/expense-management-small-business",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default async function ExpenseManagementPage() {
  const supabase = await createClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  // If user is logged in, redirect to dashboard
  if (user) {
    redirect('/dashboard')
  }

  // Comprehensive structured data
  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "WebPage",
        "@id": "https://receiptlabs.com/expense-management-small-business",
        "url": "https://receiptlabs.com/expense-management-small-business",
        "name": "Expense Management for Small Business - ReceiptLabs",
        "description": "AI-powered expense management solution specifically designed for small businesses. Automate receipt processing with 99% accuracy.",
        "isPartOf": {
          "@id": "https://receiptlabs.com"
        },
        "primaryImageOfPage": {
          "@type": "ImageObject",
          "url": "https://receiptlabs.com/og-expense-management.jpg"
        },
        "datePublished": "2025-01-16",
        "dateModified": "2025-01-16"
      },
      {
        "@type": "Organization",
        "@id": "https://receiptlabs.com",
        "name": "ReceiptLabs",
        "url": "https://receiptlabs.com",
        "logo": {
          "@type": "ImageObject",
          "url": "https://receiptlabs.com/receiptlabs-logo.svg"
        },
        "description": "AI-powered receipt management and expense tracking for businesses",
        "foundingDate": "2024",
        "contactPoint": {
          "@type": "ContactPoint",
          "contactType": "customer service",
          "url": "https://receiptlabs.com/contact"
        }
      },
      {
        "@type": "SoftwareApplication",
        "name": "ReceiptLabs",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "description": "AI-powered expense management for small businesses with automatic receipt processing and Google Sheets integration",
        "offers": [
          {
            "@type": "Offer",
            "name": "Free Plan",
            "price": "0",
            "priceCurrency": "KES",
            "description": "10 receipts per month with Google Sheets integration"
          },
          {
            "@type": "Offer",
            "name": "Professional Plan", 
            "price": "4999",
            "priceCurrency": "KES",
            "description": "500 receipts per month with advanced features"
          },
          {
            "@type": "Offer",
            "name": "Business Plan",
            "price": "6999", 
            "priceCurrency": "KES",
            "description": "Unlimited receipts with Gmail auto-processing"
          }
        ],
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": "4.9",
          "reviewCount": "500",
          "bestRating": "5",
          "worstRating": "1"
        }
      },
      {
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": "https://receiptlabs.com"
          },
          {
            "@type": "ListItem", 
            "position": 2,
            "name": "Expense Management for Small Business",
            "item": "https://receiptlabs.com/expense-management-small-business"
          }
        ]
      }
    ]
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      {/* Comprehensive Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      
      <Header />
      
      <main>
        <HeroSection 
          headline={expenseManagementContent.hero.headline}
          subheadline={expenseManagementContent.hero.subheadline}
          trustIndicators={expenseManagementContent.hero.trustIndicators}
          primaryCTA={expenseManagementContent.hero.primaryCTA}
          secondaryCTA={expenseManagementContent.hero.secondaryCTA}
        />
        
        <FeaturesSection features={expenseManagementContent.features} />
        
        <BenefitsSection benefits={expenseManagementContent.benefits} />
        
        <HowItWorksSection />
        
        <TestimonialsSection testimonials={expenseManagementContent.testimonials} />
        
        <PricingSection pricing={expenseManagementContent.pricing} />
        
        <FAQSection faqs={expenseManagementContent.faqs} />
        
        <CTASection />
      </main>
      
      <Footer />
      
      <FloatingCTA />
    </div>
  )
}