import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { setUserGoogleDriveFolder } from '@/lib/google-drive/client';

export async function POST(request: NextRequest) {
  try {
    const { folderId, folderName } = await request.json();

    if (!folderId || !folderName) {
      return NextResponse.json(
        { success: false, error: 'Folder ID and name are required' },
        { status: 400 }
      );
    }

    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has connected Google Drive
    const { data: userData } = await supabase
      .from('users')
      .select('google_drive_connected')
      .eq('id', user.id)
      .single();

    if (!userData?.google_drive_connected) {
      return NextResponse.json(
        { success: false, error: 'Google Drive not connected' },
        { status: 400 }
      );
    }

    // Set the user's selected folder
    const success = await setUserGoogleDriveFolder(user.id, folderId, folderName);

    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Failed to set Google Drive folder' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: { folderId, folderName }
    });
  } catch (error) {
    console.error('Error setting Google Drive folder:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to set Google Drive folder' },
      { status: 500 }
    );
  }
}
