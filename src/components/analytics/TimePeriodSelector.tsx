'use client';

import { useState } from 'react';
import { Calendar, ChevronDown } from 'lucide-react';
import { TimePeriod, AnalyticsFilters } from '@/lib/data/analytics';

interface TimePeriodSelectorProps {
  filters: AnalyticsFilters;
  onFiltersChange: (filters: AnalyticsFilters) => void;
}

const periodOptions = [
  { value: 'month' as TimePeriod, label: 'This Month', description: 'Current month data' },
  { value: 'quarter' as TimePeriod, label: 'This Quarter', description: 'Current quarter data' },
  { value: 'year' as TimePeriod, label: 'This Year', description: 'Current year data' },
  { value: 'all' as TimePeriod, label: 'All Time', description: 'All available data' },
];

export default function TimePeriodSelector({ filters, onFiltersChange }: TimePeriodSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showCustomRange, setShowCustomRange] = useState(false);

  const selectedOption = periodOptions.find(option => option.value === filters.period) || periodOptions[0];

  const handlePeriodChange = (period: TimePeriod) => {
    onFiltersChange({
      ...filters,
      period,
      startDate: undefined,
      endDate: undefined,
    });
    setIsOpen(false);
    setShowCustomRange(false);
  };

  const handleCustomRangeToggle = () => {
    setShowCustomRange(!showCustomRange);
    setIsOpen(false);
  };

  const handleCustomDateChange = (field: 'startDate' | 'endDate', value: string) => {
    onFiltersChange({
      ...filters,
      period: 'all', // Set to 'all' when using custom dates
      [field]: value,
    });
  };

  return (
    <div className="space-y-4">
      {/* Period Selector */}
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center justify-between w-full px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white hover:bg-gray-750 transition-colors duration-200"
        >
          <div className="flex items-center space-x-3">
            <Calendar className="w-5 h-5 text-gray-400" />
            <div className="text-left">
              <div className="font-medium">{selectedOption.label}</div>
              <div className="text-sm text-gray-400">{selectedOption.description}</div>
            </div>
          </div>
          <ChevronDown className={`w-5 h-5 text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10">
            <div className="py-2">
              {periodOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handlePeriodChange(option.value)}
                  className={`w-full px-4 py-3 text-left hover:bg-gray-700 transition-colors duration-200 ${
                    filters.period === option.value ? 'bg-gray-700 text-accent-pink' : 'text-white'
                  }`}
                >
                  <div className="font-medium">{option.label}</div>
                  <div className="text-sm text-gray-400">{option.description}</div>
                </button>
              ))}
              <hr className="border-gray-700 my-2" />
              <button
                onClick={handleCustomRangeToggle}
                className="w-full px-4 py-3 text-left hover:bg-gray-700 transition-colors duration-200 text-white"
              >
                <div className="font-medium">Custom Range</div>
                <div className="text-sm text-gray-400">Select specific dates</div>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Custom Date Range */}
      {showCustomRange && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-800/50 border border-gray-700 rounded-lg">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Start Date
            </label>
            <input
              type="date"
              value={filters.startDate || ''}
              onChange={(e) => handleCustomDateChange('startDate', e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-accent-pink focus:border-transparent"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              End Date
            </label>
            <input
              type="date"
              value={filters.endDate || ''}
              onChange={(e) => handleCustomDateChange('endDate', e.target.value)}
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:ring-2 focus:ring-accent-pink focus:border-transparent"
            />
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {(filters.startDate || filters.endDate || filters.vendor || filters.category) && (
        <div className="flex flex-wrap gap-2">
          {filters.startDate && filters.endDate && (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-accent-pink/20 text-accent-pink border border-accent-pink/30">
              {filters.startDate} to {filters.endDate}
              <button
                onClick={() => onFiltersChange({ ...filters, startDate: undefined, endDate: undefined })}
                className="ml-2 hover:text-white"
              >
                ×
              </button>
            </span>
          )}
          {filters.vendor && (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-accent-purple/20 text-accent-purple border border-accent-purple/30">
              Vendor: {filters.vendor}
              <button
                onClick={() => onFiltersChange({ ...filters, vendor: undefined })}
                className="ml-2 hover:text-white"
              >
                ×
              </button>
            </span>
          )}
          {filters.category && (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-400 border border-green-500/30">
              Category: {filters.category}
              <button
                onClick={() => onFiltersChange({ ...filters, category: undefined })}
                className="ml-2 hover:text-white"
              >
                ×
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
}
