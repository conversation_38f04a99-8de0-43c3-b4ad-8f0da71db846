import { createClient } from '@/lib/supabase/server';
import { createClient as createServiceClient } from '@supabase/supabase-js';
import { searchGmailForReceipts, downloadGmailAttachment, GmailMessage } from './client';
import { batchAnalyzeEmailsForReceipts, EmailAnalysisResult } from './receipt-detector';
import { createProcessReceiptJob } from '@/lib/jobs';
import { addJobToQueue } from '@/lib/queue';
import { logGmailProcessing } from './monitoring';
import { v4 as uuidv4 } from 'uuid';

export interface GmailProcessingResult {
  success: boolean;
  userId: string;
  processedEmails: number;
  receiptsFound: number;
  receiptsCreated: string[]; // Receipt IDs
  errors: string[];
  processingTime: number;
}

export interface GmailProcessingOptions {
  userId: string;
  dateAfter?: Date;
  dateBefore?: Date;
  maxEmails?: number;
  keywords?: string[];
  minConfidence?: number;
}

/**
 * Process Gmail for a specific user - INTEGRATES WITH EXISTING PIPELINE
 */
export async function processGmailForUser(options: GmailProcessingOptions): Promise<GmailProcessingResult> {
  const startTime = Date.now();
  const { userId, dateAfter, dateBefore, maxEmails = 50, keywords, minConfidence = 60 } = options;
  
  console.log(`🚀 Starting Gmail processing for user ${userId}`);
  
  const result: GmailProcessingResult = {
    success: false,
    userId,
    processedEmails: 0,
    receiptsFound: 0,
    receiptsCreated: [],
    errors: [],
    processingTime: 0
  };

  try {
    const supabase = await createClient();

    // Create service role client for storage operations (bypasses RLS)
    const serviceSupabase = createServiceClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Verify user has Business tier access
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('current_tier, subscription_status, gmail_connected, gmail_keywords')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      throw new Error('User not found');
    }

    if (user.current_tier !== 'business' || user.subscription_status !== 'active') {
      throw new Error('Business plan required for Gmail processing');
    }

    if (!user.gmail_connected) {
      throw new Error('Gmail not connected for this user');
    }

    // Use user's keywords or provided keywords
    const searchKeywords = keywords || (user.gmail_keywords ? user.gmail_keywords.split(',').map(k => k.trim()) : undefined);

    console.log(`📧 Searching Gmail with keywords: ${searchKeywords?.join(', ')}`);

    // Step 1: Search Gmail for potential receipt emails
    const searchResult = await searchGmailForReceipts(userId, {
      keywords: searchKeywords,
      dateAfter,
      dateBefore,
      maxResults: maxEmails
    });

    result.processedEmails = searchResult.messages.length;
    console.log(`📬 Found ${searchResult.messages.length} emails to analyze`);

    if (searchResult.messages.length === 0) {
      result.success = true;
      result.processingTime = Date.now() - startTime;
      return result;
    }

    // Step 2: Use Gemini to analyze emails for receipt content
    console.log('🤖 Analyzing emails with Gemini for receipt detection...');
    const analysisResults = await batchAnalyzeEmailsForReceipts(searchResult.messages, {
      minConfidence,
      maxConcurrent: 3 // Limit concurrent Gemini calls
    });

    result.receiptsFound = analysisResults.length;
    console.log(`✅ Found ${analysisResults.length} receipt emails`);

    // Step 3: Process each receipt email - FEED INTO EXISTING PIPELINE
    for (const analysis of analysisResults) {
      try {
        await processReceiptEmail(userId, analysis, searchResult.messages, supabase, serviceSupabase);
        result.receiptsCreated.push(analysis.messageId);
      } catch (error) {
        console.error(`❌ Failed to process receipt email ${analysis.messageId}:`, error);
        result.errors.push(`Failed to process email ${analysis.messageId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Step 4: Update user's last processed date and time
    await supabase
      .from('users')
      .update({ gmail_last_processed_date: new Date().toISOString() })
      .eq('id', userId);

    result.success = true;
    result.processingTime = Date.now() - startTime;

    console.log(`🎉 Gmail processing complete for user ${userId}: ${result.receiptsCreated.length} receipts created in ${result.processingTime}ms`);

    // Log processing results for monitoring
    await logGmailProcessing({
      user_id: userId,
      processing_date: new Date().toISOString().split('T')[0],
      emails_processed: result.processedEmails,
      receipts_found: result.receiptsFound,
      receipts_created: result.receiptsCreated.length,
      processing_time_ms: result.processingTime,
      success: true,
      errors: result.errors.length > 0 ? result.errors : undefined,
      source: 'cron'
    });

    return result;

  } catch (error) {
    console.error(`❌ Gmail processing failed for user ${userId}:`, error);
    result.errors.push(error instanceof Error ? error.message : 'Unknown error');
    result.processingTime = Date.now() - startTime;

    // Log failed processing for monitoring
    await logGmailProcessing({
      user_id: userId,
      processing_date: new Date().toISOString().split('T')[0],
      emails_processed: result.processedEmails,
      receipts_found: result.receiptsFound,
      receipts_created: result.receiptsCreated.length,
      processing_time_ms: result.processingTime,
      success: false,
      errors: result.errors,
      source: 'cron'
    });

    return result;
  }
}

/**
 * Process a single receipt email - INTEGRATES WITH EXISTING RECEIPT PIPELINE
 */
async function processReceiptEmail(
  userId: string,
  analysis: EmailAnalysisResult,
  allMessages: GmailMessage[],
  supabase: any,
  serviceSupabase: any
): Promise<void> {
  console.log(`📧 Processing receipt email: ${analysis.messageId}`);

  // Find the original message
  const message = allMessages.find(m => m.id === analysis.messageId);
  if (!message) {
    throw new Error('Original message not found');
  }

  // Check if we've already processed this Gmail message (prevent duplicates)
  const { data: existingReceipt } = await supabase
    .from('receipts')
    .select('id')
    .eq('gmail_message_id', message.id)
    .single();

  if (existingReceipt) {
    console.log(`⏭️ Skipping already processed message: ${message.id}`);
    return;
  }

  // Process each receipt attachment
  for (const attachment of analysis.receiptAttachments) {
    try {
      console.log(`📎 Processing attachment: ${attachment.filename}`);

      // Download attachment from Gmail
      const downloadResult = await downloadGmailAttachment(userId, message.id, attachment.attachmentId);
      
      if (!downloadResult.success || !downloadResult.buffer) {
        console.error(`❌ Failed to download attachment ${attachment.filename}`);
        continue;
      }

      // Upload to Supabase Storage using service role (bypasses RLS)
      const fileName = `gmail_${message.id}_${attachment.filename}`;
      const filePath = `${userId}/${new Date().getFullYear()}/${uuidv4()}_${fileName}`;

      const { data: uploadData, error: uploadError } = await serviceSupabase.storage
        .from('receipts')
        .upload(filePath, downloadResult.buffer, {
          contentType: attachment.mimeType,
          upsert: false
        });

      if (uploadError) {
        console.error(`❌ Failed to upload attachment to storage:`, uploadError);
        continue;
      }

      // Create receipt record (same structure as existing uploads)
      const { data: receipt, error: receiptError } = await supabase
        .from('receipts')
        .insert({
          user_id: userId,
          original_file_name: attachment.filename,
          file_path: uploadData.path,
          file_size: attachment.size,
          mime_type: attachment.mimeType,
          processing_status: 'pending',
          source: 'gmail_auto', // Mark as Gmail auto-processed
          // Gmail-specific metadata
          gmail_message_id: message.id,
          gmail_thread_id: message.threadId,
          gmail_subject: message.subject,
          gmail_sender: message.sender,
          gmail_received_date: message.receivedDate.toISOString()
        })
        .select('id')
        .single();

      if (receiptError) {
        console.error(`❌ Failed to create receipt record:`, receiptError);
        continue;
      }

      console.log(`✅ Created receipt record: ${receipt.id}`);

      // **CRITICAL: FEED INTO EXISTING PIPELINE**
      // Create processing job using existing job system
      const job = createProcessReceiptJob({
        receiptId: receipt.id,
        userId: userId,
        priority: 'normal', // Normal priority for Gmail auto-processing
        imageUrl: uploadData.path,
        fileName: attachment.filename,
        fileSize: attachment.size
      });

      // Add to existing Redis queue
      await addJobToQueue(job);

      // Update receipt with job ID (same as existing flow)
      await supabase
        .from('receipts')
        .update({
          redis_job_id: job.id,
          processing_status: 'processing'
        })
        .eq('id', receipt.id);

      console.log(`🚀 Created processing job ${job.id} for Gmail receipt ${receipt.id}`);

      // **TRIGGER EXISTING WEBHOOK SYSTEM**
      // This ensures the receipt goes through the same processing pipeline
      try {
        const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
        const pipelineResponse = await fetch(`${baseUrl}/api/pipeline`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'User-Agent': 'Gmail-Processor/1.0'
          }
        });

        if (pipelineResponse.ok) {
          console.log('⚡ Triggered existing pipeline for Gmail receipt');
        } else {
          console.warn('⚠️ Pipeline trigger failed, job will be processed by queue system');
        }
      } catch (triggerError) {
        console.warn('⚠️ Failed to trigger pipeline:', triggerError);
        // Job is still in queue, so it will be processed eventually
      }

    } catch (attachmentError) {
      console.error(`❌ Failed to process attachment ${attachment.filename}:`, attachmentError);
      throw attachmentError;
    }
  }
}

/**
 * Process Gmail for all users at a specific hour
 */
export async function processGmailForAllUsers(targetHour: number): Promise<{
  success: boolean;
  processedUsers: number;
  totalReceipts: number;
  errors: string[];
}> {
  console.log(`🕐 Processing Gmail for all users at hour ${targetHour}`);

  const supabase = await createClient();
  
  // Get users ready for processing at this hour
  const { data: users, error } = await supabase.rpc('get_users_for_gmail_processing', {
    target_hour: targetHour
  });

  if (error) {
    console.error('❌ Failed to get users for Gmail processing:', error);
    return {
      success: false,
      processedUsers: 0,
      totalReceipts: 0,
      errors: [error.message]
    };
  }

  if (!users || users.length === 0) {
    console.log('📭 No users ready for Gmail processing at this hour');
    return {
      success: true,
      processedUsers: 0,
      totalReceipts: 0,
      errors: []
    };
  }

  console.log(`👥 Processing Gmail for ${users.length} users`);

  const results = {
    success: true,
    processedUsers: 0,
    totalReceipts: 0,
    errors: [] as string[]
  };

  // Process each user
  for (const user of users) {
    try {
      // Process last 24 hours of emails
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);

      const userResult = await processGmailForUser({
        userId: user.user_id,
        dateAfter: yesterday,
        keywords: user.gmail_keywords ? user.gmail_keywords.split(',').map(k => k.trim()) : undefined
      });

      if (userResult.success) {
        results.processedUsers++;
        results.totalReceipts += userResult.receiptsCreated.length;
      } else {
        results.errors.push(`User ${user.user_id}: ${userResult.errors.join(', ')}`);
      }

    } catch (error) {
      console.error(`❌ Failed to process Gmail for user ${user.user_id}:`, error);
      results.errors.push(`User ${user.user_id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  console.log(`🎉 Gmail batch processing complete: ${results.processedUsers} users, ${results.totalReceipts} receipts`);

  return results;
}
