import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Get current time info
    const now = new Date();
    const currentHour = now.getHours();
    const currentTime = now.toISOString();
    
    console.log(`🕐 Debug timing - Current hour: ${currentHour}, Current time: ${currentTime}`);
    
    // Get all users with Gmail settings
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select(`
        id,
        email,
        current_tier,
        subscription_status,
        gmail_connected,
        gmail_processing_enabled,
        gmail_processing_time,
        gmail_last_processed_date,
        gmail_email
      `)
      .eq('current_tier', 'business')
      .eq('gmail_connected', true);
    
    if (allUsersError) {
      throw allUsersError;
    }
    
    console.log(`📊 Found ${allUsers?.length || 0} business users with Gmail connected`);
    
    // Test the function for current hour
    const { data: currentHourUsers, error: currentError } = await supabase
      .rpc('get_users_for_gmail_processing', {
        target_hour: currentHour
      });
    
    if (currentError) {
      console.error('❌ Error calling get_users_for_gmail_processing:', currentError);
    }
    
    // Test for next few hours
    const testResults = [];
    for (let hour = 0; hour < 24; hour++) {
      const { data: hourUsers, error } = await supabase
        .rpc('get_users_for_gmail_processing', {
          target_hour: hour
        });
      
      if (!error && hourUsers && hourUsers.length > 0) {
        testResults.push({
          hour,
          userCount: hourUsers.length,
          users: hourUsers.map((u: any) => ({
            id: u.user_id,
            email: u.gmail_email,
            processingTime: allUsers?.find(au => au.id === u.user_id)?.gmail_processing_time
          }))
        });
      }
    }
    
    // Detailed analysis of each user
    const userAnalysis = allUsers?.map(user => {
      const processingTime = user.gmail_processing_time;
      const processingHour = processingTime ? parseInt(processingTime.split(':')[0]) : null;
      
      return {
        id: user.id,
        email: user.email,
        gmailEmail: user.gmail_email,
        tier: user.current_tier,
        status: user.subscription_status,
        gmailConnected: user.gmail_connected,
        processingEnabled: user.gmail_processing_enabled,
        processingTime: processingTime,
        processingHour: processingHour,
        lastProcessed: user.gmail_last_processed_date,
        shouldProcessNow: (
          user.current_tier === 'business' &&
          user.subscription_status === 'active' &&
          user.gmail_connected === true &&
          user.gmail_processing_enabled === true &&
          processingHour === currentHour &&
          (!user.gmail_last_processed_date || new Date(user.gmail_last_processed_date) < new Date(now.toDateString()))
        )
      };
    }) || [];
    
    return NextResponse.json({
      success: true,
      debug: {
        currentTime,
        currentHour,
        serverTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        totalBusinessUsers: allUsers?.length || 0,
        usersReadyForCurrentHour: currentHourUsers?.length || 0,
        testResults,
        userAnalysis,
        sqlFunction: {
          called: true,
          error: currentError?.message || null,
          result: currentHourUsers
        }
      }
    });
    
  } catch (error) {
    console.error('❌ Debug timing error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
