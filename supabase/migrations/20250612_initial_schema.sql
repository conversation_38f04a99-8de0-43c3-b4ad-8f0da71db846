-- Initial schema migration for RecoAI Receipt Processing App
-- Based on Product Requirements Document specifications

-- Enable RLS globally
ALTER DATABASE postgres SET row_security = on;

-- Create users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255),
  avatar_url TEXT,
  google_access_token TEXT,
  google_refresh_token TEXT,
  current_tier VARCHAR(20) DEFAULT 'free',
  receipts_processed INTEGER DEFAULT 0,
  subscription_status VARCHAR(20) DEFAULT 'inactive',
  subscription_end_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create receipts table
CREATE TABLE receipts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  original_file_name VARCHAR(255) NOT NULL,
  file_path TEXT NOT NULL, -- Supabase storage path
  file_size BIGINT,
  mime_type VARCHAR(100),
  
  -- Extracted data fields
  vendor VARCHAR(255),
  vendor_tax_id VARCHAR(100),
  receipt_date DATE,
  currency VARCHAR(10) DEFAULT 'KES',
  payment_method VARCHAR(50),
  subtotal DECIMAL(12,2),
  tax_rate_percent DECIMAL(5,2),
  tax_amount DECIMAL(12,2),
  total_amount DECIMAL(12,2),
  paid_amount DECIMAL(12,2),
  
  -- Processing metadata
  processing_status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
  confidence_score DECIMAL(3,2),
  extraction_method VARCHAR(20), -- openai, manual
  error_message TEXT,
  google_sheet_row_number INTEGER,
  redis_job_id VARCHAR(255), -- Upstash Redis job identifier
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create receipt_items table
CREATE TABLE receipt_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  receipt_id UUID REFERENCES receipts(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  quantity DECIMAL(8,2) DEFAULT 1,
  unit_price DECIMAL(10,2),
  total_price DECIMAL(10,2) NOT NULL,
  category VARCHAR(100),
  
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create google_sheets table
CREATE TABLE google_sheets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  year INTEGER NOT NULL,
  sheet_id VARCHAR(255) NOT NULL, -- Google Sheet ID
  sheet_url TEXT NOT NULL,
  sheet_name VARCHAR(255) NOT NULL, -- e.g., "Receipts 2025"
  last_row_number INTEGER DEFAULT 1, -- Track last written row
  total_receipts INTEGER DEFAULT 0,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, year) -- One sheet per user per year
);

-- Create payment_transactions table
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  paystack_reference VARCHAR(255) UNIQUE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(10) DEFAULT 'KES',
  status VARCHAR(20) NOT NULL, -- pending, successful, failed
  tier VARCHAR(20) NOT NULL, -- tier1, tier2
  receipts_allowance INTEGER NOT NULL,
  
  paystack_response JSONB, -- Store full PayStack response
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create user_analytics table
CREATE TABLE user_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  month_year VARCHAR(7) NOT NULL, -- Format: "2025-06"
  
  total_receipts INTEGER DEFAULT 0,
  total_spent DECIMAL(12,2) DEFAULT 0,
  top_vendor VARCHAR(255),
  top_category VARCHAR(100),
  average_receipt_amount DECIMAL(10,2) DEFAULT 0,
  
  category_breakdown JSONB, -- {"Food": 15000, "Transport": 5000}
  vendor_breakdown JSONB,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, month_year)
);

-- Create indexes for performance
CREATE INDEX idx_receipts_user_id ON receipts(user_id);
CREATE INDEX idx_receipts_status ON receipts(processing_status);
CREATE INDEX idx_receipts_date ON receipts(receipt_date);
CREATE INDEX idx_receipts_redis_job ON receipts(redis_job_id);

CREATE INDEX idx_receipt_items_receipt_id ON receipt_items(receipt_id);
CREATE INDEX idx_receipt_items_category ON receipt_items(category);

-- Enable Row Level Security on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE receipt_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE google_sheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users can only access their own data
CREATE POLICY "Users can access own data" ON users 
FOR ALL USING (auth.uid() = id);

-- Users can only access their own receipts
CREATE POLICY "Users can access own receipts" ON receipts 
FOR ALL USING (auth.uid() = user_id);

-- Users can only access receipt items for their own receipts
CREATE POLICY "Users can access own receipt items" ON receipt_items 
FOR ALL USING (
  auth.uid() = (SELECT user_id FROM receipts WHERE id = receipt_id)
);

-- Users can only access their own Google Sheets data
CREATE POLICY "Users can access own google sheets" ON google_sheets 
FOR ALL USING (auth.uid() = user_id);

-- Users can only access their own payment transactions
CREATE POLICY "Users can access own payment transactions" ON payment_transactions 
FOR ALL USING (auth.uid() = user_id);

-- Users can only access their own analytics
CREATE POLICY "Users can access own analytics" ON user_analytics 
FOR ALL USING (auth.uid() = user_id);

-- Create storage bucket for receipts
INSERT INTO storage.buckets (id, name, public) VALUES ('receipts', 'receipts', false);

-- Storage policies for receipts bucket
CREATE POLICY "Users can upload their own receipts" 
ON storage.objects FOR INSERT 
WITH CHECK (
  bucket_id = 'receipts' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can view their own receipts" 
ON storage.objects FOR SELECT 
USING (
  bucket_id = 'receipts' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can update their own receipts" 
ON storage.objects FOR UPDATE 
USING (
  bucket_id = 'receipts' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users can delete their own receipts" 
ON storage.objects FOR DELETE 
USING (
  bucket_id = 'receipts' AND 
  auth.uid()::text = (storage.foldername(name))[1]
);

-- Add trigger to update updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_receipts_updated_at BEFORE UPDATE ON receipts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_google_sheets_updated_at BEFORE UPDATE ON google_sheets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_transactions_updated_at BEFORE UPDATE ON payment_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_analytics_updated_at BEFORE UPDATE ON user_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 