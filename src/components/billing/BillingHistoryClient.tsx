'use client';

import { useState, useEffect } from 'react';
import { Calendar, CreditCard, CheckCircle, XCircle, Clock, ArrowLeft, Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';

interface SubscriptionTransaction {
  id: string;
  paystack_reference: string;
  amount: number;
  currency: string;
  status: string;
  tier: string;
  paid_at: string | null;
  created_at: string;
  payment_method?: string;
}

export default function BillingHistoryClient() {
  const [transactions, setTransactions] = useState<SubscriptionTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/subscription/transactions');
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setTransactions(data.transactions || []);
          } else {
            setError(data.error || 'Failed to fetch billing history');
          }
        } else {
          setError('Failed to fetch billing history');
        }
      } catch (error) {
        console.error('Error fetching billing history:', error);
        setError('Failed to fetch billing history');
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: currency || 'KES'
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-400" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-400" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const statusLower = status.toLowerCase();
    switch (statusLower) {
      case 'success':
        return <Badge className="bg-green-600/20 text-green-400 border-green-600/30">Success</Badge>;
      case 'failed':
        return <Badge className="bg-red-600/20 text-red-400 border-red-600/30">Failed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-600/20 text-yellow-400 border-yellow-600/30">Pending</Badge>;
      default:
        return <Badge className="bg-gray-600/20 text-gray-400 border-gray-600/30">{status}</Badge>;
    }
  };

  const getTierDisplayName = (tier: string) => {
    switch (tier.toLowerCase()) {
      case 'professional':
        return 'Professional';
      case 'business':
        return 'Business';
      case 'free':
        return 'Free';
      default:
        return tier.charAt(0).toUpperCase() + tier.slice(1);
    }
  };

  if (loading) {
    return (
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-8">
        <div className="flex items-center justify-center">
          <Loader2 className="w-8 h-8 animate-spin text-gray-400" />
          <span className="ml-3 text-gray-400">Loading billing history...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-8">
        <div className="text-center">
          <XCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Error Loading Billing History</h3>
          <p className="text-gray-400">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Link 
        href="/dashboard/settings"
        className="inline-flex items-center gap-2 text-gray-400 hover:text-white transition-colors"
      >
        <ArrowLeft className="w-4 h-4" />
        Back to Settings
      </Link>

      {/* Billing History */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-green-600/20 rounded-lg flex items-center justify-center">
            <Calendar className="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-white">All Transactions</h2>
            <p className="text-gray-400 text-sm">Complete history of your subscription payments</p>
          </div>
        </div>

        {transactions.length === 0 ? (
          <div className="text-center p-12">
            <CreditCard className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">No Transactions Yet</h3>
            <p className="text-gray-400 mb-4">Your payment history will appear here after you make a purchase</p>
            <Link 
              href="/dashboard/settings"
              className="inline-flex items-center gap-2 bg-pink-600 hover:bg-pink-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              View Subscription Plans
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div key={transaction.id} className="bg-gray-700/50 border border-gray-600/50 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(transaction.status)}
                    <div>
                      <h3 className="text-lg font-semibold text-white">
                        {getTierDisplayName(transaction.tier)} Plan
                      </h3>
                      <p className="text-sm text-gray-400">
                        Reference: {transaction.paystack_reference}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-white">
                      {formatAmount(transaction.amount, transaction.currency)}
                    </div>
                    {getStatusBadge(transaction.status)}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">Created:</span>
                    <div className="text-white font-medium">
                      {formatDate(transaction.created_at)}
                    </div>
                  </div>
                  
                  {transaction.paid_at && (
                    <div>
                      <span className="text-gray-400">Paid:</span>
                      <div className="text-white font-medium">
                        {formatDate(transaction.paid_at)}
                      </div>
                    </div>
                  )}
                  
                  {transaction.payment_method && (
                    <div>
                      <span className="text-gray-400">Payment Method:</span>
                      <div className="text-white font-medium capitalize">
                        {transaction.payment_method}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
