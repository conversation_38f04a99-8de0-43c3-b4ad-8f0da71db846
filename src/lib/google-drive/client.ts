import { google } from 'googleapis';
import { createAuthenticatedDriveClient } from './auth';
import { createClient } from '@/lib/supabase/server';

export interface DriveFile {
  id: string;
  name: string;
  mimeType: string;
  size?: string;
  modifiedTime: string;
  webViewLink: string;
  thumbnailLink?: string;
}

export interface DriveFolder {
  id: string;
  name: string;
  webViewLink: string;
}

export interface DriveImportResult {
  success: boolean;
  filesProcessed?: number;
  errors?: string[];
  error?: string;
}

export interface DriveDownloadResult {
  success: boolean;
  buffer?: Buffer;
  mimeType?: string;
  error?: string;
}

/**
 * List all folders in user's Google Drive
 */
export async function listGoogleDriveFolders(userId: string): Promise<DriveFolder[]> {
  console.log('📁 listGoogleDriveFolders called for user:', userId);

  try {
    console.log('🔐 Creating authenticated Drive client...');
    const { drive } = await createAuthenticatedDriveClient(userId);
    console.log('✅ Drive client created successfully');

    console.log('🔍 Querying Google Drive for folders...');
    const response = await drive.files.list({
      q: "mimeType='application/vnd.google-apps.folder' and trashed=false",
      fields: 'files(id, name, webViewLink)',
      orderBy: 'name'
    });

    console.log('📊 Google Drive API response:', {
      filesCount: response.data.files?.length || 0,
      files: response.data.files?.map(f => ({ id: f.id, name: f.name })) || []
    });

    if (!response.data.files) {
      console.log('⚠️ No files returned from Google Drive API');
      return [];
    }

    const folders = response.data.files.map(file => ({
      id: file.id!,
      name: file.name!,
      webViewLink: file.webViewLink!
    }));

    console.log('✅ Processed folders:', folders.length);
    return folders;
  } catch (error) {
    console.error('💥 Error listing Google Drive folders:', error);
    return [];
  }
}

/**
 * List receipt files in a specific Google Drive folder
 */
export async function listReceiptFilesInFolder(userId: string, folderId: string): Promise<DriveFile[]> {
  try {
    const { drive } = await createAuthenticatedDriveClient(userId);

    // Query for image and PDF files in the specified folder
    const imageQuery = `'${folderId}' in parents and trashed=false and (mimeType contains 'image/' or mimeType='application/pdf')`;

    const response = await drive.files.list({
      q: imageQuery,
      fields: 'files(id, name, mimeType, size, modifiedTime, webViewLink, thumbnailLink)',
      orderBy: 'modifiedTime desc',
      pageSize: 100 // Limit to 100 files for now
    });

    if (!response.data.files) {
      return [];
    }

    return response.data.files.map(file => ({
      id: file.id!,
      name: file.name!,
      mimeType: file.mimeType!,
      size: file.size,
      modifiedTime: file.modifiedTime!,
      webViewLink: file.webViewLink!,
      thumbnailLink: file.thumbnailLink
    }));
  } catch (error) {
    console.error('Error listing receipt files in folder:', error);
    return [];
  }
}

/**
 * Download a file from Google Drive and return as buffer
 */
export async function downloadFileFromGoogleDrive(
  userId: string,
  fileId: string
): Promise<{ success: boolean; buffer?: Buffer; mimeType?: string; error?: string }> {
  try {
    const { drive } = await createAuthenticatedDriveClient(userId);

    // Get file metadata first
    const fileMetadata = await drive.files.get({
      fileId: fileId,
      fields: 'mimeType'
    });

    // Download file content
    const response = await drive.files.get({
      fileId: fileId,
      alt: 'media'
    }, {
      responseType: 'arraybuffer'
    });

    return {
      success: true,
      buffer: Buffer.from(response.data as ArrayBuffer),
      mimeType: fileMetadata.data.mimeType || undefined
    };
  } catch (error) {
    console.error('Error downloading file from Google Drive:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to download file'
    };
  }
}

/**
 * Set the user's selected Google Drive folder for receipt import
 */
export async function setUserGoogleDriveFolder(
  userId: string,
  folderId: string,
  folderName: string
): Promise<boolean> {
  try {
    const supabase = await createClient();

    const { error } = await supabase
      .from('users')
      .update({
        google_drive_folder_id: folderId,
        google_drive_folder_name: folderName
      })
      .eq('id', userId);

    if (error) {
      console.error('Error setting Google Drive folder:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error setting Google Drive folder:', error);
    return false;
  }
}

/**
 * Get user's Google Drive connection status and folder info
 */
export async function getGoogleDriveStatus(userId: string) {
  try {
    const supabase = await createClient();
    const { data: user, error } = await supabase
      .from('users')
      .select(`
        google_drive_connected,
        google_drive_email,
        google_drive_folder_id,
        google_drive_folder_name
      `)
      .eq('id', userId)
      .single();

    if (error || !user) {
      return {
        connected: false,
        email: null,
        folder: null
      };
    }

    let folder = null;
    if (user.google_drive_connected && user.google_drive_folder_id) {
      try {
        const { drive } = await createAuthenticatedDriveClient(userId);
        const folderResponse = await drive.files.get({
          fileId: user.google_drive_folder_id,
          fields: 'id, name, webViewLink'
        });

        folder = {
          id: folderResponse.data.id!,
          name: folderResponse.data.name!,
          webViewLink: folderResponse.data.webViewLink!
        };
      } catch (error) {
        console.log('Could not access stored folder, user will need to select a new one');
      }
    }

    return {
      connected: user.google_drive_connected || false,
      email: user.google_drive_email,
      folder: folder
    };
  } catch (error) {
    console.error('Error getting Google Drive status:', error);
    return {
      connected: false,
      email: null,
      folder: null
    };
  }
}

/**
 * Test Google Drive connection by attempting to list files
 */
export async function testGoogleDriveConnection(userId: string): Promise<boolean> {
  try {
    const { drive } = await createAuthenticatedDriveClient(userId);
    
    // Try to list files (just to test the connection)
    await drive.files.list({
      pageSize: 1,
      fields: 'files(id)'
    });

    return true;
  } catch (error) {
    console.error('Google Drive connection test failed:', error);
    return false;
  }
}
