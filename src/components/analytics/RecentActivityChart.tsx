'use client';

import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from 'recharts';
import { Activity, Calendar } from 'lucide-react';

interface RecentActivityChartProps {
  data: {
    date: string;
    receipts: number;
    amount: number;
  }[];
  currency?: string;
}

export default function RecentActivityChart({ data, currency = 'KES' }: RecentActivityChartProps) {
  const hasData = data && data.length > 0;

  // Format currency based on the currency type
  const formatCurrency = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 0 })}`;
    }
  };

  const formatCurrencyDetailed = (amount: number) => {
    if (currency === 'KES') {
      return `Ksh ${amount.toLocaleString('en-KE', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else if (currency === 'USD') {
      return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    } else {
      return `${currency} ${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Calculate totals for the week
  const totalReceipts = hasData ? data.reduce((sum, day) => sum + day.receipts, 0) : 0;
  const totalAmount = hasData ? data.reduce((sum, day) => sum + day.amount, 0) : 0;
  const averageDaily = hasData ? totalAmount / data.length : 0;

  // Prepare data with formatted dates
  const chartData = hasData ? data.map(item => ({
    ...item,
    displayDate: formatDate(item.date)
  })) : [];

  return (
    <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-white flex items-center">
            <Activity className="w-5 h-5 mr-2 text-accent-pink" />
            Recent Activity
          </h3>
          <p className="text-sm text-gray-400">Last 7 days activity</p>
        </div>
        <div className="flex items-center space-x-4 text-sm">
          <div className="text-center">
            <p className="text-gray-400">Total Receipts</p>
            <p className="text-white font-semibold">{totalReceipts}</p>
          </div>
          <div className="text-center">
            <p className="text-gray-400">Total Amount</p>
            <p className="text-white font-semibold">{formatCurrency(totalAmount)}</p>
          </div>
        </div>
      </div>
      
      {hasData ? (
        <>
          <ResponsiveContainer width="100%" height={250}>
            <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <defs>
                <linearGradient id="activityGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#FF006E" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#FF006E" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis 
                dataKey="displayDate" 
                stroke="#9CA3AF"
                fontSize={11}
                tick={{ fill: '#9CA3AF' }}
              />
              <YAxis
                stroke="#9CA3AF"
                fontSize={11}
                tick={{ fill: '#9CA3AF' }}
                tickFormatter={(value) => formatCurrency(value)}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '12px',
                  color: '#F9FAFB',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                }}
                formatter={(value: any, name: string, props: any) => [
                  <div key="activity-tooltip" className="space-y-1">
                    <div>Amount: {formatCurrencyDetailed(props.payload.amount)}</div>
                    <div>Receipts: {props.payload.receipts}</div>
                    <div>Date: {new Date(props.payload.date).toLocaleDateString()}</div>
                  </div>
                ]}
                labelFormatter={() => ''}
              />
              <Area
                type="monotone"
                dataKey="amount"
                stroke="#FF006E"
                strokeWidth={2}
                fill="url(#activityGradient)"
                dot={{ fill: '#FF006E', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, stroke: '#FF006E', strokeWidth: 2, fill: '#1F2937' }}
              />
            </AreaChart>
          </ResponsiveContainer>
          
          {/* Daily Breakdown */}
          <div className="mt-6 grid grid-cols-7 gap-2">
            {chartData.map((day, index) => (
              <div key={day.date} className="text-center p-2 bg-gray-700/30 rounded-lg">
                <p className="text-xs text-gray-400 mb-1">{day.displayDate}</p>
                <p className="text-white font-medium text-sm">{day.receipts}</p>
                <p className="text-xs text-gray-400">{formatCurrency(day.amount)}</p>
              </div>
            ))}
          </div>
          
          {/* Summary Stats */}
          <div className="mt-6 grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="p-3 bg-accent-pink/10 border border-accent-pink/20 rounded-lg text-center">
              <p className="text-accent-pink text-sm font-medium">Daily Average</p>
              <p className="text-accent-pink text-lg font-bold">{formatCurrency(averageDaily)}</p>
            </div>
            <div className="p-3 bg-accent-purple/10 border border-accent-purple/20 rounded-lg text-center">
              <p className="text-accent-purple text-sm font-medium">Most Active Day</p>
              <p className="text-accent-purple text-lg font-bold">
                {chartData.reduce((max, day) => day.receipts > max.receipts ? day : max, chartData[0])?.displayDate || 'N/A'}
              </p>
            </div>
            <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
              <p className="text-green-400 text-sm font-medium">Highest Spending</p>
              <p className="text-green-400 text-lg font-bold">
                {formatCurrency(Math.max(...chartData.map(day => day.amount)))}
              </p>
            </div>
          </div>
        </>
      ) : (
        <div className="h-[250px] flex items-center justify-center text-gray-400">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-4 bg-gray-700/30 rounded-full flex items-center justify-center">
              <Calendar className="w-10 h-10" />
            </div>
            <p className="text-lg font-medium mb-2">No recent activity</p>
            <p className="text-sm">Upload receipts to see your daily activity</p>
          </div>
        </div>
      )}
    </div>
  );
}
