-- Migration to fix user synchronization between auth.users and custom users table
-- This prevents foreign key constraint errors when uploading receipts

-- Create a function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'name'),
    NEW.raw_user_meta_data->>'avatar_url'
  )
  ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    full_name = COALESCE(EXCLUDED.full_name, users.full_name),
    avatar_url = COALESCE(EXCLUDED.avatar_url, users.avatar_url),
    updated_at = NOW();
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create custom user record
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT OR UPDATE ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Backfill existing auth users to custom users table
INSERT INTO public.users (id, email, full_name, avatar_url)
SELECT 
  id,
  email,
  COALESCE(raw_user_meta_data->>'full_name', raw_user_meta_data->>'name'),
  raw_user_meta_data->>'avatar_url'
FROM auth.users
ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  full_name = COALESCE(EXCLUDED.full_name, users.full_name),
  avatar_url = COALESCE(EXCLUDED.avatar_url, users.avatar_url),
  updated_at = NOW();

-- Fix user synchronization and schema cache issues

-- First, ensure that all tables have proper structure
-- This migration addresses the 'total' column cache issue

-- Drop and recreate the receipt_items table to ensure schema cache is refreshed
DROP TABLE IF EXISTS receipt_items CASCADE;

-- Create receipt_items table with correct schema
CREATE TABLE receipt_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  receipt_id UUID REFERENCES receipts(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  quantity DECIMAL(8,2) DEFAULT 1,
  unit_price DECIMAL(10,2),
  total_price DECIMAL(10,2) NOT NULL,
  category VARCHAR(100),
  
  created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_receipt_items_receipt_id ON receipt_items(receipt_id);
CREATE INDEX idx_receipt_items_category ON receipt_items(category);

-- Enable Row Level Security
ALTER TABLE receipt_items ENABLE ROW LEVEL SECURITY;

-- Users can only access receipt items for their own receipts
CREATE POLICY "Users can access own receipt items" ON receipt_items 
FOR ALL USING (
  auth.uid() = (SELECT user_id FROM receipts WHERE id = receipt_id)
);

-- Refresh schema cache by updating metadata
NOTIFY pgrst, 'reload schema';

-- Add Google Sheets connection status to users table if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'users' AND column_name = 'google_sheets_connected') THEN
        ALTER TABLE users ADD COLUMN google_sheets_connected BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Update existing users with Google tokens to mark as connected
UPDATE users 
SET google_sheets_connected = TRUE 
WHERE google_access_token IS NOT NULL AND google_refresh_token IS NOT NULL; 