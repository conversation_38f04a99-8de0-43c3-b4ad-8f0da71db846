import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { createAuthenticatedSheetsClient } from '@/lib/google-sheets/auth';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get all existing Google Sheets for the user
    const { data: sheets, error } = await supabase
      .from('google_sheets')
      .select('*')
      .eq('user_id', user.id);

    if (error) {
      throw error;
    }

    if (!sheets || sheets.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No sheets to migrate'
      });
    }

    const { sheets: googleSheets } = await createAuthenticatedSheetsClient(user.id);
    const migratedSheets = [];

    for (const sheet of sheets) {
      try {
        // Check if sheet already has enhanced format (multiple tabs)
        const sheetInfo = await googleSheets.spreadsheets.get({
          spreadsheetId: sheet.sheet_id
        });

        const existingSheetNames = sheetInfo.data.sheets?.map(s => s.properties?.title) || [];
        
        // If it already has the enhanced tabs, skip
        if (existingSheetNames.includes('Monthly Summary') && 
            existingSheetNames.includes('Category Analysis') && 
            existingSheetNames.includes('Vendor Analysis')) {
          continue;
        }

        // Add the enhanced sheets
        await addEnhancedSheetsToExisting(googleSheets, sheet.sheet_id);
        migratedSheets.push(sheet.sheet_name);

      } catch (sheetError) {
        console.error(`Failed to migrate sheet ${sheet.sheet_name}:`, sheetError);
        // Continue with other sheets
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully migrated ${migratedSheets.length} sheets`,
      migratedSheets
    });

  } catch (error) {
    console.error('Migration error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

async function addEnhancedSheetsToExisting(sheets: any, spreadsheetId: string) {
  // Add the three new sheets
  await sheets.spreadsheets.batchUpdate({
    spreadsheetId,
    requestBody: {
      requests: [
        {
          addSheet: {
            properties: {
              sheetId: 1,
              title: 'Monthly Summary',
              gridProperties: {
                rowCount: 100,
                columnCount: 10
              }
            }
          }
        },
        {
          addSheet: {
            properties: {
              sheetId: 2,
              title: 'Category Analysis',
              gridProperties: {
                rowCount: 100,
                columnCount: 8
              }
            }
          }
        },
        {
          addSheet: {
            properties: {
              sheetId: 3,
              title: 'Vendor Analysis',
              gridProperties: {
                rowCount: 100,
                columnCount: 7
              }
            }
          }
        }
      ]
    }
  });

  // Setup each sheet with headers and formulas
  await setupMonthlySummarySheet(sheets, spreadsheetId);
  await setupCategoryAnalysisSheet(sheets, spreadsheetId);
  await setupVendorAnalysisSheet(sheets, spreadsheetId);
}

async function setupMonthlySummarySheet(sheets: any, spreadsheetId: string) {
  const headers = [
    'Month', 'Total Receipts', 'Total Amount', 'Average Amount', 
    'Top Category', 'Top Vendor', 'Tax Amount', 'Business Expenses', 'Notes'
  ];

  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Monthly Summary!A1:I1',
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: [headers]
    }
  });

  // Add sample formulas for current year months
  const currentYear = new Date().getFullYear();
  const monthFormulas = [];
  
  for (let month = 1; month <= 12; month++) {
    const monthName = new Date(currentYear, month - 1).toLocaleDateString('en-US', { month: 'long' });
    const row = month + 1;
    
    monthFormulas.push([
      `${monthName} ${currentYear}`,
      `=COUNTIFS(Receipts!D:D,">="&DATE(${currentYear},${month},1),Receipts!D:D,"<"&DATE(${currentYear},${month + 1},1))`,
      `=SUMIFS(Receipts!M:M,Receipts!D:D,">="&DATE(${currentYear},${month},1),Receipts!D:D,"<"&DATE(${currentYear},${month + 1},1))`,
      `=IF(B${row}>0,C${row}/B${row},0)`,
      `=INDEX(Receipts!H:H,MODE(MATCH(Receipts!H:H,Receipts!H:H,0)))`,
      `=INDEX(Receipts!B:B,MODE(MATCH(Receipts!B:B,Receipts!B:B,0)))`,
      `=SUMIFS(Receipts!L:L,Receipts!D:D,">="&DATE(${currentYear},${month},1),Receipts!D:D,"<"&DATE(${currentYear},${month + 1},1))`,
      `=C${row}`, // Assuming all are business expenses for now
      'Auto-calculated'
    ]);
  }

  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Monthly Summary!A2:I13',
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: monthFormulas
    }
  });
}

async function setupCategoryAnalysisSheet(sheets: any, spreadsheetId: string) {
  const headers = [
    'Category', 'Total Amount', 'Receipt Count', 'Average Amount', 
    '% of Total', 'Last Purchase', 'Top Vendor', 'Notes'
  ];

  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Category Analysis!A1:H1',
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: [headers]
    }
  });
}

async function setupVendorAnalysisSheet(sheets: any, spreadsheetId: string) {
  const headers = [
    'Vendor', 'Total Amount', 'Receipt Count', 'Average Amount', 
    '% of Total', 'Last Purchase', 'Notes'
  ];

  await sheets.spreadsheets.values.update({
    spreadsheetId,
    range: 'Vendor Analysis!A1:G1',
    valueInputOption: 'USER_ENTERED',
    requestBody: {
      values: [headers]
    }
  });
}
