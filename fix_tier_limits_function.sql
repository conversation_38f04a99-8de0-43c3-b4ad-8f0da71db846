-- Fix the check_tier_limits function to include Gmail auto-processing field
-- Run this in Supabase SQL Editor to fix the "Failed to check tier limits" error
-- Date: 2025-06-28

CREATE OR REPLACE FUNCTION check_tier_limits(user_id_param UUID)
RETURNS TABLE(
  can_process_receipt BOOLEAN,
  receipts_remaining INTEGER,
  has_analytics_access BOOLEAN,
  has_google_drive_access BOOLEAN,
  has_gmail_auto_processing BOOLEAN,
  data_retention_months INTEGER
) AS $$
DECLARE
  user_tier TEXT;
  user_limit INTEGER;
  user_used INTEGER;
  user_status TEXT;
  user_retention INTEGER;
  remaining_receipts INTEGER;
  can_process BOOLEAN;
  has_analytics BOOLEAN;
  has_drive BOOLEAN;
  has_gmail BOOLEAN;
BEGIN
  -- Get user data
  SELECT
    u.current_tier,
    u.monthly_receipt_limit,
    COALESCE(u.receipts_used_this_period, 0),
    u.subscription_status,
    COALESCE(u.data_retention_months, 1)
  INTO user_tier, user_limit, user_used, user_status, user_retention
  FROM users u
  WHERE u.id = user_id_param;

  -- If user not found, return default values
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 0, false, false, false, 1;
    RETURN;
  END IF;

  -- Calculate remaining receipts
  -- Handle unlimited receipts (-1) properly
  IF user_limit = -1 THEN
    remaining_receipts := -1; -- unlimited
    can_process := user_status IN ('active', 'inactive');
  ELSE
    remaining_receipts := GREATEST(0, user_limit - user_used);
    can_process := remaining_receipts > 0 AND user_status IN ('active', 'inactive');
  END IF;
  
  -- Check tier-based features
  has_analytics := user_tier IN ('professional', 'business');
  has_drive := user_tier = 'business';
  has_gmail := user_tier = 'business'; -- Gmail auto-processing is Business tier only

  -- Return results
  RETURN QUERY SELECT 
    can_process,
    remaining_receipts,
    has_analytics,
    has_drive,
    has_gmail,
    user_retention;
END;
$$ LANGUAGE plpgsql;
