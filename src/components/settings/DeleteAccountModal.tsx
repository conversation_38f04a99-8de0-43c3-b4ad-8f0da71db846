'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON>ert<PERSON><PERSON>gle, Trash2, Loader2 } from 'lucide-react';
import { UserData } from './SettingsClient';
import { createClient } from '@/lib/supabase/client';

interface DeleteAccountModalProps {
  userData: UserData;
  onClose: () => void;
}

export default function DeleteAccountModal({ userData, onClose }: DeleteAccountModalProps) {
  const [step, setStep] = useState(1);
  const [confirmationText, setConfirmationText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const supabase = createClient();
  const requiredText = 'DELETE MY ACCOUNT';

  const handleDeleteAccount = async () => {
    if (confirmationText !== requiredText) {
      setError('Please type the exact confirmation text');
      return;
    }

    try {
      setIsDeleting(true);
      setError(null);

      const response = await fetch('/api/user/delete-account', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      if (result.success) {
        // Sign out the user
        await supabase.auth.signOut();
        
        // Redirect to home page
        window.location.href = '/';
      } else {
        throw new Error(result.error || 'Failed to delete account');
      }
    } catch (error) {
      console.error('Error deleting account:', error);
      setError(error instanceof Error ? error.message : 'Failed to delete account');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 border border-gray-700 rounded-xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-600/20 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Delete Account</h2>
              <p className="text-gray-400 text-sm">This action cannot be undone</p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            disabled={isDeleting}
            className="p-2 bg-gray-800 hover:bg-gray-700 disabled:opacity-50 text-gray-300 hover:text-white rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 1 ? (
            <div className="space-y-6">
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <h3 className="text-red-200 font-medium mb-2">What will be deleted:</h3>
                <ul className="text-red-300 text-sm space-y-1">
                  <li>• Your account and profile information</li>
                  <li>• All uploaded receipts and extracted data</li>
                  <li>• Google Sheets integration settings</li>
                  <li>• Processing history and analytics</li>
                  <li>• All files stored in our system</li>
                </ul>
              </div>

              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <h3 className="text-yellow-200 font-medium mb-2">Before you proceed:</h3>
                <ul className="text-yellow-300 text-sm space-y-1">
                  <li>• Export any data you want to keep</li>
                  <li>• Cancel any active subscriptions</li>
                  <li>• Note that Google Sheets data will remain</li>
                  <li>• This action is permanent and irreversible</li>
                </ul>
              </div>

              <div className="text-center">
                <p className="text-gray-400 text-sm mb-4">
                  Are you sure you want to permanently delete your account?
                </p>
                <div className="flex gap-3 justify-center">
                  <button
                    onClick={onClose}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => setStep(2)}
                    className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                  >
                    Continue
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Trash2 className="w-8 h-8 text-red-400" />
                </div>
                <h3 className="text-xl font-bold text-white mb-2">
                  Final Confirmation
                </h3>
                <p className="text-gray-400">
                  This will permanently delete your account and all data.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-400 mb-2">
                  Type <span className="text-red-400 font-mono">{requiredText}</span> to confirm:
                </label>
                <input
                  type="text"
                  value={confirmationText}
                  onChange={(e) => {
                    setConfirmationText(e.target.value);
                    setError(null);
                  }}
                  placeholder={requiredText}
                  disabled={isDeleting}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:opacity-50"
                />
                {error && (
                  <p className="text-red-400 text-sm mt-2">{error}</p>
                )}
              </div>

              <div className="bg-gray-800/50 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">Account Summary:</h4>
                <div className="text-sm text-gray-400 space-y-1">
                  <div>Email: {userData.email}</div>
                  <div>Receipts: {userData.receipts_processed}</div>
                  <div>Plan: {userData.current_tier}</div>
                  <div>Member since: {new Date(userData.created_at).toLocaleDateString()}</div>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setStep(1)}
                  disabled={isDeleting}
                  className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={handleDeleteAccount}
                  disabled={isDeleting || confirmationText !== requiredText}
                  className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-600/50 text-white rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  {isDeleting ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      Delete Account
                    </>
                  )}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
