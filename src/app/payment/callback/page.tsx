'use client';

import { useEffect, useState, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

interface VerificationResult {
  success: boolean;
  error?: string;
  data?: {
    user: {
      current_tier: string;
      subscription_status: string;
    };
    transaction: {
      amount: number;
      tier: string;
    };
  };
  message?: string;
}

function PaymentCallbackContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [result, setResult] = useState<VerificationResult | null>(null);

  useEffect(() => {
    const reference = searchParams.get('reference');
    const trxref = searchParams.get('trxref'); // Paystack also sends this

    const paymentReference = reference || trxref;

    if (!paymentReference) {
      setStatus('error');
      setResult({
        success: false,
        error: 'No payment reference found in URL'
      });
      return;
    }

    verifyPayment(paymentReference);
  }, [searchParams]);

  const verifyPayment = async (reference: string) => {
    try {
      const response = await fetch('/api/subscription/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reference }),
      });

      const data: VerificationResult = await response.json();
      setResult(data);

      if (data.success) {
        setStatus('success');

        // Dispatch tier update event to refresh sidebar
        window.dispatchEvent(new CustomEvent('tierUpdated'));

        // Redirect to dashboard after 3 seconds
        setTimeout(() => {
          router.push('/dashboard/settings?tab=billing&upgraded=true');
        }, 3000);
      } else {
        setStatus('error');
      }

    } catch (error) {
      console.error('Error verifying payment:', error);
      setStatus('error');
      setResult({
        success: false,
        error: 'Failed to verify payment. Please contact support.'
      });
    }
  };

  const formatTierName = (tier: string) => {
    switch (tier) {
      case 'professional':
        return 'Professional';
      case 'business':
        return 'Business';
      default:
        return tier;
    }
  };

  const formatAmount = (amount: number) => {
    return `KES ${amount.toLocaleString()}`;
  };

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-gray-800 rounded-xl shadow-xl p-8">
        <div className="text-center">
          {status === 'loading' && (
            <>
              <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Loader2 className="w-8 h-8 text-blue-400 animate-spin" />
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Verifying Payment
              </h1>
              <p className="text-gray-400">
                Please wait while we confirm your payment...
              </p>
            </>
          )}

          {status === 'success' && result?.success && (
            <>
              <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-400" />
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Payment Successful!
              </h1>
              <p className="text-gray-400 mb-6">
                {result.message || 'Your subscription has been upgraded successfully.'}
              </p>
              
              {result.data && (
                <div className="bg-gray-700/50 rounded-lg p-4 mb-6 text-left">
                  <h3 className="text-lg font-semibold text-white mb-3">
                    Subscription Details
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Plan:</span>
                      <span className="text-white font-medium">
                        {formatTierName(result.data.user.current_tier)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Amount Paid:</span>
                      <span className="text-white font-medium">
                        {formatAmount(result.data.transaction.amount)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Status:</span>
                      <span className="text-green-400 font-medium">
                        {result.data.user.subscription_status}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              <p className="text-sm text-gray-400 mb-4">
                Redirecting to dashboard in 3 seconds...
              </p>
              
              <button
                onClick={() => {
                  window.dispatchEvent(new CustomEvent('tierUpdated'));
                  router.push('/dashboard/settings?tab=billing');
                }}
                className="w-full bg-pink-600 hover:bg-pink-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
              >
                Go to Dashboard
              </button>
            </>
          )}

          {status === 'error' && (
            <>
              <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <XCircle className="w-8 h-8 text-red-400" />
              </div>
              <h1 className="text-2xl font-bold text-white mb-2">
                Payment Failed
              </h1>
              <p className="text-gray-400 mb-6">
                {result?.error || 'There was an issue processing your payment.'}
              </p>
              
              <div className="space-y-3">
                <button
                  onClick={() => router.push('/dashboard/settings?tab=billing')}
                  className="w-full bg-pink-600 hover:bg-pink-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  Try Again
                </button>
                
                <button
                  onClick={() => router.push('/dashboard')}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
                >
                  Back to Dashboard
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}

export default function PaymentCallbackPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-gray-800 rounded-xl shadow-xl p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Loader2 className="w-8 h-8 text-blue-400 animate-spin" />
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">
              Loading...
            </h1>
            <p className="text-gray-400">
              Please wait while we load the payment verification page...
            </p>
          </div>
        </div>
      </div>
    }>
      <PaymentCallbackContent />
    </Suspense>
  );
}
