'use client'

import Link from 'next/link'
import { <PERSON><PERSON><PERSON>, CheckCircle, Clock, Shield } from 'lucide-react'
import { trackLandingPageEvents } from '@/lib/analytics'

export default function CTASection() {
  return (
    <section className="relative z-10 px-4 py-12 sm:py-20 bg-gradient-to-r from-pink-500/10 to-purple-600/10">
      <div className="max-w-4xl mx-auto text-center">
        <div className="mb-8 sm:mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6 px-2">
            Ready to Transform Your Small Business 
            <span className="bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
              {' '}Expense Management?
            </span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-300 mb-6 sm:mb-8 max-w-3xl mx-auto px-2">
            Join hundreds of small businesses saving 10+ hours monthly with AI-powered receipt processing. 
            Start free today and see the difference automated expense management makes.
          </p>
        </div>

        {/* Trust Indicators */}
        <div className="flex flex-wrap justify-center gap-3 sm:gap-6 mb-8 sm:mb-12">
          <div className="flex items-center gap-2 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg px-3 sm:px-4 py-2">
            <CheckCircle className="w-4 sm:w-5 h-4 sm:h-5 text-green-400 flex-shrink-0" />
            <span className="text-gray-300 text-sm sm:text-base">99% Accuracy</span>
          </div>
          <div className="flex items-center gap-2 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg px-3 sm:px-4 py-2">
            <Clock className="w-4 sm:w-5 h-4 sm:h-5 text-blue-400 flex-shrink-0" />
            <span className="text-gray-300 text-sm sm:text-base">5-Minute Setup</span>
          </div>
          <div className="flex items-center gap-2 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg px-3 sm:px-4 py-2">
            <Shield className="w-4 sm:w-5 h-4 sm:h-5 text-purple-400 flex-shrink-0" />
            <span className="text-gray-300 text-sm sm:text-base">Bank-Level Security</span>
          </div>
        </div>

        {/* Main CTA */}
        <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center mb-8 sm:mb-12">
          <Link
            href="/login"
            onClick={() => trackLandingPageEvents.finalCta()}
            className="group bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-4 sm:py-5 px-8 sm:px-10 rounded-lg text-lg sm:text-xl transition-all duration-200 transform hover:scale-105 flex items-center gap-2 w-full sm:w-auto justify-center shadow-2xl"
          >
            Start Your Free Plan Today
            <ArrowRight className="w-5 sm:w-6 h-5 sm:h-6 group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>

        {/* Guarantee and Trust */}
        <div className="space-y-3 sm:space-y-4 text-sm sm:text-base text-gray-400">
          <p className="flex items-center justify-center gap-2">
            <CheckCircle className="w-4 sm:w-5 h-4 sm:h-5 text-green-400 flex-shrink-0" />
            No credit card required • 10 free receipts monthly • Cancel anytime
          </p>
          <p className="flex items-center justify-center gap-2">
            <CheckCircle className="w-4 sm:w-5 h-4 sm:h-5 text-green-400 flex-shrink-0" />
            30-day money-back guarantee • Upgrade only when you're ready
          </p>
        </div>

        {/* Social Proof Numbers */}
        <div className="mt-12 sm:mt-16 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 sm:p-8">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8">
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-white mb-2">500+</div>
              <div className="text-gray-400 text-sm sm:text-base">Small Businesses Trust Us</div>
            </div>
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-white mb-2">10+</div>
              <div className="text-gray-400 text-sm sm:text-base">Hours Saved Monthly</div>
            </div>
            <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-white mb-2">25K+</div>
              <div className="text-gray-400 text-sm sm:text-base">Receipts Processed</div>
            </div>
          </div>
        </div>

        {/* Final Urgency Message */}
        <div className="mt-8 sm:mt-12 bg-gradient-to-r from-orange-500/10 to-red-600/10 backdrop-blur-md border border-orange-500/20 rounded-xl p-4 sm:p-6">
          <p className="text-orange-300 font-medium text-sm sm:text-base">
            🔥 Limited Time: Get started today and receive priority onboarding support for your small business
          </p>
        </div>
      </div>
    </section>
  )
}