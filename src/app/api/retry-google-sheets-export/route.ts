import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user
    const supabase = createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const { receiptId } = await request.json();
    
    if (!receiptId) {
      return NextResponse.json(
        { success: false, error: 'Receipt ID is required' },
        { status: 400 }
      );
    }



    // Verify the receipt belongs to the user and get its data
    const { data: receipt, error: receiptError } = await supabase
      .from('receipts')
      .select(`
        id,
        user_id,
        vendor,
        vendor_tax_id,
        receipt_date,
        currency,
        payment_method,
        subtotal,
        tax_rate_percent,
        tax_amount,
        total_amount,
        paid_amount,
        original_file_name,
        processing_status
      `)
      .eq('id', receiptId)
      .eq('user_id', user.id)
      .single();

    if (receiptError || !receipt) {
      return NextResponse.json(
        { success: false, error: 'Receipt not found or access denied' },
        { status: 404 }
      );
    }

    // Check if the receipt is completed (required for retry)
    if (receipt.processing_status !== 'completed') {
      return NextResponse.json(
        { success: false, error: 'Receipt must be completed before retrying Google Sheets export' },
        { status: 400 }
      );
    }

    // Get receipt items
    const { data: items, error: itemsError } = await supabase
      .from('receipt_items')
      .select('description, total, category')
      .eq('receipt_id', receiptId);

    if (itemsError) {
      console.error('Error fetching receipt items:', itemsError);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch receipt items' },
        { status: 500 }
      );
    }

    // Prepare extracted data for Google Sheets export
    const extractedData = {
      vendor: receipt.vendor,
      vendor_tax_id: receipt.vendor_tax_id,
      date: receipt.receipt_date,
      currency: receipt.currency,
      payment_method: receipt.payment_method,
      subtotal: receipt.subtotal,
      tax_rate_percent: receipt.tax_rate_percent,
      tax_amount: receipt.tax_amount,
      total_amount: receipt.total_amount,
      paid_amount: receipt.paid_amount,
      items: items || []
    };

    // Call the Edge Function to retry Google Sheets export
    const edgeFunctionUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/functions/v1/retry-google-sheets-export`;
    
    const response = await fetch(edgeFunctionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.SUPABASE_SERVICE_ROLE_KEY}`,
      },
      body: JSON.stringify({
        receiptId: receipt.id,
        userId: receipt.user_id,
        extractedData,
        fileName: receipt.original_file_name
      }),
    });

    const result = await response.json();

    if (result.success) {
      // Clear any previous error message
      await supabase
        .from('receipts')
        .update({ 
          error_message: null,
          google_sheet_row_number: result.rowNumber 
        })
        .eq('id', receiptId);

      return NextResponse.json({
        success: true,
        message: 'Google Sheets export retried successfully',
        rowNumber: result.rowNumber
      });
    } else {
      // Update error message
      await supabase
        .from('receipts')
        .update({ 
          error_message: `Google Sheets retry failed: ${result.error}` 
        })
        .eq('id', receiptId);

      return NextResponse.json({
        success: false,
        error: result.error || 'Google Sheets export retry failed'
      });
    }

  } catch (error) {
    console.error('Retry Google Sheets export error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    );
  }
}
