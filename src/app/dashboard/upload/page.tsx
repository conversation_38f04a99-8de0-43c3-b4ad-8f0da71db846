'use client'

import { useState } from 'react'
import UploadZone from '@/components/upload/UploadZone'
import GoogleDriveConnection from '@/components/upload/GoogleDriveConnection'
import { createClient } from '@/lib/supabase/client'
import { CheckCircle, AlertCircle, FileText, Upload as UploadIcon, Cloud, CloudOff } from 'lucide-react'

interface UploadResult {
  fileName: string
  status: 'success' | 'error'
  message: string
  receiptId?: string
  jobId?: string
}

interface ImportResult {
  processed: number
  total: number
  results: Array<{
    fileId: string
    receiptId: string
    queueJobId: string
    status: string
  }>
  errors?: string[]
}

export default function UploadPage() {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [uploadMode, setUploadMode] = useState<'single' | 'batch'>('single')
  const [googleDriveMode, setGoogleDriveMode] = useState<'single' | 'batch'>('single')
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([])
  const [uploadProgress, setUploadProgress] = useState<{ current: number; total: number }>({ current: 0, total: 0 })
  const [googleDriveConnected, setGoogleDriveConnected] = useState(false)
  const [importResults, setImportResults] = useState<ImportResult | null>(null)

  const processFile = async (file: File): Promise<UploadResult> => {
    try {
      console.log(`Uploading file: ${file.name}`)
      
      // Create form data for the upload
      const formData = new FormData()
      formData.append('file', file)
      
      // Call the upload API endpoint
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })
      
      const result = await response.json()

      if (!response.ok || !result.success) {
        // Handle specific error codes
        if (result.code === 'LIMIT_EXCEEDED') {
          const upgradeMessage = result.receipts_remaining === 0
            ? 'You have reached your monthly receipt limit. Please upgrade your subscription to continue uploading receipts.'
            : `You have ${result.receipts_remaining} receipts remaining this month. ${result.error}`
          throw new Error(upgradeMessage)
        }
        throw new Error(result.error || 'Upload failed')
      }
      
      console.log('Upload successful:', result)

      // Trigger intensive polling for fast processing
      try {
        if (typeof window !== 'undefined' && (window as any).triggerIntensivePolling) {
          (window as any).triggerIntensivePolling()
          console.log('🚀 Intensive polling triggered for upload')
        }
      } catch (pollingError) {
        console.warn('Failed to trigger intensive polling:', pollingError)
      }

      return {
        fileName: file.name,
        status: 'success',
        message: 'File uploaded and queued for processing',
        receiptId: result.data.receiptId,
        jobId: result.data.jobId
      }

    } catch (e: any) {
      console.error('File processing failed:', { file: file.name, error: e })
      return {
        fileName: file.name,
        status: 'error',
        message: e.message || 'An unexpected error occurred'
      }
    }
  }

  const handleSingleFileUpload = async (file: File) => {
    setIsUploading(true)
    setError(null)
    setUploadResults([])

    // Check authentication first
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      setError('You must be logged in to upload files.')
      setIsUploading(false)
      return
    }

    try {
      const result = await processFile(file)
      setUploadResults([result])
      
      if (result.status === 'error') {
        setError(result.message)
      }
    } catch (e: any) {
      setError(e.message || 'An unexpected error occurred during the upload process.')
    } finally {
      setIsUploading(false)
    }
  }

  const handleBatchFileUpload = async (files: File[]) => {
    if (files.length === 0) return

    setIsUploading(true)
    setError(null)
    setUploadResults([])
    setUploadProgress({ current: 0, total: files.length })

    // Check authentication first
    const supabase = createClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      setError('You must be logged in to upload files.')
      setIsUploading(false)
      return
    }

    const results: UploadResult[] = []

    // Process files sequentially to avoid overwhelming the system
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      setUploadProgress({ current: i + 1, total: files.length })

      try {
        const result = await processFile(file)
        results.push(result)
      } catch (e: any) {
        results.push({
          fileName: file.name,
          status: 'error',
          message: e.message || 'Failed to process file'
        })
      }

      // Small delay to prevent overwhelming the API
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    setUploadResults(results)

    // Trigger intensive polling for batch uploads
    try {
      if (typeof window !== 'undefined' && (window as any).triggerIntensivePolling) {
        (window as any).triggerIntensivePolling()
        console.log('🚀 Intensive polling triggered for batch upload')
      }
    } catch (pollingError) {
      console.warn('Failed to trigger intensive polling:', pollingError)
    }

    setIsUploading(false)
  }

  const handleFileRemove = () => {
    setError(null)
    setUploadResults([])
    setUploadProgress({ current: 0, total: 0 })
    setImportResults(null)
  }

  const handleImportComplete = (results: ImportResult) => {
    setImportResults(results)
    // Clear any previous upload results when importing
    setUploadResults([])
  }

  const successCount = uploadResults.filter(r => r.status === 'success').length
  const errorCount = uploadResults.filter(r => r.status === 'error').length

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-4">Upload Receipts</h1>
        <p className="text-gray-400">Choose your preferred method to upload and process receipts</p>
      </div>

      {/* Upload Methods Grid */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Google Drive Import */}
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
          <div className="flex items-start space-x-4 mb-4">
            <div className="bg-blue-600/20 p-3 rounded-lg">
              <Cloud className="w-6 h-6 text-blue-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-2">Import from Google Drive</h3>
              <p className="text-gray-400 text-sm mb-4">
                Connect your Google Drive to automatically import receipts from a specific folder.
              </p>

              {/* Google Drive Mode Toggle */}
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-400 text-sm">Import Mode:</span>
                <div className="flex bg-gray-700 rounded-lg p-1">
                  <button
                    onClick={() => setGoogleDriveMode('single')}
                    className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                      googleDriveMode === 'single'
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    Single
                  </button>
                  <button
                    onClick={() => setGoogleDriveMode('batch')}
                    className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                      googleDriveMode === 'batch'
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    Batch
                  </button>
                </div>
              </div>

              <div className="text-xs text-gray-500 mb-4">
                <p>• Imports from connected Google Drive folder</p>
                <p>• Processes files automatically</p>
                {googleDriveMode === 'batch' && <p>• Batch import available</p>}
              </div>
            </div>
          </div>
          <GoogleDriveConnection
            onStatusChange={setGoogleDriveConnected}
            onImportComplete={handleImportComplete}
          />
        </div>

        {/* Manual Upload */}
        <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
          <div className="flex items-start space-x-4 mb-4">
            <div className="bg-pink-600/20 p-3 rounded-lg">
              <UploadIcon className="w-6 h-6 text-pink-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-2">Manual Upload</h3>
              <p className="text-gray-400 text-sm mb-4">
                Upload receipts directly from your device for immediate processing.
              </p>

              {/* Manual Upload Mode Toggle */}
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-400 text-sm">Upload Mode:</span>
                <div className="flex bg-gray-700 rounded-lg p-1">
                  <button
                    onClick={() => setUploadMode('single')}
                    className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                      uploadMode === 'single'
                        ? 'bg-pink-600 text-white'
                        : 'text-gray-400 hover:text-white'
                    }`}
                    disabled={isUploading}
                  >
                    Single
                  </button>
                  <button
                    onClick={() => setUploadMode('batch')}
                    className={`px-3 py-1 rounded-md text-xs font-medium transition-colors ${
                      uploadMode === 'batch'
                        ? 'bg-pink-600 text-white'
                        : 'text-gray-400 hover:text-white'
                    }`}
                    disabled={isUploading}
                  >
                    Batch
                  </button>
                </div>
              </div>

              <div className="text-xs text-gray-500 mb-4">
                <p>• Formats: JPG, PNG, WebP, PDF</p>
                <p>• Max size: 10MB for images, 5MB for PDFs</p>
                <p>• PDFs are automatically converted to images</p>
                {uploadMode === 'batch' && <p>• Max files: 10 per batch</p>}
              </div>
            </div>
          </div>

          {/* Upload Zone integrated into card */}
          <UploadZone
            onFileSelect={uploadMode === 'single' ? handleSingleFileUpload : undefined}
            onBatchFileSelect={uploadMode === 'batch' ? handleBatchFileUpload : undefined}
            onFileRemove={handleFileRemove}
            isUploading={isUploading}
            error={error}
            allowBatch={uploadMode === 'batch'}
            maxFiles={10}
          />
        </div>
      </div>

      {/* Upload Progress (Batch Mode) */}
      {isUploading && uploadMode === 'batch' && uploadProgress.total > 0 && (
        <div className="mt-6 p-4 bg-gray-800/50 border border-gray-700 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-white font-medium">Processing Files</span>
            <span className="text-gray-400 text-sm">
              {uploadProgress.current} / {uploadProgress.total}
            </span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div 
              className="bg-pink-500 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${(uploadProgress.current / uploadProgress.total) * 100}%` }}
            />
          </div>
        </div>
      )}

      {/* Import Results */}
      {importResults && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Import Results</h3>
            <div className="flex items-center space-x-4 text-sm">
              <span className="text-green-400">✓ {importResults.processed} imported</span>
              {importResults.errors && importResults.errors.length > 0 && (
                <span className="text-red-400">✗ {importResults.errors.length} failed</span>
              )}
            </div>
          </div>

          <div className="space-y-3">
            {importResults.results.map((result, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-700/50 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <div className="flex-grow min-w-0">
                  <p className="text-green-400 font-medium">File imported successfully</p>
                  <p className="text-gray-400 text-sm">
                    Receipt ID: {result.receiptId} • Job ID: {result.queueJobId}
                  </p>
                </div>
              </div>
            ))}

            {importResults.errors && importResults.errors.map((error, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-700/50 rounded-lg">
                <AlertCircle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                <div className="flex-grow min-w-0">
                  <p className="text-red-400 font-medium">Import failed</p>
                  <p className="text-gray-400 text-sm">{error}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Results */}
      {uploadResults.length > 0 && (
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Upload Results</h3>
            <div className="flex items-center space-x-4 text-sm">
              {successCount > 0 && (
                <span className="text-green-400">
                  ✓ {successCount} successful
                </span>
              )}
              {errorCount > 0 && (
                <span className="text-red-400">
                  ✗ {errorCount} failed
                </span>
              )}
            </div>
          </div>

          <div className="space-y-3">
            {uploadResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border ${
                  result.status === 'success'
                    ? 'bg-green-500/10 border-green-500/20'
                    : 'bg-red-500/10 border-red-500/20'
                }`}
              >
                <div className="flex items-center space-x-3">
                  {result.status === 'success' ? (
                    <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  ) : (
                    <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0" />
                  )}
                  <div className="flex-grow min-w-0">
                    <p className={`font-medium ${
                      result.status === 'success' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {result.fileName}
                    </p>
                    <p className="text-sm text-gray-400">{result.message}</p>
                    {result.receiptId && (
                      <p className="text-xs text-gray-500 mt-1">
                        Receipt ID: {result.receiptId}
                      </p>
                    )}

                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
} 