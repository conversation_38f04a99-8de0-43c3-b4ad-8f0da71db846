import { Clock, BarChart3, FileText } from 'lucide-react'
import { Benefit } from '@/lib/types/landing-page'

interface BenefitsSectionProps {
  benefits: Benefit[];
}

export default function BenefitsSection({ benefits }: BenefitsSectionProps) {
  const getIcon = (iconName: string) => {
    const iconProps = "w-12 sm:w-16 h-12 sm:h-16"
    switch (iconName) {
      case 'Clock':
        return <Clock className={`${iconProps} text-blue-400`} />
      case 'BarChart3':
        return <BarChart3 className={`${iconProps} text-green-400`} />
      case 'FileText':
        return <FileText className={`${iconProps} text-purple-400`} />
      default:
        return <Clock className={`${iconProps} text-blue-400`} />
    }
  }

  const getGradientColor = (iconName: string) => {
    switch (iconName) {
      case 'Clock':
        return 'from-blue-500 to-cyan-600'
      case 'BarChart3':
        return 'from-green-500 to-emerald-600'
      case 'FileText':
        return 'from-purple-500 to-indigo-600'
      default:
        return 'from-blue-500 to-cyan-600'
    }
  }

  return (
    <section className="relative z-10 px-4 py-12 sm:py-20">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6 px-2">
            Transform Your Small Business Operations
          </h2>
          <p className="text-base sm:text-xl text-gray-300 max-w-3xl mx-auto px-2">
            See the immediate impact ReceiptLabs has on your business efficiency, financial clarity, and growth potential.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 sm:gap-12 mb-12 sm:mb-16">
          {benefits.map((benefit, index) => (
            <div key={benefit.id} className="text-center group">
              <div className="relative mb-6 sm:mb-8">
                <div className={`w-20 sm:w-24 h-20 sm:h-24 bg-gradient-to-r ${getGradientColor(benefit.icon)} rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  {getIcon(benefit.icon)}
                </div>
                <div className="absolute -top-2 -right-2 sm:-top-3 sm:-right-3 w-8 h-8 bg-gradient-to-r from-pink-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {index + 1}
                </div>
              </div>
              
              <h3 className="text-xl sm:text-2xl font-bold text-white mb-4 sm:mb-6">
                {benefit.title}
              </h3>
              
              <p className="text-gray-400 text-sm sm:text-base leading-relaxed max-w-sm mx-auto">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>

        {/* Statistics Section */}
        <div className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 sm:p-8 md:p-12">
          <div className="text-center mb-8 sm:mb-12">
            <h3 className="text-2xl sm:text-3xl font-bold text-white mb-4">
              Real Results from Small Businesses
            </h3>
            <p className="text-gray-400 text-sm sm:text-base">
              See the measurable impact ReceiptLabs has on businesses like yours
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2">10+</div>
              <div className="text-gray-400 text-sm">Hours Saved Monthly</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2">99%</div>
              <div className="text-gray-400 text-sm">Data Accuracy</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2">75%</div>
              <div className="text-gray-400 text-sm">Faster Tax Prep</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-white mb-2">100%</div>
              <div className="text-gray-400 text-sm">Organized Records</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}