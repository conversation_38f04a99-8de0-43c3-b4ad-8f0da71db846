'use client'

import Link from 'next/link'
import { Upload, Zap, <PERSON><PERSON>hart3, ArrowRight, Play, Star, CheckCircle, Clock, Shield } from 'lucide-react'
import { CTAButton } from '@/lib/types/landing-page'
import { trackLandingPageEvents } from '@/lib/analytics'

interface HeroSectionProps {
  headline: string;
  subheadline: string;
  trustIndicators: string[];
  primaryCTA: CTAButton;
  secondaryCTA: CTAButton;
}

export default function HeroSection({ 
  headline, 
  subheadline, 
  trustIndicators, 
  primaryCTA, 
  secondaryCTA 
}: HeroSectionProps) {
  const getTrustIcon = (indicator: string) => {
    switch (indicator) {
      case '99% Accuracy':
        return <CheckCircle className="w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 text-green-400 flex-shrink-0" />
      case 'Instant Processing':
        return <Clock className="w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 text-blue-400 flex-shrink-0" />
      case 'Bank-Level Security':
        return <Shield className="w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 text-purple-400 flex-shrink-0" />
      default:
        return <CheckCircle className="w-3 sm:w-4 md:w-5 h-3 sm:h-4 md:h-5 text-green-400 flex-shrink-0" />
    }
  }

  return (
    <section className="relative z-10 px-4 py-12 sm:py-20">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          {/* Trust Badge */}
          <div className="inline-flex items-center gap-2 bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-full px-3 sm:px-4 py-2 mb-6 sm:mb-8 animate-fade-in-up">
            <div className="flex items-center gap-1">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-3 sm:w-4 h-3 sm:h-4 text-yellow-400 fill-current" />
              ))}
            </div>
            <span className="text-gray-300 text-xs sm:text-sm">Trusted by 1000+ small businesses</span>
          </div>

          <h1 className="text-3xl sm:text-5xl md:text-7xl font-bold text-white mb-4 sm:mb-6 leading-tight animate-fade-in-up animate-delay-100 px-2">
            {headline}
            <br />
            <span className="bg-gradient-to-r from-pink-500 to-purple-600 bg-clip-text text-transparent">
              with ReceiptLabs
            </span>
          </h1>

          <p className="text-base sm:text-xl md:text-2xl text-gray-300 mb-6 sm:mb-8 max-w-4xl mx-auto leading-relaxed animate-fade-in-up animate-delay-200 px-2">
            {subheadline}
          </p>

          {/* Trust Indicators */}
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4 md:gap-6 mb-8 sm:mb-12 animate-fade-in-up animate-delay-300 px-2">
            {trustIndicators.map((indicator, index) => (
              <div key={index} className="flex items-center gap-2 bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-lg px-2 sm:px-3 md:px-4 py-2 hover:border-green-500/50 transition-colors">
                {getTrustIcon(indicator)}
                <span className="text-gray-300 text-xs sm:text-sm md:text-base">{indicator}</span>
              </div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-16 animate-fade-in-up animate-delay-400 px-4">
            <Link
              href={primaryCTA.href}
              onClick={() => trackLandingPageEvents.heroCtaPrimary()}
              className="group bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-bold py-3 sm:py-4 px-6 sm:px-8 rounded-lg text-base sm:text-lg transition-all duration-200 transform hover:scale-105 flex items-center gap-2 w-full sm:w-auto justify-center"
            >
              {primaryCTA.text}
              <ArrowRight className="w-4 sm:w-5 h-4 sm:h-5 group-hover:translate-x-1 transition-transform" />
            </Link>

            <a
              href={secondaryCTA.href}
              onClick={() => trackLandingPageEvents.heroCtaSecondary()}
              className="group border border-gray-600 hover:border-gray-500 text-white font-medium py-3 sm:py-4 px-6 sm:px-8 rounded-lg text-base sm:text-lg transition-all duration-200 flex items-center gap-2 w-full sm:w-auto justify-center"
            >
              <Play className="w-4 sm:w-5 h-4 sm:h-5" />
              {secondaryCTA.text}
            </a>
          </div>

          {/* 3-Step Process Visualization */}
          <div className="relative max-w-5xl mx-auto animate-fade-in-up animate-delay-500">
            <div className="bg-gray-800/50 backdrop-blur-md border border-gray-700 rounded-xl sm:rounded-2xl p-4 sm:p-6 md:p-8 shadow-2xl">
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4 sm:gap-6 md:gap-8 items-center">
                {/* Step 1: Upload */}
                <div className="text-center md:col-span-2 animate-fade-in-left">
                  <div className="w-12 sm:w-16 h-12 sm:h-16 bg-pink-500/20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 animate-float">
                    <Upload className="w-6 sm:w-8 h-6 sm:h-8 text-pink-400" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold text-white mb-2">1. Upload Receipts</h3>
                  <p className="text-gray-400 text-xs sm:text-sm">Drag & drop your business receipts or connect Gmail</p>
                </div>

                {/* Arrow */}
                <div className="hidden md:flex justify-center">
                  <ArrowRight className="w-6 h-6 text-gray-500 animate-pulse-slow" />
                </div>

                {/* Step 2: AI Processing */}
                <div className="text-center md:col-span-2 animate-fade-in-up animate-delay-200">
                  <div className="w-12 sm:w-16 h-12 sm:h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 animate-float animate-delay-200">
                    <Zap className="w-6 sm:w-8 h-6 sm:h-8 text-purple-400" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold text-white mb-2">2. AI Extracts Data</h3>
                  <p className="text-gray-400 text-xs sm:text-sm">Advanced AI reads vendor, items, totals, dates</p>
                </div>

                {/* Mobile Arrow */}
                <div className="flex md:hidden justify-center">
                  <ArrowRight className="w-5 sm:w-6 h-5 sm:h-6 text-gray-500 rotate-90 animate-pulse-slow" />
                </div>

                {/* Desktop Arrow */}
                <div className="hidden md:flex justify-center">
                  <ArrowRight className="w-6 h-6 text-gray-500 animate-pulse-slow" />
                </div>

                {/* Step 3: Google Sheets */}
                <div className="text-center md:col-span-2 animate-fade-in-right">
                  <div className="w-12 sm:w-16 h-12 sm:h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-3 sm:mb-4 animate-float animate-delay-400">
                    <BarChart3 className="w-6 sm:w-8 h-6 sm:h-8 text-green-400" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold text-white mb-2">3. Organized in Sheets</h3>
                  <p className="text-gray-400 text-xs sm:text-sm">Data appears instantly in your Google Sheet</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}