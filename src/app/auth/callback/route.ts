import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get('next') ?? '/dashboard'

  if (code) {
    const supabase = await createClient()
    const { data, error } = await supabase.auth.exchangeCodeForSession(code)
    
    if (!error && data.session) {
      // Extract Google tokens from the session
      const providerToken = data.session.provider_token
      const providerRefreshToken = data.session.provider_refresh_token
      const userId = data.session.user.id
      const userEmail = data.session.user.email
      const userFullName = data.session.user.user_metadata?.full_name || data.session.user.user_metadata?.name

      // Create or update user record
      const { error: upsertError } = await supabase
        .from('users')
        .upsert({
          id: userId,
          email: userEmail,
          full_name: userFullName,
          google_access_token: providerToken,
          google_refresh_token: providerRefreshToken,
          google_sheets_connected: !!(providerToken && providerRefreshToken),
          avatar_url: data.session.user.user_metadata?.avatar_url,
          updated_at: new Date().toISOString()
        }, { 
          onConflict: 'id',
          ignoreDuplicates: false 
        })

      if (upsertError) {
        console.error('Failed to create/update user:', upsertError)
        // Continue anyway - don't block authentication
      }

      const forwardedHost = request.headers.get('x-forwarded-host') // original origin before load balancer
      const isLocalEnv = process.env.NODE_ENV === 'development'
      if (isLocalEnv) {
        // we can be sure that there is no load balancer in between, so no need to watch for X-Forwarded-Host
        return NextResponse.redirect(`${origin}${next}`)
      } else if (forwardedHost) {
        return NextResponse.redirect(`https://${forwardedHost}${next}`)
      } else {
        return NextResponse.redirect(`${origin}${next}`)
      }
    }
  }

  // return the user to an error page with instructions
  return NextResponse.redirect(`${origin}/auth/auth-code-error`)
} 