import jsPDF from 'jspdf';
import { ReceiptData, CURRENCIES } from '../types';

export async function generateReceiptPDF(receiptData: ReceiptData) {
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  // Colors
  const primaryColor = '#EC4899'; // Pink-500
  const darkGray = '#374151';
  const lightGray = '#6B7280';

  // Page dimensions
  const pageWidth = pdf.internal.pageSize.getWidth();
  const margin = 20;
  const contentWidth = pageWidth - (margin * 2);
  
  let yPosition = margin;

  // Helper function to add text
  const addText = (text: string, x: number, y: number, options: any = {}) => {
    pdf.setFontSize(options.fontSize || 10);
    pdf.setTextColor(options.color || '#000000');
    if (options.bold) pdf.setFont('helvetica', 'bold');
    else pdf.setFont('helvetica', 'normal');
    
    if (options.align === 'center') {
      pdf.text(text, x, y, { align: 'center' });
    } else if (options.align === 'right') {
      pdf.text(text, x, y, { align: 'right' });
    } else {
      pdf.text(text, x, y);
    }
  };

  // Helper function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: receiptData.currency,
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Header - RECEIPT (simple text, no colored background)
  addText('RECEIPT', pageWidth / 2, yPosition + 5, {
    fontSize: 20,
    bold: true,
    color: '#000000',
    align: 'center'
  });
  yPosition += 20;

  // Business Information
  if (receiptData.business.name) {
    addText(receiptData.business.name, margin, yPosition, { fontSize: 14, bold: true });
    yPosition += 8;
  }

  if (receiptData.business.address) {
    addText(receiptData.business.address, margin, yPosition);
    yPosition += 6;
  }

  const cityStateZip = [
    receiptData.business.city,
    receiptData.business.state,
    receiptData.business.zip
  ].filter(Boolean).join(', ');

  if (cityStateZip) {
    addText(cityStateZip, margin, yPosition);
    yPosition += 6;
  }

  if (receiptData.business.phone || receiptData.business.email) {
    const contact = [receiptData.business.phone, receiptData.business.email]
      .filter(Boolean).join(' | ');
    addText(contact, margin, yPosition);
    yPosition += 6;
  }

  yPosition += 10;

  // Receipt Details (Right side)
  const rightX = pageWidth - margin;
  let rightY = yPosition - 30; // Align with business info

  addText(`Receipt #: ${receiptData.receipt.number}`, rightX, rightY, { align: 'right' });
  rightY += 6;
  
  const formattedDate = new Date(receiptData.receipt.date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  addText(`Date: ${formattedDate}`, rightX, rightY, { align: 'right' });
  rightY += 6;
  
  addText(`Payment: ${receiptData.receipt.paymentMethod}`, rightX, rightY, { align: 'right' });

  // Customer Information (if provided)
  if (receiptData.customer.name) {
    yPosition += 5;
    pdf.setDrawColor(200, 200, 200);
    pdf.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 10;

    addText('SOLD TO:', margin, yPosition, { fontSize: 10, bold: true, color: lightGray });
    yPosition += 8;

    addText(receiptData.customer.name, margin, yPosition);
    yPosition += 6;

    if (receiptData.customer.address) {
      addText(receiptData.customer.address, margin, yPosition);
      yPosition += 6;
    }

    const customerCityStateZip = [
      receiptData.customer.city,
      receiptData.customer.state,
      receiptData.customer.zip
    ].filter(Boolean).join(', ');

    if (customerCityStateZip) {
      addText(customerCityStateZip, margin, yPosition);
      yPosition += 6;
    }
  }

  yPosition += 10;

  // Items Header
  pdf.setDrawColor(200, 200, 200);
  pdf.line(margin, yPosition, pageWidth - margin, yPosition);
  yPosition += 8;

  // Table headers
  addText('DESCRIPTION', margin, yPosition, { fontSize: 10, bold: true, color: darkGray });
  addText('QTY', pageWidth - 80, yPosition, { fontSize: 10, bold: true, color: darkGray });
  addText('RATE', pageWidth - 60, yPosition, { fontSize: 10, bold: true, color: darkGray });
  addText('AMOUNT', pageWidth - margin, yPosition, { fontSize: 10, bold: true, color: darkGray, align: 'right' });
  yPosition += 8;

  pdf.line(margin, yPosition, pageWidth - margin, yPosition);
  yPosition += 8;

  // Items
  receiptData.items.forEach((item) => {
    if (item.description.trim()) {
      addText(item.description, margin, yPosition);
      addText(item.quantity.toString(), pageWidth - 80, yPosition);
      addText(formatCurrency(item.rate), pageWidth - 60, yPosition);
      addText(formatCurrency(item.amount), pageWidth - margin, yPosition, { align: 'right' });
      yPosition += 8;
    }
  });

  yPosition += 5;
  pdf.line(margin, yPosition, pageWidth - margin, yPosition);
  yPosition += 10;

  // Totals
  const totalsX = pageWidth - 80;
  
  addText('SUBTOTAL:', totalsX, yPosition);
  addText(formatCurrency(receiptData.totals.subtotal), pageWidth - margin, yPosition, { align: 'right' });
  yPosition += 8;

  if (receiptData.tax.rate > 0) {
    addText(`TAX (${receiptData.tax.rate}%):`, totalsX, yPosition);
    addText(formatCurrency(receiptData.tax.amount), pageWidth - margin, yPosition, { align: 'right' });
    yPosition += 8;
  }

  if (receiptData.discount.amount > 0) {
    const discountAmount = receiptData.discount.type === 'percentage'
      ? (receiptData.totals.subtotal * receiptData.discount.amount) / 100
      : receiptData.discount.amount;

    const discountLabel = receiptData.discount.type === 'percentage'
      ? `DISCOUNT (${receiptData.discount.amount}%):`
      : 'DISCOUNT:';

    addText(discountLabel, totalsX, yPosition);
    addText(`-${formatCurrency(discountAmount)}`, pageWidth - margin, yPosition, { align: 'right' });
    yPosition += 8;
  }

  // Total line
  pdf.setDrawColor(200, 200, 200);
  pdf.line(totalsX, yPosition, pageWidth - margin, yPosition);
  yPosition += 8;

  addText('TOTAL:', totalsX, yPosition, { fontSize: 12, bold: true });
  addText(formatCurrency(receiptData.totals.total), pageWidth - margin, yPosition, { 
    fontSize: 12, 
    bold: true, 
    align: 'right' 
  });

  // Notes
  if (receiptData.notes.trim()) {
    yPosition += 20;
    pdf.line(margin, yPosition, pageWidth - margin, yPosition);
    yPosition += 10;
    
    addText(receiptData.notes, margin, yPosition, { fontSize: 10, color: lightGray });
  }

  // Remove footer watermark - no footer needed

  // Download
  const fileName = `Receipt_${receiptData.receipt.number}_${receiptData.receipt.date}.pdf`;
  pdf.save(fileName);
}
