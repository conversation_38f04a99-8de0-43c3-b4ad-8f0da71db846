# Gmail Processing Cron Jobs for Reco Engine

This directory contains scripts for automated Gmail receipt processing using standard Unix cron jobs.

## 📁 Files

- **`gmail-cron.js`** - Main cron job script that processes Gmail for Business users
- **`setup-gmail-cron.sh`** - Setup script to configure the cron job
- **`test-gmail-processing.js`** - Test script for manual testing
- **`README.md`** - This documentation

## 🚀 Quick Setup

1. **Run the setup script:**
   ```bash
   ./scripts/setup-gmail-cron.sh
   ```

2. **The script will:**
   - Check your environment variables
   - Test the Gmail processing
   - Offer to add the cron job automatically
   - Create necessary directories

## 📅 Manual Cron Setup

If you prefer to set up the cron job manually:

1. **Edit your crontab:**
   ```bash
   crontab -e
   ```

2. **Add this line:**
   ```bash
   0 * * * * cd /path/to/recoAI && node scripts/gmail-cron.js >> logs/gmail-cron.log 2>&1
   ```

3. **Replace `/path/to/recoAI` with your actual project path**

## 🧪 Testing

### Test Current Hour
```bash
node scripts/test-gmail-processing.js
```

### Test Specific Hour
```bash
node scripts/test-gmail-processing.js 9  # Test 9 AM
```

### Test All Hours
```bash
node scripts/test-gmail-processing.js all
```

### Manual Gmail Processing
```bash
node scripts/gmail-cron.js
```

## 📊 Monitoring

### View Logs
```bash
tail -f logs/gmail-cron.log
```

### Check Cron Status
```bash
crontab -l  # List current cron jobs
```

### Check if Cron is Running
```bash
ps aux | grep cron
```

## 🔧 Environment Variables

Required in your `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
GEMINI_API_KEY=your_gemini_api_key
NEXT_PUBLIC_SITE_URL=http://localhost:3000  # or your deployed URL
CRON_SECRET=your_cron_secret  # optional
```

## 📋 How It Works

1. **Hourly Execution**: Cron runs the script every hour
2. **User Selection**: Script queries database for users scheduled at current hour
3. **Gmail Processing**: For each user:
   - Searches Gmail for receipts from last 24 hours
   - Uses Gemini AI to analyze emails
   - Downloads receipt attachments
   - Creates receipt records in database
   - Triggers existing processing pipeline
4. **Logging**: Results are logged for monitoring

## 🔍 Troubleshooting

### Cron Job Not Running
```bash
# Check if cron service is running
sudo systemctl status cron

# Check cron logs
sudo tail -f /var/log/cron.log
```

### Script Errors
```bash
# Check Gmail cron logs
tail -f logs/gmail-cron.log

# Test script manually
node scripts/gmail-cron.js
```

### Environment Issues
```bash
# Test environment variables
node -e "console.log(process.env.NEXT_PUBLIC_SUPABASE_URL)"

# Run setup script again
./scripts/setup-gmail-cron.sh
```

### Database Connection Issues
- Ensure your app is running on localhost:3000
- Check Supabase credentials
- Verify database schema is up to date

## 📈 Production Deployment

When deploying to production:

1. **Update environment variables** in `.env.local` or server environment
2. **Update `NEXT_PUBLIC_SITE_URL`** to your production URL
3. **Set up cron on your production server**
4. **Monitor logs** for any issues
5. **Consider using a process manager** like PM2 for better reliability

### Using PM2 (Recommended for Production)
```bash
# Install PM2
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'gmail-cron',
    script: 'scripts/gmail-cron.js',
    cron_restart: '0 * * * *',  # Every hour
    autorestart: false,
    env: {
      NODE_ENV: 'production'
    }
  }]
}
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## 🔒 Security Notes

- Keep your `.env.local` file secure and never commit it
- Use strong `CRON_SECRET` values
- Regularly rotate API keys
- Monitor logs for suspicious activity
- Limit file permissions on scripts and logs

## 📞 Support

If you encounter issues:

1. Check the logs first: `tail -f logs/gmail-cron.log`
2. Test manually: `node scripts/test-gmail-processing.js`
3. Verify environment variables
4. Ensure your app is running and accessible
5. Check database connectivity

## 🎯 Next Steps

After setting up the cron job:

1. **Test with a Business user account**
2. **Connect Gmail and set processing time**
3. **Monitor the first few automated runs**
4. **Check receipt processing in the dashboard**
5. **Verify Google Sheets integration**
