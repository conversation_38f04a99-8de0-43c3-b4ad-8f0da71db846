{"name": "receiptlabs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "prebuild": "cp node_modules/pdfjs-dist/build/pdf.worker.min.mjs public/pdf.worker.min.mjs || true", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/postcss": "^4", "@types/jspdf": "^1.3.3", "@upstash/redis": "^1.35.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "googleapis": "^150.0.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.514.0", "next": "15.3.3", "pdfjs-dist": "^5.3.31", "postcss": "^8.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.8", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/recharts": "^1.8.29", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "eslint": "^9", "eslint-config-next": "15.3.3", "tw-animate-css": "^1.3.4", "typescript": "^5"}}