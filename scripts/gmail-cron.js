#!/usr/bin/env node

/**
 * Gmail Processing Cron Job for Reco Engine
 * 
 * This script processes Gmail for Business tier users at their scheduled times.
 * Run this script every hour via cron:
 * 
 * # Add to crontab (crontab -e):
 * 0 * * * * cd /path/to/recoAI && node scripts/gmail-cron.js >> logs/gmail-cron.log 2>&1
 * 
 * Or for development/testing:
 * node scripts/gmail-cron.js
 */

const https = require('https');
const http = require('http');

// Configuration
const config = {
  supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
  supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
  appUrl: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  cronSecret: process.env.CRON_SECRET || 'your-cron-secret'
};

// Validate configuration
function validateConfig() {
  const required = ['supabaseUrl', 'supabaseServiceKey'];
  const missing = required.filter(key => !config[key]);
  
  if (missing.length > 0) {
    console.error('❌ Missing required environment variables:', missing.join(', '));
    process.exit(1);
  }
}

// Make HTTP request
function makeRequest(url, options, data = null) {
  return new Promise((resolve, reject) => {
    const lib = url.startsWith('https:') ? https : http;
    
    const req = lib.request(url, options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (error) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Get users ready for Gmail processing
async function getUsersForProcessing(targetHour) {
  const url = `${config.supabaseUrl}/rest/v1/rpc/get_users_for_gmail_processing`;
  
  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${config.supabaseServiceKey}`,
      'Content-Type': 'application/json',
      'apikey': config.supabaseServiceKey
    }
  };

  const data = { target_hour: targetHour };

  try {
    const response = await makeRequest(url, options, data);
    
    if (response.status !== 200) {
      throw new Error(`Failed to get users: ${response.status} - ${JSON.stringify(response.data)}`);
    }

    return response.data || [];
  } catch (error) {
    console.error('❌ Error getting users for processing:', error.message);
    throw error;
  }
}

// Process Gmail for a single user
async function processUserGmail(userId, userEmail) {
  const url = `${config.appUrl}/api/gmail/process-internal`;

  const options = {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${config.supabaseServiceKey}`,
      'Content-Type': 'application/json'
    }
  };

  // Calculate date range (last 24 hours)
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);

  const data = {
    userId,
    dateAfter: yesterday.toISOString(),
    maxEmails: 50,
    minConfidence: 50 // Lowered for accountant-grade detection
  };

  try {
    console.log(`📧 Processing Gmail for user ${userId} (${userEmail})`);
    
    const response = await makeRequest(url, options, data);
    
    if (response.status !== 200) {
      throw new Error(`Processing failed: ${response.status} - ${JSON.stringify(response.data)}`);
    }

    const result = response.data;
    
    if (result.success) {
      console.log(`✅ User ${userId}: ${result.data.receiptsCreated} receipts from ${result.data.processedEmails} emails`);
      return {
        success: true,
        userId,
        processedEmails: result.data.processedEmails,
        receiptsCreated: result.data.receiptsCreated,
        processingTime: result.data.processingTime
      };
    } else {
      throw new Error(result.error || 'Unknown processing error');
    }

  } catch (error) {
    console.error(`❌ Failed to process Gmail for user ${userId}:`, error.message);
    return {
      success: false,
      userId,
      error: error.message
    };
  }
}

// Main processing function
async function processGmailForHour(targetHour) {
  console.log(`🕐 Starting Gmail processing for hour ${targetHour}`);
  
  const results = {
    targetHour,
    timestamp: new Date().toISOString(),
    processedUsers: 0,
    totalReceipts: 0,
    totalEmails: 0,
    errors: []
  };

  try {
    // Get users ready for processing
    const users = await getUsersForProcessing(targetHour);
    
    if (users.length === 0) {
      console.log('📭 No users ready for Gmail processing at this hour');
      return results;
    }

    console.log(`👥 Found ${users.length} users ready for processing`);

    // Process each user
    for (const user of users) {
      try {
        const userResult = await processUserGmail(user.user_id, user.gmail_email);
        
        if (userResult.success) {
          results.processedUsers++;
          results.totalReceipts += userResult.receiptsCreated;
          results.totalEmails += userResult.processedEmails;
        } else {
          results.errors.push(`User ${user.user_id}: ${userResult.error}`);
        }

        // Small delay between users
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.error(`❌ Error processing user ${user.user_id}:`, error.message);
        results.errors.push(`User ${user.user_id}: ${error.message}`);
      }
    }

  } catch (error) {
    console.error('❌ Gmail processing failed:', error.message);
    results.errors.push(`System error: ${error.message}`);
  }

  console.log(`🎉 Gmail processing complete for hour ${targetHour}:`);
  console.log(`   - Processed users: ${results.processedUsers}`);
  console.log(`   - Total receipts: ${results.totalReceipts}`);
  console.log(`   - Total emails: ${results.totalEmails}`);
  console.log(`   - Errors: ${results.errors.length}`);

  if (results.errors.length > 0) {
    console.log('⚠️ Errors encountered:');
    results.errors.forEach(error => console.log(`   - ${error}`));
  }

  return results;
}

// Main execution
async function main() {
  console.log('🚀 Gmail Processing Cron Job Started');
  console.log(`📅 Timestamp: ${new Date().toISOString()}`);
  
  try {
    // Validate configuration
    validateConfig();
    
    // Get current hour (0-23)
    const currentHour = new Date().getHours();
    console.log(`🕐 Current hour: ${currentHour}`);
    
    // Process Gmail for current hour
    const results = await processGmailForHour(currentHour);
    
    // Exit with appropriate code
    if (results.errors.length > 0) {
      console.log('⚠️ Gmail processing completed with errors');
      process.exit(1);
    } else {
      console.log('✅ Gmail processing completed successfully');
      process.exit(0);
    }

  } catch (error) {
    console.error('💥 Fatal error in Gmail cron job:', error.message);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  console.log('\n🛑 Gmail cron job interrupted');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Gmail cron job terminated');
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main();
}

module.exports = { processGmailForHour, processUserGmail };
