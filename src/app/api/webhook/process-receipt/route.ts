import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { addJobToQueue, QueueJob } from '@/lib/queue'
import { createProcessReceiptJob } from '@/lib/jobs'
import { publishUserNotification, storeNotificationForPolling } from '@/lib/pubsub'
import { headers } from 'next/headers'

// Supabase webhook payload interface
interface SupabaseWebhookPayload {
  type: 'INSERT' | 'UPDATE' | 'DELETE'
  table: string
  schema: string
  record: {
    id: string
    user_id: string
    processing_status: string
    file_path: string
    original_file_name: string
    file_size: number
    mime_type?: string
    created_at: string
    [key: string]: any
  }
  old_record: any
}

// Verify webhook authenticity (optional but recommended)
function verifyWebhookSignature(payload: string, signature?: string): boolean {
  // In production, you should verify the webhook signature
  // For now, we'll do basic validation
  if (!signature) {
    console.warn('No webhook signature provided')
    return true // Allow for development
  }
  
  // TODO: Implement proper HMAC signature verification
  // const expectedSignature = crypto
  //   .createHmac('sha256', process.env.WEBHOOK_SECRET!)
  //   .update(payload)
  //   .digest('hex')
  // return signature === expectedSignature
  
  return true
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔗 Webhook received: process-receipt')
    
    // Get headers for verification
    const headersList = headers()
    const signature = headersList.get('x-webhook-signature')
    const userAgent = headersList.get('user-agent')
    
    // Verify this is from Supabase (pg_net extension)
    if (!userAgent?.includes('pg_net')) {
      console.warn('Webhook not from pg_net:', userAgent)
    }

    // Parse the Supabase webhook payload
    const payload: SupabaseWebhookPayload = await request.json()
    console.log('📦 Supabase webhook payload:', {
      type: payload.type,
      table: payload.table,
      receipt_id: payload.record?.id,
      user_id: payload.record?.user_id,
      processing_status: payload.record?.processing_status
    })

    // Verify payload structure
    if (!payload.type || !payload.record || !payload.record.id || !payload.record.user_id) {
      console.error('❌ Invalid Supabase webhook payload structure')
      return NextResponse.json(
        { error: 'Invalid payload structure' },
        { status: 400 }
      )
    }

    // Only handle INSERT events on receipts table with pending status
    if (payload.type !== 'INSERT' || payload.table !== 'receipts' || payload.record.processing_status !== 'pending') {
      console.log('ℹ️ Ignoring webhook event:', {
        type: payload.type,
        table: payload.table,
        status: payload.record.processing_status
      })
      return NextResponse.json(
        { message: 'Event type not handled', type: payload.type, table: payload.table },
        { status: 200 }
      )
    }
    
    // Verify webhook signature (if implemented)
    const payloadString = JSON.stringify(payload)
    if (!verifyWebhookSignature(payloadString, signature || undefined)) {
      console.error('❌ Invalid webhook signature')
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }
    
    // Create Supabase client
    const supabase = await createClient()

    // Extract receipt data from webhook payload
    const receipt = payload.record
    console.log('📄 Processing receipt:', {
      id: receipt.id,
      user_id: receipt.user_id,
      file_name: receipt.original_file_name,
      file_size: receipt.file_size,
      processing_status: receipt.processing_status
    })

    // Check user tier limits before processing
    const { data: userLimits, error: limitsError } = await supabase
      .rpc('check_tier_limits', { user_id_param: receipt.user_id })

    if (limitsError) {
      console.error('❌ Error checking tier limits:', limitsError)
      return NextResponse.json(
        { error: 'Failed to check tier limits' },
        { status: 500 }
      )
    }

    const limits = userLimits?.[0]
    if (!limits?.can_process_receipt) {
      console.error('❌ User has exceeded receipt limit:', {
        user_id: receipt.user_id,
        receipts_remaining: limits?.receipts_remaining || 0
      })

      // Update receipt status to failed with limit exceeded message
      await supabase
        .from('receipts')
        .update({
          processing_status: 'failed',
          error_message: 'Receipt limit exceeded for current tier. Please upgrade your subscription.',
          updated_at: new Date().toISOString()
        })
        .eq('id', receipt.id)

      return NextResponse.json(
        { error: 'Receipt limit exceeded for current tier' },
        { status: 429 }
      )
    }
    
    // Create processing job
    const job: QueueJob = createProcessReceiptJob({
      receiptId: receipt.id,
      userId: receipt.user_id,
      priority: 'normal',
      imageUrl: receipt.file_path,
      fileName: receipt.original_file_name,
      fileSize: receipt.file_size || 0
    })
    
    console.log('🚀 Creating processing job:', {
      jobId: job.id,
      receiptId: job.receiptId,
      priority: job.priority
    })
    
    // Add job to Redis queue
    await addJobToQueue(job)

    // Update receipt status to processing
    const { error: updateError } = await supabase
      .from('receipts')
      .update({
        processing_status: 'processing',
        redis_job_id: job.id
      })
      .eq('id', receipt.id)

    if (updateError) {
      console.error('❌ Failed to update receipt status:', updateError)
      // Don't fail the webhook, job is already queued
    }

    // Send upload notification
    try {
      const uploadNotification = {
        event_type: 'receipt.uploaded' as const,
        user_id: receipt.user_id,
        receipt_id: receipt.id,
        message: `Receipt "${receipt.original_file_name}" uploaded and queued for processing`,
        data: {
          file_name: receipt.original_file_name,
          job_id: job.id
        },
        timestamp: new Date().toISOString()
      }

      await publishUserNotification(uploadNotification)
      await storeNotificationForPolling(receipt.user_id, uploadNotification)

      console.log('📢 Published upload notification')
    } catch (notificationError) {
      console.error('❌ Failed to publish upload notification:', notificationError)
    }
    
    // Trigger intensive polling for fast processing
    try {
      console.log('⚡ Triggering intensive polling and immediate processing...')

      // Call the pipeline API to process the job immediately
      const baseUrl = new URL(request.url).origin
      const pipelineResponse = await fetch(`${baseUrl}/api/pipeline`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Webhook-Trigger/1.0'
        }
      })

      if (pipelineResponse.ok) {
        const pipelineData = await pipelineResponse.json()
        console.log('✅ Immediate processing triggered:', pipelineData.success)
      } else {
        console.warn('⚠️ Immediate processing failed, intensive polling will handle it')
      }

      // Also trigger a notification for the frontend to start intensive polling
      try {
        await publishUserNotification(receipt.user_id, {
          event_type: 'webhook.triggered',
          receipt_id: receipt.id,
          message: 'Receipt upload detected - starting intensive processing',
          timestamp: new Date().toISOString()
        })
      } catch (notificationError) {
        console.warn('Failed to send webhook notification:', notificationError)
      }
    } catch (processingError) {
      console.warn('⚠️ Failed to trigger immediate processing:', processingError)
      // Don't fail the webhook, job is queued and will be processed by fallback
    }
    
    console.log('✅ Webhook processed successfully')
    
    return NextResponse.json({
      success: true,
      message: 'Receipt processing triggered',
      job_id: job.id,
      receipt_id: receipt.id
    })
    
  } catch (error) {
    console.error('❌ Webhook processing failed:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, x-webhook-signature',
    },
  })
}
