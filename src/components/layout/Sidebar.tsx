'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Upload,
  History,
  Sheet,
  BarChart,
  Settings,
  CreditCard,
  ChevronsLeft,
  ChevronsRight,
  LogOut,
  Lock,
  Menu,
  X,
  Mail,
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import NotificationCenter from '@/components/notifications/NotificationCenter';
import NoSSR from '@/components/common/NoSSR';
import { hasAnalyticsAccess, hasGmailAutoProcessingAccess } from '@/lib/subscription/tiers';

const navItems = [
  { href: '/dashboard', label: 'Dashboard', icon: LayoutDashboard },
  { href: '/dashboard/upload', label: 'Upload Receipts', icon: Upload },
  { href: '/dashboard/history', label: 'Receipt History', icon: History },
  { href: '/dashboard/sheets', label:'Google Sheets', icon: Sheet },
  { href: '/dashboard/analytics', label: 'Analytics', icon: Bar<PERSON><PERSON> },
  // Gmail feature temporarily disabled for Google verification
  // { href: '/dashboard/gmail', label: 'Gmail Processing', icon: Mail },
];

const settingsItems = [
  { href: '/dashboard/settings', label: 'Settings', icon: Settings },
];

export default function Sidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [userTier, setUserTier] = useState<string>('free');
  const [marketingOverride, setMarketingOverride] = useState<boolean>(false);
  const [loading, setLoading] = useState(true);

  const toggleSidebar = () => setIsCollapsed(!isCollapsed);
  const toggleMobileSidebar = () => setIsMobileOpen(!isMobileOpen);
  const closeMobileSidebar = () => setIsMobileOpen(false);

  // Fetch user tier function
  const fetchUserTier = async () => {
    try {
      const response = await fetch('/api/user/profile');
      const data = await response.json();
      if (data.success && data.user) {
        setUserTier(data.user.current_tier || 'free');
        setMarketingOverride(data.user.marketing_override || false);
      }
    } catch (error) {
      console.error('Error fetching user tier:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserTier();
  }, []);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      const response = await fetch('/auth/signout', {
        method: 'POST',
      });

      if (response.ok) {
        window.location.href = '/';
      } else {
        console.error('Logout failed');
        setIsLoggingOut(false);
      }
    } catch (error) {
      console.error('Error during logout:', error);
      setIsLoggingOut(false);
    }
  };

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={toggleMobileSidebar}
        className="md:hidden fixed top-4 left-4 z-50 p-2 bg-gray-800 text-white rounded-lg shadow-lg hover:bg-gray-700 transition-colors"
        aria-label="Toggle mobile menu"
      >
        {isMobileOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
      </button>

      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div 
          className="md:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
          onClick={closeMobileSidebar}
        />
      )}

      {/* Desktop Sidebar */}
      <aside
        className={cn(
          'hidden md:flex flex-col h-screen bg-gray-900 text-white transition-all duration-300 ease-in-out',
          isCollapsed ? 'w-20' : 'w-64'
        )}
      >
        {/* Clean Desktop Header - Logo and Notification Only */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          {/* Logo Only */}
          <div className="flex items-center">
            <img 
              src="/receiptlabs-logo.svg" 
              alt="Logo" 
              className={cn(
                "w-auto flex-shrink-0 transition-all duration-300",
                isCollapsed ? "h-10" : "h-12"
              )} 
            />
          </div>
          
          {/* Notification and Collapse Button */}
          <div className="flex items-center space-x-2">
            {/* Always show notification icon */}
            <div className="relative">
              <NoSSR fallback={<div className="w-8 h-8 bg-gray-800 rounded-lg animate-pulse" />}>
                <NotificationCenter />
              </NoSSR>
            </div>
            
            {/* Collapse/Expand Button */}
            <button 
              onClick={toggleSidebar} 
              className="p-2 rounded-lg hover:bg-gray-800 transition-colors duration-200"
              title={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
            >
              {isCollapsed ? <ChevronsRight className="w-4 h-4" /> : <ChevronsLeft className="w-4 h-4" />}
            </button>
          </div>
        </div>

        <nav className="flex-1 px-4 py-4 space-y-2">
          {navItems.map((item) => {
            const isActive = pathname === item.href;
            const isAnalytics = item.href === '/dashboard/analytics';
            const isGmail = item.href === '/dashboard/gmail';

            let hasAccess = true;
            let tierLabel = '';

            if (isAnalytics) {
              hasAccess = hasAnalyticsAccess(userTier, marketingOverride);
              tierLabel = 'Pro';
            } else if (isGmail) {
              hasAccess = hasGmailAutoProcessingAccess(userTier, marketingOverride);
              tierLabel = 'Business';
            }

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center p-2 rounded-lg transition-colors',
                  hasAccess
                    ? 'hover:bg-accent-pink/20'
                    : 'opacity-60 cursor-not-allowed',
                  isActive && hasAccess
                    ? 'bg-accent-pink text-white'
                    : hasAccess
                    ? 'hover:text-accent-pink'
                    : 'text-gray-500',
                  isCollapsed ? 'justify-center' : ''
                )}
                onClick={!hasAccess ? (e) => e.preventDefault() : undefined}
              >
                <div className="flex items-center">
                  <item.icon className="h-5 w-5" />
                  {!hasAccess && (isAnalytics || isGmail) && (
                    <Lock className="h-3 w-3 ml-1" />
                  )}
                </div>
                {!isCollapsed && (
                  <span className="ml-4 flex items-center gap-2">
                    {item.label}
                    {!hasAccess && (isAnalytics || isGmail) && (
                      <span className="text-xs bg-gray-700 px-2 py-1 rounded">
                        {tierLabel}
                      </span>
                    )}
                  </span>
                )}
              </Link>
            );
          })}
        </nav>

        <div className="px-4 py-4 border-t border-gray-800">
          <nav className="space-y-2">
            {settingsItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'flex items-center p-2 rounded-lg hover:bg-accent-pink/20',
                    isActive ? 'bg-accent-pink text-white' : 'hover:text-accent-pink',
                    isCollapsed ? 'justify-center' : ''
                  )}
                >
                  <item.icon className="h-5 w-5" />
                  {!isCollapsed && <span className="ml-4">{item.label}</span>}
                </Link>
              );
            })}

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className={cn(
                'w-full flex items-center p-2 rounded-lg hover:bg-red-500/20 hover:text-red-400 text-gray-400 transition-colors disabled:opacity-50',
                isCollapsed ? 'justify-center' : ''
              )}
            >
              <LogOut className="h-5 w-5" />
              {!isCollapsed && (
                <span className="ml-4">
                  {isLoggingOut ? 'Signing Out...' : 'Sign Out'}
                </span>
              )}
            </button>
          </nav>
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <aside
        className={cn(
          'md:hidden fixed left-0 top-0 h-screen bg-gray-900 text-white transition-transform duration-300 ease-in-out z-50 w-64',
          isMobileOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        {/* Clean Mobile Header - Logo and Notification Only */}
        <div className="flex items-center justify-between p-4 border-b border-gray-800">
          {/* Logo Only */}
          <div className="flex items-center">
            <img 
              src="/receiptlabs-logo.svg" 
              alt="Logo" 
              className="h-10 w-auto flex-shrink-0" 
            />
          </div>
          
          {/* Notification and Close Button */}
          <div className="flex items-center space-x-2">
            {/* Notification icon */}
            <div className="relative">
              <NoSSR fallback={<div className="w-8 h-8 bg-gray-800 rounded-lg animate-pulse" />}>
                <NotificationCenter />
              </NoSSR>
            </div>
            
            {/* Close Button */}
            <button 
              onClick={closeMobileSidebar} 
              className="p-2 rounded-lg hover:bg-gray-800 transition-colors duration-200"
              title="Close menu"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <nav className="flex-1 px-4 py-4 space-y-2">
          {navItems.map((item) => {
            const isActive = pathname === item.href;
            const isAnalytics = item.href === '/dashboard/analytics';
            const isGmail = item.href === '/dashboard/gmail';

            let hasAccess = true;
            let tierLabel = '';

            if (isAnalytics) {
              hasAccess = hasAnalyticsAccess(userTier, marketingOverride);
              tierLabel = 'Pro';
            } else if (isGmail) {
              hasAccess = hasGmailAutoProcessingAccess(userTier, marketingOverride);
              tierLabel = 'Business';
            }

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center p-2 rounded-lg transition-colors',
                  hasAccess
                    ? 'hover:bg-accent-pink/20'
                    : 'opacity-60 cursor-not-allowed',
                  isActive && hasAccess
                    ? 'bg-accent-pink text-white'
                    : hasAccess
                    ? 'hover:text-accent-pink'
                    : 'text-gray-500'
                )}
                onClick={!hasAccess ? (e) => e.preventDefault() : closeMobileSidebar}
              >
                <div className="flex items-center">
                  <item.icon className="h-5 w-5" />
                  {!hasAccess && (isAnalytics || isGmail) && (
                    <Lock className="h-3 w-3 ml-1" />
                  )}
                </div>
                <span className="ml-4 flex items-center gap-2">
                  {item.label}
                  {!hasAccess && (isAnalytics || isGmail) && (
                    <span className="text-xs bg-gray-700 px-2 py-1 rounded">
                      {tierLabel}
                    </span>
                  )}
                </span>
              </Link>
            );
          })}
        </nav>

        <div className="px-4 py-4 border-t border-gray-800">
          <nav className="space-y-2">
            {settingsItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={closeMobileSidebar}
                  className={cn(
                    'flex items-center p-2 rounded-lg hover:bg-accent-pink/20',
                    isActive ? 'bg-accent-pink text-white' : 'hover:text-accent-pink'
                  )}
                >
                  <item.icon className="h-5 w-5" />
                  <span className="ml-4">{item.label}</span>
                </Link>
              );
            })}

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              disabled={isLoggingOut}
              className="w-full flex items-center p-2 rounded-lg hover:bg-red-500/20 hover:text-red-400 text-gray-400 transition-colors disabled:opacity-50"
            >
              <LogOut className="h-5 w-5" />
              <span className="ml-4">
                {isLoggingOut ? 'Signing Out...' : 'Sign Out'}
              </span>
            </button>
          </nav>
        </div>
      </aside>
    </>
  );
}
