'use client';

import { useState } from 'react';
import { Download, FileText, Image, Loader2 } from 'lucide-react';
import { ReceiptData } from './types';
import { generateReceiptPDF } from './utils/pdfGenerator';
import { generateReceiptPNG } from './utils/pngGenerator';

interface ReceiptActionsProps {
  receiptData: ReceiptData;
  isGenerating: boolean;
  setIsGenerating: (generating: boolean) => void;
  onDownloadSuccess?: () => void;
}

export default function ReceiptActions({
  receiptData,
  isGenerating,
  setIsGenerating,
  onDownloadSuccess
}: ReceiptActionsProps) {
  const [downloadType, setDownloadType] = useState<'pdf' | 'png' | null>(null);

  const validateForm = () => {
    if (!receiptData.business.name.trim()) {
      alert('Please enter your business name');
      return false;
    }
    
    const hasValidItems = receiptData.items.some(item => 
      item.description.trim() && item.quantity > 0 && item.rate > 0
    );
    
    if (!hasValidItems) {
      alert('Please add at least one item with description, quantity, and rate');
      return false;
    }
    
    return true;
  };

  const handleDownloadPDF = async () => {
    if (!validateForm()) return;
    
    try {
      setIsGenerating(true);
      setDownloadType('pdf');
      await generateReceiptPDF(receiptData);
      onDownloadSuccess?.();
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setIsGenerating(false);
      setDownloadType(null);
    }
  };

  const handleDownloadPNG = async () => {
    if (!validateForm()) return;
    
    try {
      setIsGenerating(true);
      setDownloadType('png');
      await generateReceiptPNG(receiptData);
      onDownloadSuccess?.();
    } catch (error) {
      console.error('Error generating PNG:', error);
      alert('Failed to generate PNG. Please try again.');
    } finally {
      setIsGenerating(false);
      setDownloadType(null);
    }
  };

  const clearForm = () => {
    if (confirm('Are you sure you want to clear all form data?')) {
      window.location.reload();
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-t border-gray-700 pt-6">
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleDownloadPDF}
            disabled={isGenerating}
            className="flex items-center justify-center gap-2 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 min-w-[160px]"
          >
            {isGenerating && downloadType === 'pdf' ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <FileText className="w-5 h-5" />
            )}
            Download PDF
          </button>

          <button
            onClick={handleDownloadPNG}
            disabled={isGenerating}
            className="flex items-center justify-center gap-2 bg-gray-700 hover:bg-gray-600 disabled:bg-gray-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 min-w-[160px]"
          >
            {isGenerating && downloadType === 'png' ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <Image className="w-5 h-5" />
            )}
            Download PNG
          </button>
        </div>

        <div className="flex justify-center mt-4">
          <button
            onClick={clearForm}
            disabled={isGenerating}
            className="text-gray-400 hover:text-gray-300 transition-colors text-sm"
          >
            Clear Form
          </button>
        </div>
      </div>

      {/* Download Info */}
      <div className="bg-blue-600/20 border border-blue-600/30 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <Download className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
          <div className="text-sm">
            <p className="text-blue-300 font-medium mb-1">Download Information</p>
            <ul className="text-blue-200 space-y-1">
              <li>• PDF format is recommended for printing and professional use</li>
              <li>• PNG format is great for sharing and embedding in documents</li>
              <li>• All downloads are processed locally - your data stays private</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
