---
description: 
globs: 
alwaysApply: true
---
Receipt Processing App - Product Requirements Document (PRD)
1. Project Overview
1.1 Product Vision
A modern, AI-powered web application called RecoAI (short for Receipt Cognition) that transforms receipt management by automatically extracting structured data from receipt images and PDFS and seamlessly organizing it into Google Sheets for easy tracking, analysis, and export.
1.2 Technology Stack
•	Frontend: Next.js (App Router, TypeScript, Tailwind CSS), lucide icons
•	Backend: Supabase (PostgreSQL Database + Edge Functions)
•	Authentication: Supabase Auth with Google OAuth
•	Storage: Supabase Storage for receipt images
•	AI Processing: OpenAI GPT-4 Vision API
•	Export Integration: Google Sheets API v4
•	Payments: PayStack API
•	Queue & Cache: Upstash Redis (Job queues, rate limiting, caching)
•	Deployment: Vercel (Frontend) + Supabase (Backend)
1.3 Core Value Proposition
•	Automated: No manual data entry required
•	Organized: One Google Sheet per calendar year per user
•	Intelligent: AI-powered extraction with high accuracy
•	Scalable: Handles bulk uploads and background processing
•	Insightful: Provides spending analytics and trends
1.4 Target Users
•	Small Business Owners: Track expenses for tax purposes
•	Freelancers: Organize business receipts and invoices
•	Personal Users: Manage household expenses and budgets
•	Accountants: Process client receipts efficiently
________________________________________
2. Detailed System Architecture
2.1 Application Flow Architecture
User Upload → Next.js Frontend → Supabase Storage → Upstash Redis Queue → 
Edge Function → OpenAI Vision API → Structured Data → Google Sheets API → 
User Dashboard with Results
2.2 Background Processing Queue System (Upstash Redis)
Receipt Upload → Redis Queue Job → Worker Processing → Status Updates → 
Real-time Notifications → Completion
2.3 Data Flow Diagram
1.	Upload Phase: User uploads receipt(s) → Supabase Storage
2.	Queue Phase: Create processing job → Upstash Redis queue
3.	Processing Phase: Edge Function picks up job → OpenAI extraction
4.	Storage Phase: Save structured data → PostgreSQL
5.	Export Phase: Push data → Google Sheets
6.	Notification Phase: Update user → Real-time status
________________________________________
3. Database Schema & Architecture
3.1 Supabase PostgreSQL Tables
users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255),
  avatar_url TEXT,
  google_access_token TEXT,
  google_refresh_token TEXT,
  current_tier VARCHAR(20) DEFAULT 'free',
  receipts_processed INTEGER DEFAULT 0,
  subscription_status VARCHAR(20) DEFAULT 'inactive',
  subscription_end_date TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
receipts table
CREATE TABLE receipts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  original_file_name VARCHAR(255) NOT NULL,
  file_path TEXT NOT NULL, -- Supabase storage path
  file_size BIGINT,
  mime_type VARCHAR(100),
  
  -- Extracted data fields
  vendor VARCHAR(255),
  vendor_tax_id VARCHAR(100),
  receipt_date DATE,
  currency VARCHAR(10) DEFAULT 'KES',
  payment_method VARCHAR(50),
  subtotal DECIMAL(12,2),
  tax_rate_percent DECIMAL(5,2),
  tax_amount DECIMAL(12,2),
  total_amount DECIMAL(12,2),
  paid_amount DECIMAL(12,2),
  
  -- Processing metadata
  processing_status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
  confidence_score DECIMAL(3,2),
  extraction_method VARCHAR(20), -- openai, manual
  error_message TEXT,
  google_sheet_row_number INTEGER,
  redis_job_id VARCHAR(255), -- Upstash Redis job identifier
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_receipts_user_id ON receipts(user_id);
CREATE INDEX idx_receipts_status ON receipts(processing_status);
CREATE INDEX idx_receipts_date ON receipts(receipt_date);
CREATE INDEX idx_receipts_redis_job ON receipts(redis_job_id);
receipt_items table
CREATE TABLE receipt_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  receipt_id UUID REFERENCES receipts(id) ON DELETE CASCADE,
  description TEXT NOT NULL,
  quantity DECIMAL(8,2) DEFAULT 1,
  unit_price DECIMAL(10,2),
  total_price DECIMAL(10,2) NOT NULL,
  category VARCHAR(100),
  
  created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_receipt_items_receipt_id ON receipt_items(receipt_id);
CREATE INDEX idx_receipt_items_category ON receipt_items(category);
google_sheets table
CREATE TABLE google_sheets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  year INTEGER NOT NULL,
  sheet_id VARCHAR(255) NOT NULL, -- Google Sheet ID
  sheet_url TEXT NOT NULL,
  sheet_name VARCHAR(255) NOT NULL, -- e.g., "Receipts 2025"
  last_row_number INTEGER DEFAULT 1, -- Track last written row
  total_receipts INTEGER DEFAULT 0,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, year) -- One sheet per user per year
);
payment_transactions table
CREATE TABLE payment_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  paystack_reference VARCHAR(255) UNIQUE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(10) DEFAULT 'KES',
  status VARCHAR(20) NOT NULL, -- pending, successful, failed
  tier VARCHAR(20) NOT NULL, -- tier1, tier2
  receipts_allowance INTEGER NOT NULL,
  
  paystack_response JSONB, -- Store full PayStack response
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
user_analytics table
CREATE TABLE user_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  month_year VARCHAR(7) NOT NULL, -- Format: "2025-06"
  
  total_receipts INTEGER DEFAULT 0,
  total_spent DECIMAL(12,2) DEFAULT 0,
  top_vendor VARCHAR(255),
  top_category VARCHAR(100),
  average_receipt_amount DECIMAL(10,2) DEFAULT 0,
  
  category_breakdown JSONB, -- {"Food": 15000, "Transport": 5000}
  vendor_breakdown JSONB,
  
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  UNIQUE(user_id, month_year)
);
3.2 Supabase Storage Buckets
receipts bucket
-- Storage policies
CREATE POLICY "Users can upload their own receipts" 
ON storage.objects FOR INSERT 
WITH CHECK (auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can view their own receipts" 
ON storage.objects FOR SELECT 
USING (auth.uid()::text = (storage.foldername(name))[1]);

-- Bucket configuration
-- Max file size: 10MB
-- Allowed file types: image/jpeg, image/png, image/webp, application/pdf
-- Path structure: /{user_id}/{year}/{filename}
3.3 Row Level Security (RLS) Policies
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE receipts ENABLE ROW LEVEL SECURITY;
ALTER TABLE receipt_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE google_sheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_analytics ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can access own data" ON users FOR ALL USING (auth.uid() = id);
CREATE POLICY "Users can access own receipts" ON receipts FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can access own receipt items" ON receipt_items FOR ALL USING (
  auth.uid() = (SELECT user_id FROM receipts WHERE id = receipt_id)
);
-- Similar policies for other tables...
________________________________________
4. Upstash Redis Integration
4.1 Queue Management with Redis
Job Queue Structure
// Redis key patterns
const QUEUES = {
  HIGH_PRIORITY: 'queue:receipts:high',
  NORMAL_PRIORITY: 'queue:receipts:normal',
  LOW_PRIORITY: 'queue:receipts:low',
  PROCESSING: 'queue:receipts:processing',
  FAILED: 'queue:receipts:failed'
};

// Job data structure
interface QueueJob {
  id: string;
  receiptId: string;
  userId: string;
  priority: 'high' | 'normal' | 'low';
  attempts: number;
  maxAttempts: number;
  createdAt: string;
  processingStartedAt?: string;
  data: {
    imageUrl: string;
    fileName: string;
    fileSize: number;
  };
}
Queue Operations
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!
});

// Add job to queue
async function addJobToQueue(job: QueueJob): Promise<void> {
  const queueKey = getQueueKey(job.priority);
  const jobData = JSON.stringify(job);
  
  // Add to priority queue (higher score = higher priority)
  const score = job.priority === 'high' ? Date.now() + 1000000 : Date.now();
  await redis.zadd(queueKey, { score, member: jobData });
  
  // Set job tracking
  await redis.hset(`job:${job.id}`, {
    status: 'queued',
    queuedAt: new Date().toISOString(),
    attempts: 0
  });
  
  // Set TTL for job tracking (24 hours)
  await redis.expire(`job:${job.id}`, 86400);
}

// Get next job from queue
async function getNextJob(): Promise<QueueJob | null> {
  // Check high priority first
  for (const queueKey of [QUEUES.HIGH_PRIORITY, QUEUES.NORMAL_PRIORITY, QUEUES.LOW_PRIORITY]) {
    const jobs = await redis.zrange(queueKey, 0, 0, { withScores: true });
    if (jobs.length > 0) {
      const jobData = JSON.parse(jobs[0].member as string);
      
      // Move to processing queue
      await redis.zrem(queueKey, jobs[0].member);
      await redis.zadd(QUEUES.PROCESSING, { 
        score: Date.now(), 
        member: jobs[0].member 
      });
      
      return jobData;
    }
  }
  return null;
}

// Complete job
async function completeJob(jobId: string): Promise<void> {
  await redis.hset(`job:${jobId}`, {
    status: 'completed',
    completedAt: new Date().toISOString()
  });
  
  // Remove from processing queue
  const jobData = await redis.hget(`job:${jobId}`, 'data');
  if (jobData) {
    await redis.zrem(QUEUES.PROCESSING, jobData);
  }
}

// Fail job
async function failJob(jobId: string, error: string): Promise<void> {
  const job = await redis.hgetall(`job:${jobId}`);
  const attempts = parseInt(job.attempts || '0') + 1;
  const maxAttempts = 3;
  
  if (attempts < maxAttempts) {
    // Retry job
    await redis.hset(`job:${jobId}`, {
      status: 'retrying',
      attempts: attempts.toString(),
      lastError: error
    });
    
    // Re-queue with exponential backoff
    const delay = Math.pow(2, attempts) * 1000; // 2s, 4s, 8s
    setTimeout(async () => {
      // Add back to queue
      const jobData = await redis.hget(`job:${jobId}`, 'data');
      if (jobData) {
        await redis.zadd(QUEUES.NORMAL_PRIORITY, { 
          score: Date.now(), 
          member: jobData 
        });
      }
    }, delay);
  } else {
    // Max attempts reached
    await redis.hset(`job:${jobId}`, {
      status: 'failed',
      failedAt: new Date().toISOString(),
      finalError: error
    });
    
    // Move to failed queue
    const jobData = await redis.hget(`job:${jobId}`, 'data');
    if (jobData) {
      await redis.zrem(QUEUES.PROCESSING, jobData);
      await redis.zadd(QUEUES.FAILED, { 
        score: Date.now(), 
        member: jobData 
      });
    }
  }
}
4.2 Rate Limiting with Redis
Rate Limiting Implementation
// Rate limiting configurations
const RATE_LIMITS = {
  free: { requests: 10, window: 3600 }, // 10 requests per hour
  tier1: { requests: 500, window: 3600 }, // 500 requests per hour
  tier2: { requests: 2000, window: 3600 } // 2000 requests per hour
};

// Sliding window rate limiter
async function checkRateLimit(userId: string, tier: string): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
  const limit = RATE_LIMITS[tier as keyof typeof RATE_LIMITS];
  const key = `rate_limit:${userId}`;
  const now = Date.now();
  const window = limit.window * 1000; // Convert to milliseconds
  
  // Remove old entries outside the window
  await redis.zremrangebyscore(key, 0, now - window);
  
  // Count current requests
  const currentCount = await redis.zcard(key);
  
  if (currentCount >= limit.requests) {
    // Rate limit exceeded
    const oldestEntry = await redis.zrange(key, 0, 0, { withScores: true });
    const resetTime = oldestEntry.length > 0 ? 
      (oldestEntry[0].score as number) + window : 
      now + window;
    
    return {
      allowed: false,
      remaining: 0,
      resetTime: Math.ceil(resetTime / 1000)
    };
  }
  
  // Add current request
  await redis.zadd(key, { score: now, member: `${now}-${Math.random()}` });
  await redis.expire(key, limit.window);
  
  return {
    allowed: true,
    remaining: limit.requests - currentCount - 1,
    resetTime: Math.ceil((now + window) / 1000)
  };
}

// Rate limiting middleware
export async function rateLimitMiddleware(req: NextRequest, userId: string, tier: string) {
  const rateLimit = await checkRateLimit(userId, tier);
  
  if (!rateLimit.allowed) {
    return new Response(JSON.stringify({
      error: 'Rate limit exceeded',
      resetTime: rateLimit.resetTime
    }), {
      status: 429,
      headers: {
        'X-RateLimit-Limit': RATE_LIMITS[tier as keyof typeof RATE_LIMITS].requests.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': rateLimit.resetTime.toString()
      }
    });
  }
  
  return {
    headers: {
      'X-RateLimit-Limit': RATE_LIMITS[tier as keyof typeof RATE_LIMITS].requests.toString(),
      'X-RateLimit-Remaining': rateLimit.remaining.toString(),
      'X-RateLimit-Reset': rateLimit.resetTime.toString()
    }
  };
}
4.3 Caching with Redis
Cache Implementation
// Cache configurations
const CACHE_KEYS = {
  USER_PROFILE: (userId: string) => `cache:user:${userId}`,
  USER_ANALYTICS: (userId: string, month: string) => `cache:analytics:${userId}:${month}`,
  RECEIPT_DATA: (receiptId: string) => `cache:receipt:${receiptId}`,
  GOOGLE_SHEETS: (userId: string, year: number) => `cache:sheets:${userId}:${year}`,
  MONTHLY_STATS: (userId: string) => `cache:monthly_stats:${userId}`
};

const CACHE_TTL = {
  USER_PROFILE: 3600, // 1 hour
  USER_ANALYTICS: 1800, // 30 minutes
  RECEIPT_DATA: 7200, // 2 hours
  GOOGLE_SHEETS: 1800, // 30 minutes
  MONTHLY_STATS: 900 // 15 minutes
};

// Generic cache operations
async function cacheSet<T>(key: string, data: T, ttl: number): Promise<void> {
  await redis.setex(key, ttl, JSON.stringify(data));
}

async function cacheGet<T>(key: string): Promise<T | null> {
  const data = await redis.get(key);
  return data ? JSON.parse(data as string) : null;
}

async function cacheDelete(key: string): Promise<void> {
  await redis.del(key);
}

// Specific cache functions
async function cacheUserProfile(userId: string, profile: any): Promise<void> {
  await cacheSet(CACHE_KEYS.USER_PROFILE(userId), profile, CACHE_TTL.USER_PROFILE);
}

async function getCachedUserProfile(userId: string): Promise<any | null> {
  return await cacheGet(CACHE_KEYS.USER_PROFILE(userId));
}

async function cacheUserAnalytics(userId: string, month: string, analytics: any): Promise<void> {
  await cacheSet(CACHE_KEYS.USER_ANALYTICS(userId, month), analytics, CACHE_TTL.USER_ANALYTICS);
}

async function getCachedUserAnalytics(userId: string, month: string): Promise<any | null> {
  return await cacheGet(CACHE_KEYS.USER_ANALYTICS(userId, month));
}

// Cache invalidation
async function invalidateUserCache(userId: string): Promise<void> {
  const keys = await redis.keys(`cache:*:${userId}*`);
  if (keys.length > 0) {
    await redis.del(...keys);
  }
}
________________________________________
5. User Experience & Interface Design
5.1 Design System
Color Palette
•	Primary Dark: #0a0a0a (Background)
•	Secondary Dark: #1a1a1a (Cards/Containers)
•	Accent Pink: #ff006e (Primary actions)
•	Accent Orange: #fb8500 (Secondary actions)
•	Accent Purple: #8338ec (Highlights)
•	Text Primary: #ffffff (Headings)
•	Text Secondary: #a3a3a3 (Body text)
•	Success: #00f5ff (Neon cyan)
•	Error: #ff073a (Bright red)
Typography
•	Headings: Inter Bold (24px, 32px, 40px)
•	Body: Inter Regular (16px, 18px)
•	Captions: Inter Light (14px)
•	Monospace: JetBrains Mono (Code/numbers)
UI Effects
•	Glassmorphism: backdrop-blur-md bg-white/10
•	Gradients: bg-gradient-to-r from-pink-500 to-orange-500
•	Neon Glow: shadow-[0_0_20px_rgba(255,0,110,0.3)]
•	Hover Effects: Scale transforms and glow intensification
5.2 Page Layouts & Components
Landing Page
•	Header (Logo, Login/Signup)
•	Hero Section (Animated 3D receipt, Value proposition)
•	Features Section (Upload, Extract, Export cards)
•	Pricing Section (Tier comparison)
•	Footer (Links, Social)
Dashboard Layout Sidebar Navigation:
•	Dashboard (Overview)
•	Upload Receipts
•	Receipt History
•	Google Sheets
•	Analytics
•	Settings
•	Billing
Main Content Area:
•	Dynamic based on selected nav item
•	Real-time notifications bar
•	Progress indicators for processing
Component Library
•	ReceiptCard: Displays receipt preview with status
•	UploadZone: Drag & drop with preview
•	ProgressBar: Processing status indicator
•	DataTable: Sortable receipt list
•	StatsCard: Analytics display
•	TierBadge: Current subscription tier
•	PaymentModal: PayStack integration
________________________________________
6. User Flow & Feature Specifications
6.1 Authentication & Onboarding
Step 1: Quick Start (30 seconds)
1.	Landing Page: User clicks "Get Started"
2.	Google OAuth: One-click sign up with Google
3.	Auto-Setup: System creates: 
o	User profile in database
o	Google Sheet for current year
o	Welcome notification
4.	Sample Upload: Interactive tutorial with demo receipt
5.	Instant Results: Show extracted data in real-time
6.	Success Message: "Your receipt data is now in Google Sheets!"
Step 2: Enhanced Setup (2 minutes)
1.	Google Drive Integration: Optional connection
2.	Notification Preferences: in-app settings
3.	Data Categories: Customize expense categories
Step 3: Feature Discovery (As needed)
1.	Bulk Upload Tutorial: Guided walkthrough
2.	Analytics Overview: Show insights capabilities
3.	Export Options: Demonstrate download features
4.	Billing Setup: Introduce pricing tiers
6.2 Receipt Processing Workflow
Upload Methods
1.	Single Upload:
o	Drag & drop zone
o	File picker button
o	Mobile camera capture
o	Paste from clipboard
2.	Bulk Upload:
o	Multiple file selection
o	Folder upload
o	ZIP file extraction
o	Progress tracking for each file
3.	Google Drive Integration:
o	OAuth connection
o	Folder selection
o	Automatic sync options
o	Duplicate detection
Processing Pipeline
1.	File Validation:
o	Check file type (JPEG, PNG, WebP, PDF)
o	Verify file size (< 10MB)
o	Rate limiting check
2.	Queue Management:
o	Add to Upstash Redis queue
o	Assign priority based on user tier
o	Estimate processing time
o	Send confirmation to user
3.	AI Extraction (Edge Function):
o	Load image from Supabase Storage
o	Call OpenAI GPT-4 Vision API
o	Parse JSON response
o	Validate extracted fields
o	Calculate confidence score
4.	Data Processing:
o	Save to receipts table
o	Create receipt_items entries
o	Update Redis job status
o	Generate analytics data
5.	Google Sheets Export:
o	Get or create year-specific sheet
o	Format data according to schema
o	Write each item as separate row
o	Update sheet metadata
o	Send completion notification
6.3 Google Sheets Integration
Sheet Creation Process
1.	Authorization: Request Google Sheets API access
2.	Sheet Naming: "Receipts {YEAR} - {USER_NAME}"
3.	Header Setup: Create column headers
4.	Formatting: Apply consistent styling
5.	Permissions: Set appropriate sharing settings
Data Export Schema Each receipt item creates one row with these columns:
Column	Data Type	Example	Description
Receipt ID	String	REC_20250603_001	Unique identifier
Vendor	String	Monacoo	Business name
Vendor Tax ID	String	P051234567M	Tax identification
Date	Date	2025-06-03	Receipt date
Currency	String	KES	Currency code
Payment Method	String	Cash	Payment type
Item Description	String	Milk	Product/service
Item Category	String	Food Supplies	Expense category
Item Total	Number	1050.00	Individual item cost
Subtotal	Number	7050.00	Pre-tax total
Tax Rate %	Number	1.0	Tax percentage
Tax Amount	Number	70.50	Tax amount
Total Amount	Number	6979.50	Final total
Source File	String	receipt0366.png	Original filename
Sheet Management Features
•	Auto-categorization: AI-suggested categories
•	Data validation: Prevent invalid entries
•	Conditional formatting: Highlight important data
•	Summary sections: Monthly/quarterly totals
•	Export options: CSV, Excel, PDF downloads
________________________________________
