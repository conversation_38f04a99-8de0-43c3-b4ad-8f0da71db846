'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { Upload, FileImage, X, AlertCircle, Trash2, Eye, FileText, Loader2 } from 'lucide-react'
import { useClientOnly } from '@/hooks/useClientOnly'

interface UploadZoneProps {
  onFileSelect?: (file: File) => void
  onBatchFileSelect?: (files: File[]) => void
  onFileRemove?: (fileId: string, file: File) => void
  isUploading?: boolean
  error?: string | null
  maxSizeInMB?: number
  acceptedTypes?: string[]
  allowBatch?: boolean
  maxFiles?: number
}

interface FileWithPreview {
  id: string
  file: File
  previewUrl?: string
  isPdf?: boolean
  isProcessing?: boolean
  processingError?: string
}

export default function UploadZone({
  onFileSelect,
  onBatchFileSelect,
  onFileRemove,
  isUploading = false,
  error = null,
  maxSizeInMB = 10,
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf'],
  allowBatch = false,
  maxFiles = 10
}: UploadZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<FileWithPreview[]>([])
  const [validationError, setValidationError] = useState<string | null>(null)
  const [processingFiles, setProcessingFiles] = useState<Set<string>>(new Set())
  const [pdfProcessingSupported, setPdfProcessingSupported] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const isClient = useClientOnly()

  // Check PDF processing support on client side only
  useEffect(() => {
    const checkPdfSupport = async () => {
      // Only run on client side
      if (!isClient || typeof window === 'undefined') {
        setPdfProcessingSupported(false)
        return
      }

      try {
        const { isPdfProcessingSupported } = await import('@/lib/pdf-processor-client')
        setPdfProcessingSupported(isPdfProcessingSupported())
      } catch (error) {
        console.warn('Failed to load PDF processor:', error)
        setPdfProcessingSupported(false)
      }
    }

    checkPdfSupport()
  }, [isClient])

  const createFilePreview = useCallback((file: File): FileWithPreview => {
    const fileWithPreview: FileWithPreview = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      file,
      isPdf: file.type === 'application/pdf',
      isProcessing: false
    }

    // Create preview for images
    if (file.type.startsWith('image/')) {
      fileWithPreview.previewUrl = URL.createObjectURL(file)
    }

    return fileWithPreview
  }, [])

  const validateFile = useCallback((file: File): string | null => {
    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} is not supported. Please upload JPG, PNG, WebP, or PDF files.`
    }

    // Check PDF processing support
    if (file.type === 'application/pdf' && !pdfProcessingSupported) {
      return `PDF processing is not supported in this browser. Please use a modern browser or convert your PDF to an image first.`
    }

    // Check file size (different limits for PDFs vs images)
    const maxSize = file.type === 'application/pdf' ? 5 : maxSizeInMB
    const maxSizeInBytes = maxSize * 1024 * 1024
    if (file.size > maxSizeInBytes) {
      return `File size exceeds ${maxSize}MB limit. Please choose a smaller file.`
    }

    return null
  }, [acceptedTypes, maxSizeInMB, pdfProcessingSupported])

  // Process PDF files by converting them to images
  const processPdfFile = useCallback(async (file: File, fileId: string): Promise<File> => {
    setProcessingFiles(prev => new Set([...prev, fileId]))

    try {
      // Only process on client side
      if (typeof window === 'undefined') {
        throw new Error('PDF processing not available on server side')
      }

      const { convertPdfToImage } = await import('@/lib/pdf-processor-client')
      const result = await convertPdfToImage(file)

      if (!result.success || !result.imageFile) {
        throw new Error(result.error || 'Failed to convert PDF')
      }

      return result.imageFile
    } finally {
      setProcessingFiles(prev => {
        const newSet = new Set(prev)
        newSet.delete(fileId)
        return newSet
      })
    }
  }, [])

  const handleFiles = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files)
    const validFiles: File[] = []
    const errors: string[] = []

    // Clear any previous validation errors
    setValidationError(null)

    // Check total file count for batch uploads
    if (allowBatch && selectedFiles.length + fileArray.length > maxFiles) {
      const errorMessage = `Cannot upload more than ${maxFiles} files at once. You have ${selectedFiles.length} files selected and are trying to add ${fileArray.length} more.`
      setValidationError(errorMessage)
      return
    }

    // Validate each file
    fileArray.forEach(file => {
      const validationError = validateFile(file)
      if (validationError) {
        errors.push(`${file.name}: ${validationError}`)
      } else {
        validFiles.push(file)
      }
    })

    if (errors.length > 0) {
      setValidationError(errors.join(', '))
      return
    }

    // Create file previews first
    const newFiles = validFiles.map(createFilePreview)

    if (allowBatch) {
      setSelectedFiles(prev => [...prev, ...newFiles])
    } else {
      setSelectedFiles(newFiles)
    }

    // Process files (convert PDFs to images if needed)
    const processedFiles: File[] = []
    const processingErrors: string[] = []

    for (const fileWithPreview of newFiles) {
      try {
        if (fileWithPreview.isPdf) {
          const convertedFile = await processPdfFile(fileWithPreview.file, fileWithPreview.id)
          processedFiles.push(convertedFile)
        } else {
          processedFiles.push(fileWithPreview.file)
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        processingErrors.push(`${fileWithPreview.file.name}: ${errorMessage}`)
      }
    }

    if (processingErrors.length > 0) {
      setValidationError(processingErrors.join(', '))
      return
    }

    // Call callbacks with processed files
    if (allowBatch) {
      if (onBatchFileSelect) {
        const allFiles = [...selectedFiles.map(f => f.file), ...processedFiles]
        onBatchFileSelect(allFiles)
      }
    } else {
      if (processedFiles.length > 0 && onFileSelect) {
        onFileSelect(processedFiles[0])
      }
    }
  }, [allowBatch, selectedFiles, maxFiles, validateFile, createFilePreview, onFileSelect, onBatchFileSelect, processPdfFile])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    if (isUploading) return
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      handleFiles(files)
    }
  }, [handleFiles, isUploading])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFiles(files)
    }
    
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }, [handleFiles])

  const handleRemoveFile = useCallback((fileId: string) => {
    const fileToRemove = selectedFiles.find(f => f.id === fileId)
    if (!fileToRemove) return

    // Clean up preview URL
    if (fileToRemove.previewUrl) {
      URL.revokeObjectURL(fileToRemove.previewUrl)
    }

    // Remove from state
    setSelectedFiles(prev => prev.filter(f => f.id !== fileId))

    // Call remove callback
    if (onFileRemove) {
      onFileRemove?.(fileId, fileToRemove.file)
    }

    // Update batch callback with remaining files
    if (allowBatch && onBatchFileSelect) {
      const remainingFiles = selectedFiles.filter(f => f.id !== fileId).map(f => f.file)
      onBatchFileSelect(remainingFiles)
    }
  }, [selectedFiles, onFileRemove, allowBatch, onBatchFileSelect])

  const handleClick = useCallback(() => {
    if (!isUploading && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [isUploading])

  const handleClearAll = useCallback(() => {
    // Clean up all preview URLs
    selectedFiles.forEach(file => {
      if (file.previewUrl) {
        URL.revokeObjectURL(file.previewUrl)
      }
    })

    // Clear all files
    setSelectedFiles([])
    setValidationError(null)

    // Update batch callback with empty array
    if (allowBatch && onBatchFileSelect) {
      onBatchFileSelect([])
    }
  }, [selectedFiles, allowBatch, onBatchFileSelect])

  // Clean up preview URLs on unmount
  React.useEffect(() => {
    return () => {
      selectedFiles.forEach(file => {
        if (file.previewUrl) {
          URL.revokeObjectURL(file.previewUrl)
        }
      })
    }
  }, [])

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // Prevent rendering during SSR to avoid any potential issues
  if (!isClient) {
    return (
      <div className="w-full max-w-4xl mx-auto">
        <div className="border-2 border-dashed border-gray-600 rounded-xl p-6 text-center bg-gray-800/50">
          <div className="flex flex-col items-center space-y-4">
            <Upload className="w-12 h-12 text-gray-400" />
            <div>
              <h3 className="text-base font-semibold text-white mb-2">Loading Upload Zone...</h3>
              <p className="text-gray-400 text-sm">Please wait while the upload functionality loads.</p>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Upload Zone */}
      {(!allowBatch && selectedFiles.length === 0) || (allowBatch && selectedFiles.length < maxFiles) ? (
        <div
          className={`
            relative border-2 border-dashed rounded-xl p-6 text-center cursor-pointer
            transition-all duration-200 ease-in-out
            ${isDragOver
              ? 'border-pink-500 bg-pink-500/10 scale-105'
              : 'border-gray-600 hover:border-gray-500 bg-gray-800/50'
            }
            ${isUploading ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={acceptedTypes.join(',')}
            onChange={handleFileInputChange}
            className="hidden"
            disabled={isUploading}
            multiple={allowBatch}
          />

          <div className="flex flex-col items-center space-y-4">
            <div className={`
              w-16 h-16 rounded-full flex items-center justify-center
              ${isDragOver ? 'bg-pink-500/20' : 'bg-gray-700/50'}
              transition-colors duration-200
            `}>
              <Upload className={`w-8 h-8 ${isDragOver ? 'text-pink-400' : 'text-gray-400'}`} />
            </div>

            <div>
              <h3 className="text-base font-semibold text-white mb-2">
                {isDragOver
                  ? `Drop your receipt${allowBatch ? 's' : ''} here`
                  : `Upload Receipt${allowBatch ? 's' : ''}`
                }
              </h3>
              <p className="text-gray-400 mb-3 text-sm">
                Drag and drop your receipt {allowBatch ? 'files' : 'file'}, or click to browse
              </p>
              <div className="text-xs text-gray-500">
                <p>Supported formats: JPG, PNG, WebP, PDF</p>
                <p>Maximum size: {maxSizeInMB}MB for images, 5MB for PDFs</p>
                <p className="text-green-400 mt-1">✨ PDFs are automatically converted to images for processing</p>
                {allowBatch && <p>Maximum files: {maxFiles}</p>}
                {allowBatch && selectedFiles.length > 0 && (
                  <p className="text-pink-400 mt-2">
                    {selectedFiles.length} / {maxFiles} files selected
                  </p>
                )}
              </div>
            </div>

            {!isUploading && (
              <button
                type="button"
                className="bg-pink-600 hover:bg-pink-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200"
              >
                Choose File{allowBatch ? 's' : ''}
              </button>
            )}

            {isUploading && (
              <div className="flex items-center space-x-2 text-pink-400">
                <div className="w-4 h-4 border-2 border-pink-400 border-t-transparent rounded-full animate-spin"></div>
                <span>Uploading...</span>
              </div>
            )}
          </div>
        </div>
      ) : null}

      {/* File Previews */}
      {selectedFiles.length > 0 && (
        <div className={`${selectedFiles.length === 0 || (!allowBatch && selectedFiles.length > 0) ? 'mt-6' : 'mt-6'}`}>
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-semibold text-white">
              Selected File{selectedFiles.length > 1 ? 's' : ''} ({selectedFiles.length})
            </h4>
            {allowBatch && (
              <button
                onClick={handleClearAll}
                className="text-red-400 hover:text-red-300 text-xs flex items-center space-x-1"
              >
                <Trash2 className="w-3 h-3" />
                <span>Clear All</span>
              </button>
            )}
          </div>

          <div className={allowBatch ? 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2' : ''}>
            {selectedFiles.map((fileWithPreview, index) => (
              <div
                key={fileWithPreview.id}
                className="bg-gray-800/50 border border-gray-700 rounded-lg p-2"
              >
                <div className="flex flex-col items-center space-y-2">
                  {/* Preview Image or File Icon */}
                  <div className="relative">
                    {fileWithPreview.previewUrl ? (
                      <img
                        src={fileWithPreview.previewUrl}
                        alt="Receipt preview"
                        className="w-12 h-12 object-cover rounded border border-gray-600"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-700 rounded border border-gray-600 flex items-center justify-center">
                        {fileWithPreview.isPdf ? (
                          <FileText className="w-6 h-6 text-red-400" />
                        ) : (
                          <FileImage className="w-6 h-6 text-gray-400" />
                        )}
                      </div>
                    )}

                    {/* Processing indicator for PDFs */}
                    {fileWithPreview.isPdf && processingFiles.has(fileWithPreview.id) && (
                      <div className="absolute inset-0 bg-black/50 rounded border border-gray-600 flex items-center justify-center">
                        <Loader2 className="w-4 h-4 text-pink-400 animate-spin" />
                      </div>
                    )}

                    {/* Remove button overlay */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleRemoveFile(fileWithPreview.id)
                      }}
                      className="absolute -top-1 -right-1 bg-red-500 hover:bg-red-600 text-white rounded-full w-4 h-4 flex items-center justify-center"
                      title="Remove"
                    >
                      <X className="w-2 h-2" />
                    </button>
                  </div>

                  {/* File Info */}
                  <div className="text-center w-full">
                    <p className="text-white text-xs font-medium truncate" title={fileWithPreview.file.name}>
                      {fileWithPreview.file.name.length > 12
                        ? fileWithPreview.file.name.substring(0, 12) + '...'
                        : fileWithPreview.file.name}
                    </p>
                    <p className="text-gray-400 text-xs">
                      {formatFileSize(fileWithPreview.file.size)}
                    </p>
                    {fileWithPreview.isPdf && (
                      <p className="text-xs mt-1">
                        {processingFiles.has(fileWithPreview.id) ? (
                          <span className="text-pink-400">Converting...</span>
                        ) : (
                          <span className="text-green-400">PDF</span>
                        )}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Error Display */}
      {(error || validationError) && (
        <div className="mt-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <div className="flex items-center space-x-3 text-red-400">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <p className="text-sm">{error || validationError}</p>
          </div>
        </div>
      )}
    </div>
  )
} 