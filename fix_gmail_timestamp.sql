-- Add marketing override flag for full feature access
-- Run this in Supabase SQL Editor
-- Date: 2025-06-29

-- Add marketing override column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS marketing_override BOOLEAN DEFAULT FALSE;

-- Add comment to explain the column
COMMENT ON COLUMN users.marketing_override IS 'Override flag to give full access to all features regardless of tier (for marketing/admin purposes)';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_users_marketing_override ON users(marketing_override) WHERE marketing_override = TRUE;

-- Set your account to have marketing override (replace with your actual email)
-- UPDATE users SET marketing_override = TRUE WHERE email = '<EMAIL>';

-- Example: Uncomment and replace with your actual email
-- UPDATE users SET marketing_override = TRUE WHERE email = '<EMAIL>';
