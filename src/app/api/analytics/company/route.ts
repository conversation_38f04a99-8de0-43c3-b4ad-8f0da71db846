import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();
    const { vendor } = await request.json();

    if (!vendor) {
      return NextResponse.json(
        { error: 'Vendor name is required' },
        { status: 400 }
      );
    }

    // Get the current user
    const {
      data: { user },
    } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get all receipts for this vendor
    const { data: receipts, error: receiptsError } = await supabase
      .from('receipts')
      .select(`
        id,
        vendor,
        receipt_date,
        total_amount,
        payment_method,
        processing_status,
        created_at
      `)
      .eq('user_id', user.id)
      .eq('vendor', vendor)
      .eq('processing_status', 'completed')
      .order('receipt_date', { ascending: false });

    if (receiptsError) {
      console.error('Error fetching receipts:', receiptsError);
      return NextResponse.json(
        { error: 'Failed to fetch receipts' },
        { status: 500 }
      );
    }

    // Get receipt items for category breakdown
    const receiptIds = receipts?.map(r => r.id) || [];
    const { data: receiptItems, error: itemsError } = await supabase
      .from('receipt_items')
      .select('category, total_price, receipt_id')
      .in('receipt_id', receiptIds);

    if (itemsError) {
      console.error('Error fetching receipt items:', itemsError);
      return NextResponse.json(
        { error: 'Failed to fetch receipt items' },
        { status: 500 }
      );
    }

    // Calculate basic metrics
    const totalSpent = receipts?.reduce((sum, r) => sum + (r.total_amount || 0), 0) || 0;
    const receiptCount = receipts?.length || 0;
    const averageAmount = receiptCount > 0 ? totalSpent / receiptCount : 0;

    // Calculate monthly trend (last 12 months)
    const monthlyTrend = calculateMonthlyTrend(receipts || []);

    // Calculate category breakdown
    const categoryBreakdown = calculateCategoryBreakdown(receiptItems || []);

    const companyData = {
      vendor,
      totalSpent,
      receiptCount,
      averageAmount,
      monthlyTrend,
      categoryBreakdown,
      receipts: receipts?.slice(0, 10) || [] // Return latest 10 receipts
    };

    return NextResponse.json(companyData);

  } catch (error) {
    console.error('Error in company analytics API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function calculateMonthlyTrend(receipts: any[]): { month: string; total: number }[] {
  const monthlyTotals: Record<string, number> = {};

  receipts.forEach(receipt => {
    if (receipt.receipt_date) {
      const monthKey = receipt.receipt_date.slice(0, 7); // YYYY-MM
      monthlyTotals[monthKey] = (monthlyTotals[monthKey] || 0) + (receipt.total_amount || 0);
    }
  });

  // Get last 12 months
  const now = new Date();
  const months: { month: string; total: number }[] = [];
  
  for (let i = 11; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthKey = date.toISOString().slice(0, 7);
    const monthName = date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    
    months.push({
      month: monthName,
      total: monthlyTotals[monthKey] || 0
    });
  }

  return months;
}

function calculateCategoryBreakdown(receiptItems: any[]): { category: string; total: number }[] {
  const categoryTotals: Record<string, number> = {};

  receiptItems.forEach(item => {
    const category = item.category || 'Other';
    categoryTotals[category] = (categoryTotals[category] || 0) + (item.total_price || 0);
  });

  return Object.entries(categoryTotals)
    .map(([category, total]) => ({ category, total }))
    .sort((a, b) => b.total - a.total);
}
