import { NextRequest, NextResponse } from 'next/server';
import { redis, isRedisAvailable } from '@/lib/redis';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing Redis connection...');
    
    // Check if Redis is configured
    if (!isRedisAvailable()) {
      return NextResponse.json({
        success: false,
        error: 'Redis not configured - using mock Redis',
        usingMock: true,
        timestamp: new Date().toISOString()
      });
    }

    // Test basic Redis operations
    const testKey = `test:${Date.now()}`;
    const testValue = 'Hello Redis!';
    
    // Set a value
    await redis.set(testKey, testValue);
    console.log('✅ Redis SET operation successful');
    
    // Get the value
    const retrievedValue = await redis.get(testKey);
    console.log('✅ Redis GET operation successful');
    
    // Clean up
    await redis.del(testKey);
    console.log('✅ Redis DEL operation successful');
    
    // Test if values match
    const success = retrievedValue === testValue;
    
    return NextResponse.json({
      success,
      message: success ? 'Redis is working correctly!' : 'Redis test failed',
      testResults: {
        setValue: testValue,
        retrievedValue,
        valuesMatch: success
      },
      redisAvailable: true,
      usingMock: false,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Redis test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown Redis error',
      redisAvailable: isRedisAvailable(),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
