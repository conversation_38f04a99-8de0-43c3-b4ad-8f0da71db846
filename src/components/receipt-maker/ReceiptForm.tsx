'use client';

import { useEffect } from 'react';
import BusinessSection from './sections/BusinessSection';
import CustomerSection from './sections/CustomerSection';
import ReceiptDetailsSection from './sections/ReceiptDetailsSection';
import CurrencySection from './sections/CurrencySection';
import ItemsSection from './sections/ItemsSection';
import NotesSection from './sections/NotesSection';
import { ReceiptData } from './types';

interface ReceiptFormProps {
  receiptData: ReceiptData;
  updateReceiptData: (updates: Partial<ReceiptData>) => void;
  calculateTotals: (items: any[], taxRate: number, discountAmount: number) => any;
}

export default function ReceiptForm({ 
  receiptData, 
  updateReceiptData, 
  calculateTotals 
}: ReceiptFormProps) {
  
  // Recalculate totals whenever items, tax, or discount changes
  useEffect(() => {
    const { subtotal, taxAmount, discountAmount, total } = calculateTotals(
      receiptData.items,
      receiptData.tax.rate,
      receiptData.discount
    );

    updateReceiptData({
      tax: { ...receiptData.tax, amount: taxAmount },
      totals: { subtotal, total }
    });
  }, [receiptData.items, receiptData.tax.rate, receiptData.discount]);

  return (
    <div className="space-y-8">
      {/* Business Information */}
      <BusinessSection 
        business={receiptData.business}
        updateBusiness={(business) => updateReceiptData({ business })}
      />

      {/* Customer Information */}
      <CustomerSection 
        customer={receiptData.customer}
        updateCustomer={(customer) => updateReceiptData({ customer })}
      />

      {/* Receipt Details */}
      <ReceiptDetailsSection
        receipt={receiptData.receipt}
        updateReceipt={(receipt) => updateReceiptData({ receipt })}
      />

      {/* Currency Selection */}
      <CurrencySection
        currency={receiptData.currency}
        updateCurrency={(currency) => updateReceiptData({ currency })}
      />

      {/* Items */}
      <ItemsSection
        items={receiptData.items}
        tax={receiptData.tax}
        discount={receiptData.discount}
        totals={receiptData.totals}
        currency={receiptData.currency}
        updateItems={(items) => updateReceiptData({ items })}
        updateTax={(tax) => updateReceiptData({ tax })}
        updateDiscount={(discount) => updateReceiptData({ discount })}
      />

      {/* Notes */}
      <NotesSection 
        notes={receiptData.notes}
        updateNotes={(notes) => updateReceiptData({ notes })}
      />
    </div>
  );
}
