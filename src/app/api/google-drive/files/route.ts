import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { listReceiptFilesInFolder } from '@/lib/google-drive/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const folderId = searchParams.get('folderId');

    if (!folderId) {
      return NextResponse.json(
        { success: false, error: 'Folder ID is required' },
        { status: 400 }
      );
    }

    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if user has connected Google Drive
    const { data: userData } = await supabase
      .from('users')
      .select('google_drive_connected')
      .eq('id', user.id)
      .single();

    if (!userData?.google_drive_connected) {
      return NextResponse.json(
        { success: false, error: 'Google Drive not connected' },
        { status: 400 }
      );
    }

    // List receipt files in the specified folder
    const files = await listReceiptFilesInFolder(user.id, folderId);

    return NextResponse.json({
      success: true,
      data: { files }
    });
  } catch (error) {
    console.error('Error listing Google Drive files:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to list Google Drive files' },
      { status: 500 }
    );
  }
}
