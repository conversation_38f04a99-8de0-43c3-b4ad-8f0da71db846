'use client';

import { useState } from 'react';
import { Download, Trash2, Database, Archive, AlertTriangle, FileText, Calendar, Loader2 } from 'lucide-react';
import { UserData, UserPreferences } from './SettingsClient';

interface DataSettingsProps {
  userData: UserData;
  userPreferences: UserPreferences;
  onPreferencesUpdate: () => void;
}

interface ExportProgress {
  status: 'idle' | 'preparing' | 'exporting' | 'complete' | 'error';
  progress: number;
  message: string;
  downloadUrl?: string;
}

export default function DataSettings({ userData, userPreferences, onPreferencesUpdate }: DataSettingsProps) {
  const [exportProgress, setExportProgress] = useState<ExportProgress>({ status: 'idle', progress: 0, message: '' });
  const [clearingData, setClearingData] = useState<string | null>(null);
  const [showClearConfirm, setShowClearConfirm] = useState<string | null>(null);

  const handleExportData = async (format: 'csv' | 'json' | 'xlsx') => {
    try {
      setExportProgress({ status: 'preparing', progress: 0, message: 'Preparing export...' });

      const response = await fetch('/api/data/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format }),
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      setExportProgress({ status: 'exporting', progress: 50, message: 'Generating file...' });

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      
      // Create download link
      const a = document.createElement('a');
      a.href = url;
      a.download = `receipts-export-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setExportProgress({ status: 'complete', progress: 100, message: 'Export complete!' });
      
      setTimeout(() => {
        setExportProgress({ status: 'idle', progress: 0, message: '' });
      }, 3000);

    } catch (error) {
      console.error('Export error:', error);
      setExportProgress({ 
        status: 'error', 
        progress: 0, 
        message: error instanceof Error ? error.message : 'Export failed' 
      });
      
      setTimeout(() => {
        setExportProgress({ status: 'idle', progress: 0, message: '' });
      }, 5000);
    }
  };

  const handleClearData = async (dataType: string) => {
    if (showClearConfirm !== dataType) {
      setShowClearConfirm(dataType);
      return;
    }

    try {
      setClearingData(dataType);

      const response = await fetch('/api/data/clear', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ dataType }),
      });

      const result = await response.json();

      if (result.success) {
        alert(`${dataType} data cleared successfully`);
        onPreferencesUpdate();
      } else {
        throw new Error(result.error || 'Failed to clear data');
      }
    } catch (error) {
      console.error('Clear data error:', error);
      alert(`Failed to clear ${dataType} data. Please try again.`);
    } finally {
      setClearingData(null);
      setShowClearConfirm(null);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getRetentionLabel = (months: number): string => {
    if (months === -1) return 'Forever';
    if (months === 12) return '1 Year';
    if (months === 24) return '2 Years';
    if (months === 36) return '3 Years';
    if (months === 60) return '5 Years';
    if (months === 84) return '7 Years';
    return `${months} Months`;
  };

  return (
    <div className="space-y-8">
      {/* Data Export */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-blue-600/20 rounded-lg flex items-center justify-center">
            <Download className="w-5 h-5 text-blue-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Export Data</h3>
            <p className="text-gray-400 text-sm">Download your receipt data in various formats</p>
          </div>
        </div>

        <div className="space-y-6">
          {/* Export Progress */}
          {exportProgress.status !== 'idle' && (
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                {exportProgress.status === 'error' ? (
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                ) : exportProgress.status === 'complete' ? (
                  <Download className="w-5 h-5 text-green-400" />
                ) : (
                  <Loader2 className="w-5 h-5 text-blue-400 animate-spin" />
                )}
                <span className="text-white font-medium">{exportProgress.message}</span>
              </div>
              {exportProgress.status !== 'error' && exportProgress.status !== 'complete' && (
                <div className="w-full bg-gray-600 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${exportProgress.progress}%` }}
                  />
                </div>
              )}
            </div>
          )}

          {/* Export Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <FileText className="w-5 h-5 text-green-400" />
                <span className="text-white font-medium">CSV Export</span>
              </div>
              <p className="text-gray-400 text-sm mb-4">
                Spreadsheet-compatible format for Excel, Google Sheets, etc.
              </p>
              <button
                onClick={() => handleExportData('csv')}
                disabled={exportProgress.status !== 'idle'}
                className="w-full bg-green-600 hover:bg-green-700 disabled:bg-green-600/50 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Export CSV
              </button>
            </div>

            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <Database className="w-5 h-5 text-blue-400" />
                <span className="text-white font-medium">JSON Export</span>
              </div>
              <p className="text-gray-400 text-sm mb-4">
                Structured data format for developers and data analysis.
              </p>
              <button
                onClick={() => handleExportData('json')}
                disabled={exportProgress.status !== 'idle'}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Export JSON
              </button>
            </div>

            <div className="bg-gray-700/50 rounded-lg p-4">
              <div className="flex items-center gap-3 mb-3">
                <Archive className="w-5 h-5 text-purple-400" />
                <span className="text-white font-medium">Excel Export</span>
              </div>
              <p className="text-gray-400 text-sm mb-4">
                Native Excel format with formatting and multiple sheets.
              </p>
              <button
                onClick={() => handleExportData('xlsx')}
                disabled={exportProgress.status !== 'idle'}
                className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-600/50 text-white px-4 py-2 rounded-lg transition-colors"
              >
                Export Excel
              </button>
            </div>
          </div>

          <div className="bg-gray-700/50 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2">Export includes:</h4>
            <ul className="text-gray-300 text-sm space-y-1">
              <li>• All receipt data and extracted information</li>
              <li>• Processing metadata and confidence scores</li>
              <li>• File information and timestamps</li>
              <li>• Google Sheets integration data (if applicable)</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Data Overview */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-gray-600/20 rounded-lg flex items-center justify-center">
            <Database className="w-5 h-5 text-gray-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Data Overview</h3>
            <p className="text-gray-400 text-sm">Summary of your stored data</p>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">{userData.receipts_processed}</div>
            <div className="text-gray-400 text-sm">Total Receipts</div>
          </div>
          
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">
              {new Date().getFullYear() - new Date(userData.created_at).getFullYear()}
            </div>
            <div className="text-gray-400 text-sm">Years Active</div>
          </div>
          
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">
              {getRetentionLabel(userPreferences.data_retention_months)}
            </div>
            <div className="text-gray-400 text-sm">Retention Period</div>
          </div>
          
          <div className="bg-gray-700/50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-white">
              {userData.google_sheets_connected ? 'Yes' : 'No'}
            </div>
            <div className="text-gray-400 text-sm">Sheets Connected</div>
          </div>
        </div>
      </div>

      {/* Data Management */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-red-600/20 rounded-lg flex items-center justify-center">
            <Trash2 className="w-5 h-5 text-red-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Data Management</h3>
            <p className="text-gray-400 text-sm">Clear specific types of data</p>
          </div>
        </div>

        <div className="space-y-4">
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-yellow-200 font-medium">Warning</p>
                <p className="text-yellow-300 text-sm mt-1">
                  These actions are permanent and cannot be undone. Make sure to export your data first if needed.
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">Clear Processing History</h4>
              <p className="text-gray-400 text-sm mb-4">
                Remove processing logs and error messages while keeping receipt data.
              </p>
              <button
                onClick={() => handleClearData('processing_history')}
                disabled={clearingData === 'processing_history'}
                className={`w-full px-4 py-2 rounded-lg transition-colors ${
                  showClearConfirm === 'processing_history'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-orange-600 hover:bg-orange-700 text-white'
                }`}
              >
                {clearingData === 'processing_history' ? (
                  <span className="flex items-center justify-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Clearing...
                  </span>
                ) : showClearConfirm === 'processing_history' ? (
                  'Click to Confirm'
                ) : (
                  'Clear History'
                )}
              </button>
            </div>

            <div className="bg-gray-700/50 rounded-lg p-4">
              <h4 className="text-white font-medium mb-2">Clear All Receipt Data</h4>
              <p className="text-gray-400 text-sm mb-4">
                Permanently delete all receipts, files, and extracted data.
              </p>
              <button
                onClick={() => handleClearData('all_receipts')}
                disabled={clearingData === 'all_receipts'}
                className={`w-full px-4 py-2 rounded-lg transition-colors ${
                  showClearConfirm === 'all_receipts'
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-red-600 hover:bg-red-700 text-white'
                }`}
              >
                {clearingData === 'all_receipts' ? (
                  <span className="flex items-center justify-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Clearing...
                  </span>
                ) : showClearConfirm === 'all_receipts' ? (
                  'Click to Confirm'
                ) : (
                  'Clear All Data'
                )}
              </button>
            </div>
          </div>

          {showClearConfirm && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <p className="text-red-200 text-sm">
                Click the button again to confirm deletion of {showClearConfirm.replace('_', ' ')}. 
                This action cannot be undone.
              </p>
              <button
                onClick={() => setShowClearConfirm(null)}
                className="mt-2 text-red-400 hover:text-red-300 text-sm underline"
              >
                Cancel
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Backup Information */}
      <div className="bg-gray-800/50 border border-gray-700 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-green-600/20 rounded-lg flex items-center justify-center">
            <Archive className="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h3 className="text-xl font-semibold text-white">Backup Information</h3>
            <p className="text-gray-400 text-sm">How your data is protected</p>
          </div>
        </div>

        <div className="bg-gray-700/50 rounded-lg p-4">
          <h4 className="text-white font-medium mb-3">Automatic Backups</h4>
          <ul className="text-gray-300 text-sm space-y-2">
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              Daily database backups with 30-day retention
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              File storage redundancy across multiple regions
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              Point-in-time recovery available for 7 days
            </li>
            <li className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              Google Sheets data remains in your Google account
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
