'use client'

import { useEffect, useRef, useCallback } from 'react'

interface QueueProcessorProps {
  enabled?: boolean
  intervalMs?: number
}

export default function QueueProcessor({
  enabled = true,
  intervalMs = 30000 // 30 seconds - much more reasonable for background processing
}: QueueProcessorProps) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const isProcessingRef = useRef(false)
  const consecutiveEmptyChecks = useRef(0)
  const currentInterval = useRef(intervalMs)
  const activeJobsRef = useRef(false)

  const scheduleNextCheck = useCallback(() => {
    if (intervalRef.current) {
      clearTimeout(intervalRef.current)
    }

    // Use shorter intervals when jobs are active, but not too aggressive
    const nextInterval = activeJobsRef.current ? 10000 : currentInterval.current;

    intervalRef.current = setTimeout(() => {
      console.log('QueueProcessor: Checking for jobs...')
      processQueue()
    }, nextInterval)
  }, [])

  const processQueue = useCallback(async () => {
    // Prevent overlapping calls
    if (isProcessingRef.current) {
      console.log('Queue processing already in progress, skipping')
      return
    }

    isProcessingRef.current = true

    try {
      console.log('QueueProcessor: Fallback check for stuck jobs...')
      const response = await fetch('/api/pipeline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Fallback-Processor/1.0'
        },
      })

      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          console.log('QueueProcessor: Jobs processed successfully:', data.batchResults || { successful: 1 })

          // Mark that we have active jobs
          activeJobsRef.current = true
          consecutiveEmptyChecks.current = 0
          currentInterval.current = intervalMs

          // Process immediately if there might be more jobs - much faster follow-up
          setTimeout(() => processQueue(), 200) // Very fast follow-up processing
          return
        } else {
          console.log('QueueProcessor: No jobs in queue or processing failed')
          activeJobsRef.current = false
        }
      } else if (response.status === 200) {
        // Check response body even if status is 200 but not success
        const data = await response.json()
        if (data.message?.includes('No jobs in queue')) {
          console.log('QueueProcessor: No jobs in queue')
          activeJobsRef.current = false
        }
      } else {
        console.error('QueueProcessor: Pipeline processing failed:', response.status, await response.text())
        activeJobsRef.current = false
      }

      // Adaptive polling - but much more responsive
      consecutiveEmptyChecks.current++
      if (consecutiveEmptyChecks.current >= 5) {
        // After 5 empty checks, slow down polling but not too much
        currentInterval.current = Math.min(intervalMs * 2, 30000) // Max 30 seconds
        console.log(`QueueProcessor: Slowing down polling to ${currentInterval.current}ms after ${consecutiveEmptyChecks.current} empty checks`)
      }

    } catch (error) {
      console.error('QueueProcessor: Error calling pipeline:', error)
      activeJobsRef.current = false
      consecutiveEmptyChecks.current++
    } finally {
      isProcessingRef.current = false
      // Schedule next check with adaptive interval
      scheduleNextCheck()
    }
  }, [intervalMs, scheduleNextCheck])

  // Expose global method to trigger queue processing
  useEffect(() => {
    if (!enabled || typeof window === 'undefined') return

    try {
      // Make processQueue available globally for triggering after uploads
      (window as any).triggerQueueProcessing = processQueue
    } catch (error) {
      console.warn('Failed to set global triggerQueueProcessing:', error)
    }

    return () => {
      try {
        if (typeof window !== 'undefined' && (window as any).triggerQueueProcessing === processQueue) {
          delete (window as any).triggerQueueProcessing
        }
      } catch (error) {
        console.warn('Failed to cleanup global triggerQueueProcessing:', error)
      }
    }
  }, [enabled, processQueue])

  useEffect(() => {
    if (enabled) {
      console.log('QueueProcessor: Starting fallback queue monitoring (webhook-first system)...')

      // Reset adaptive polling state
      consecutiveEmptyChecks.current = 0
      currentInterval.current = intervalMs

      // Start processing after much shorter delay for better responsiveness
      setTimeout(() => processQueue(), 1000) // 1 second delay

      // Cleanup interval on unmount
      return () => {
        console.log('QueueProcessor: Stopping fallback queue monitoring...')
        if (intervalRef.current) {
          clearTimeout(intervalRef.current)
          intervalRef.current = null
        }
      }
    }
  }, [enabled, processQueue, intervalMs])

  // This component doesn't render anything visible
  return null
} 