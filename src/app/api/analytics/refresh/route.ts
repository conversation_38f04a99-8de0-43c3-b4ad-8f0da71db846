import { NextRequest, NextResponse } from 'next/server';
import { triggerAnalyticsGeneration } from '@/lib/data/analytics-generator';
import { createClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Get user authentication
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Trigger analytics generation
    const result = await triggerAnalyticsGeneration();
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'Analytics refreshed successfully',
        timestamp: new Date().toISOString()
      }, { status: 200 });
    } else {
      return NextResponse.json(result, { status: 400 });
    }
  } catch (error) {
    console.error('Analytics refresh API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
