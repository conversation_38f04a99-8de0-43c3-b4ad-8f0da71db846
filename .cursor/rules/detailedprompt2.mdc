---
description: 
globs: 
alwaysApply: true
---
7. Pricing & Payment System
7.1 Pricing Tiers
Free Tier (0 receipts/month)
•	Features: Demo mode only
•	Limitations: 3 sample receipts
•	Purpose: Trial and evaluation
Tier 1: Starter ($50/month)
•	Receipt Limit: Up to 200 receipts
•	Features: 
o	Full AI extraction
o	Google Sheets integration
o	Basic analytics
o	Email support
o	Standard processing speed
•	Target: Small businesses, freelancers
Tier 2: Professional ($100/month)
•	Receipt Limit: Unlimited receipts
•	Features: 
o	Everything in Tier 1
o	Priority processing (high-priority Redis queue)
o	Advanced analytics
o	Google Drive integration
o	API access
o	Priority support
•	Target: Growing businesses, accountants
7.2 PayStack Integration
Payment Flow
1.	Tier Selection: User chooses subscription tier
2.	PayStack Modal: Embedded payment form
3.	Payment Processing: Handle PayStack webhook
4.	Account Upgrade: Update user tier immediately
5.	Confirmation: Send email receipt and notification
PayStack API Implementation
// Initialize payment
const paymentData = {
  email: user.email,
  amount: tier === 'tier1' ? 5000000 : ********, // Amount in kobo
  currency: 'KES',
  reference: generateReference(),
  callback_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/callback`,
  metadata: {
    user_id: user.id,
    tier: selectedTier,
    receipts_allowance: tier === 'tier1' ? 200 : 999999
  }
};
Webhook Handling
•	Verification: Validate PayStack signature
•	Status Update: Update user subscription
•	Receipt Allowance: Set monthly limits
•	Notification: Send confirmation email
•	Analytics: Track conversion metrics
7.3 Usage Enforcement
Receipt Counting
•	Track Usage: Increment on successful processing
•	Monthly Reset: Reset counters on billing cycle
•	Overage Handling: Block processing when limit reached
•	Upgrade Prompts: Show tier upgrade options
Feature Restrictions
const tierLimits = {
  free: { receipts: 3, analytics: false, googleDrive: false, priority: 'low' },
  tier1: { receipts: 200, analytics: true, googleDrive: false, priority: 'normal' },
  tier2: { receipts: 999999, analytics: true, googleDrive: true, priority: 'high' }
};
________________________________________
8. AI Processing & Data Extraction
8.1 OpenAI Integration
Model Selection
•	Primary: GPT-4 Vision Preview
•	Backup: GPT-4o with image understanding
•	Reasoning: Best accuracy for receipt data extraction
Prompt Engineering
const RECEIPT_EXTRACTION_PROMPT = `
You are an expert at extracting structured data from receipt images.
Extract the following information and return ONLY valid JSON:

{
  "vendor": "Business name",
  "vendor_tax_id": "Tax ID if visible, null if not",
  "date": "YYYY-MM-DD format",
  "currency": "3-letter currency code (default KES)",
  "payment_method": "Cash/Card/Mobile Money/etc",
  "items": [
    {
      "description": "Item name",
      "total": 0.00,
      "category": "Auto-categorize: Food, Transport, Office, etc"
    }
  ],
  "subtotal": 0.00,
  "tax_rate_percent": 0.0,
  "tax_amount": 0.00,
  "total_amount": 0.00,
  "paid_amount": 0.00
}

Rules:
- Use null for missing values
- Ensure numbers are accurate decimals
- Categories should be business-appropriate
- Date must be valid YYYY-MM-DD
- If unclear, make best estimate and note in confidence
`;
Error Handling & Fallbacks
async function extractReceiptData(imageUrl: string): Promise<ReceiptData> {
  let attempts = 0;
  const maxAttempts = 3;
  
  while (attempts < maxAttempts) {
    try {
      const response = await openai.chat.completions.create({
        model: "gpt-4-vision-preview",
        messages: [
          {
            role: "user",
            content: [
              { type: "text", text: RECEIPT_EXTRACTION_PROMPT },
              { type: "image_url", image_url: { url: imageUrl } }
            ]
          }
        ],
        max_tokens: 1500,
        temperature: 0.1 // Low temperature for consistency
      });
      
      const extractedData = JSON.parse(response.choices[0].message.content);
      return validateAndCleanData(extractedData);
      
    } catch (error) {
      attempts++;
      if (attempts === maxAttempts) {
        throw new Error(`Failed to extract data after ${maxAttempts} attempts`);
      }
      await delay(1000 * attempts); // Exponential backoff
    }
  }
}
8.2 Data Validation & Quality Control
Validation Rules
const validationSchema = {
  vendor: { required: true, minLength: 1, maxLength: 255 },
  date: { required: true, format: 'YYYY-MM-DD', maxDate: 'today' },
  currency: { required: true, enum: ['KES', 'USD', 'EUR', 'GBP'] },
  total_amount: { required: true, min: 0, max: 1000000 },
  items: { required: true, minItems: 1, maxItems: 50 }
};
Confidence Scoring
function calculateConfidenceScore(data: ReceiptData): number {
  let score = 0;
  const weights = {
    vendor: 20,
    date: 15,
    total_amount: 25,
    items: 30,
    vendor_tax_id: 10
  };
  
  Object.entries(weights).forEach(([field, weight]) => {
    if (data[field] && data[field] !== null) {
      score += weight;
    }
  });
  
  return score / 100;
}
________________________________________
8. Background Processing System
8.1 Queue Management
Job Creation
async function createProcessingJob(receiptId: string, userId: string): Promise<void> {
  await supabase
    .from('processing_queue')
    .insert({
      receipt_id: receiptId,
      user_id: userId,
      status: 'pending',
      priority: getUserPriority(userId), // Higher tier = higher priority
      scheduled_at: new Date().toISOString()
    });
  
  // Trigger immediate processing if queue is empty
  await triggerQueueProcessor();
}
Queue Processing Logic
async function processQueue(): Promise<void> {
  const jobs = await supabase
    .from('processing_queue')
    .select('*')
    .eq('status', 'pending')
    .order('priority', { ascending: false })
    .order('scheduled_at', { ascending: true })
    .limit(5); // Process 5 jobs concurrently
  
  const promises = jobs.data?.map(job => processJob(job)) || [];
  await Promise.allSettled(promises);
  
  // Schedule next processing cycle
  setTimeout(processQueue, 5000); // Check every 5 seconds
}
Job Processing
async function processJob(job: QueueJob): Promise<void> {
  try {
    // Update status to processing
    await updateJobStatus(job.id, 'processing');
    
    // Get receipt data
    const receipt = await getReceiptById(job.receipt_id);
    const imageUrl = await getSignedUrl(receipt.file_path);
    
    // Extract data with AI
    const extractedData = await extractReceiptData(imageUrl);
    
    // Save extracted data
    await saveExtractedData(job.receipt_id, extractedData);
    
    // Export to Google Sheets
    await exportToGoogleSheets(job.user_id, extractedData);
    
    // Update job status
    await updateJobStatus(job.id, 'completed');
    
    // Send notification
    await sendNotification(job.user_id, 'Receipt processed successfully');
    
  } catch (error) {
    await handleJobError(job, error);
  }
}
8.2 Real-time Updates
WebSocket Integration
// Supabase Realtime subscription
const subscription = supabase
  .channel('processing_updates')
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'processing_queue',
    filter: `user_id=eq.${userId}`
  }, (payload) => {
    updateUIProgress(payload.new);
  })
  .subscribe();
Progress Indicators
•	Upload Progress: File upload percentage
•	Queue Position: Position in processing queue
•	Processing Stage: Current AI processing step
•	Completion Status: Success/failure notification
________________________________________
9. Analytics & Insights
9.1 User Analytics Dashboard
Key Metrics
•	Monthly Spending: Total amount across all receipts
•	Receipt Count: Number of receipts processed
•	Average Receipt Value: Mean transaction amount
•	Top Vendors: Most frequent merchants
•	Category Breakdown: Spending by category
•	Trend Analysis: Month-over-month comparisons
Visualization Components
// Chart configurations
const chartConfigs = {
  spendingTrend: {
    type: 'line',
    data: monthlySpending,
    color: '#ff006e'
  },
  categoryBreakdown: {
    type: 'doughnut',
    data: categoryTotals,
    colors: ['#ff006e', '#fb8500', '#8338ec', '#00f5ff']
  },
  vendorRanking: {
    type: 'bar',
    data: topVendors,
    orientation: 'horizontal'
  }
};
9.2 Data Processing for Analytics
Monthly Analytics Generation
async function generateMonthlyAnalytics(userId: string, month: string): Promise<void> {
  const receipts = await getReceiptsForMonth(userId, month);
  
  const analytics = {
    total_receipts: receipts.length,
    total_spent: receipts.reduce((sum, r) => sum + r.total_amount, 0),
    average_receipt_amount: receipts.length > 0 ? total_spent / receipts.length : 0,
    top_vendor: getMostFrequentVendor(receipts),
    top_category: getMostSpentCategory(receipts),
    category_breakdown: getCategoryBreakdown(receipts),
    vendor_breakdown: getVendorBreakdown(receipts)
  };
  
  await supabase
    .from('user_analytics')
    .upsert({
      user_id: userId,
      month_year: month,
      ...analytics
    });
}
________________________________________
10. API Specifications
10.1 REST API Endpoints
Authentication Endpoints
POST /api/auth/google-oauth
GET  /api/auth/user
POST /api/auth/logout
Receipt Management
POST /api/receipts/upload          // Upload single receipt
POST /api/receipts/bulk-upload     // Upload multiple receipts
GET  /api/receipts                 // List user receipts
GET  /api/receipts/:id             // Get single receipt
DELETE /api/receipts/:id           // Delete receipt
POST /api/receipts/:id/reprocess   // Reprocess failed receipt
Google Sheets Integration
POST /api/google-sheets/connect    // Connect Google account
GET  /api/google-sheets/list       // List user sheets
POST /api/google-sheets/create     // Create new sheet
GET  /api/google-sheets/:id/export // Export sheet data
Analytics Endpoints
GET /api/analytics/dashboard       // Dashboard data
GET /api/analytics/monthly/:month  // Monthly analytics
GET /api/analytics/export          // Export analytics data
Payment Endpoints
POST /api/payments/initialize      // Initialize PayStack payment
POST /api/payments/webhook         // PayStack webhook handler
GET  /api/payments/history         // Payment history
10.2 Edge Functions
Receipt Processing Function
// supabase/functions/process-receipt/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import OpenAI from "https://deno.land/x/openai@v4.28.0/mod.ts"

serve(async (req) => {
  const { receiptId, userId, imageUrl } = await req.json();
  
  try {
    // Extract data with OpenAI
    const extractedData = await extractReceiptData(imageUrl);
    
    // Save to database
    await saveExtractedData(receiptId, extractedData);
    
    // Update Google Sheets
    await updateGoogleSheet(userId, extractedData);
    
    return new Response(JSON.stringify({ 
      success: true, 
      data: extractedData 
    }));
    
  } catch (error) {
    return new Response(JSON.stringify({ 
      success: false, 
      error: error.message 
    }), { status: 500 });
  }
});
________________________________________
11. Security & Privacy
11.1 Data Security Measures
Authentication Security
•	OAuth 2.0: Google OAuth for secure login
•	JWT Tokens: Supabase JWT with expiration
•	Session Management: Secure session handling
•	Multi-factor Authentication: Optional 2FA support
Data Encryption
•	In Transit: TLS 1.3 for all API communications
•	At Rest: AES-256 encryption for stored files
•	Database: PostgreSQL encryption at rest
•	API Keys: Environment variables, never client-side
Access Control
•	Row Level Security: Supabase RLS policies
•	Role-based Access: User, admin roles
•	API Rate Limiting: Prevent abuse
11.2 Privacy Compliance
Data Handling
•	Minimal Collection: Only necessary data collected
•	User Consent: Clear consent for data processing
•	Data Retention: Configurable retention periods
•	Right to Delete: Complete data deletion on request
Third-party Integrations
•	OpenAI: Image processing only, no data retention
•	Google: Limited scope access for Sheets API
•	PayStack: PCI-compliant payment processing
________________________________________
12. Performance & Scalability (Completed)
12.2 Optimization Strategies (Continued)
Frontend Optimization
•	Code Splitting: Dynamic imports for routes
•	Image Optimization: Next.js automatic optimization
•	Caching: Redis-based response caching
•	Bundle Optimization: Tree shaking and minification
•	Lazy Loading: Components and images
•	Service Worker: Offline capability for dashboards
Backend Optimization with Redis
typescript
// Caching strategies
export const CACHE_KEYS = {
  USER_ANALYTICS: (userId: string, month: string) => `analytics:${userId}:${month}`,
  RECEIPT_DATA: (receiptId: string) => `receipt:${receiptId}`,
  GOOGLE_SHEETS: (userId: string, year: number) => `sheets:${userId}:${year}`,
  USER_TIER: (userId: string) => `tier:${userId}`
};

export const CACHE_TTL = {
  ANALYTICS: 3600, // 1 hour
  RECEIPT_DATA: 86400, // 24 hours
  GOOGLE_SHEETS: 1800, // 30 minutes
  USER_TIER: 300 // 5 minutes
};

// Cache implementation
async function getCachedAnalytics(userId: string, month: string) {
  const cacheKey = CACHE_KEYS.USER_ANALYTICS(userId, month);
  const cached = await redis.get(cacheKey);
  
  if (cached) {
    return JSON.parse(cached as string);
  }
  
  const analytics = await generateMonthlyAnalytics(userId, month);
  await redis.setex(cacheKey, CACHE_TTL.ANALYTICS, JSON.stringify(analytics));
  
  return analytics;
}
Database Optimization
•	Connection Pooling: Supabase connection management
•	Query Optimization: Indexed queries and prepared statements
•	Batch Operations: Bulk inserts for receipt items
•	Partitioning: Time-based partitioning for large tables
12.3 Monitoring & Observability
Performance Monitoring
typescript
// Performance metrics tracking
export const METRICS = {
  UPLOAD_TIME: 'upload_duration_ms',
  PROCESSING_TIME: 'ai_processing_duration_ms',
  EXPORT_TIME: 'google_sheets_export_duration_ms',
  QUEUE_SIZE: 'processing_queue_size',
  ERROR_RATE: 'error_rate_percent'
};

async function trackMetric(metric: string, value: number, tags?: Record<string, string>) {
  await redis.lpush(`metrics:${metric}`, JSON.stringify({
    value,
    timestamp: Date.now(),
    tags
  }));
  
  // Keep only last 1000 entries per metric
  await redis.ltrim(`metrics:${metric}`, 0, 999);
}
Health Checks
typescript
export async function healthCheck(): Promise<{
  status: string;
  services: Record<string, boolean>;
  metrics: Record<string, number>;
}> {
  const services = {
    database: await checkDatabase(),
    redis: await checkRedis(),
    openai: await checkOpenAI(),
    google_sheets: await checkGoogleSheets(),
    paystack: await checkPayStack()
  };
  
  const queueSize = await redis.llen(QUEUE_KEYS.PROCESSING);
  const failedJobs = await redis.llen(QUEUE_KEYS.FAILED);
  
  return {
    status: Object.values(services).every(Boolean) ? 'healthy' : 'degraded',
    services,
    metrics: {
      queue_size: queueSize,
      failed_jobs: failedJobs,
      uptime: process.uptime()
    }
  };
