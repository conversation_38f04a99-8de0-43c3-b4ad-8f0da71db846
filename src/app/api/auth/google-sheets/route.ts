import { NextRequest, NextResponse } from 'next/server'
import { getAuthorizationUrl } from '@/lib/google-sheets/auth'

export async function GET(request: NextRequest) {
  try {
    // Get the Google OAuth authorization URL with Sheets permissions
    const authUrl = getAuthorizationUrl()
    
    // Redirect user to Google OAuth consent screen
    return NextResponse.redirect(authUrl)
  } catch (error) {
    console.error('Error generating Google OAuth URL:', error)
    // Get the base URL for error redirect
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || process.env.NEXT_PUBLIC_SITE_URL || 
                   `${request.nextUrl.protocol}//${request.nextUrl.host}`;
    return NextResponse.redirect(`${baseUrl}/dashboard/sheets?error=oauth_failed`)
  }
} 