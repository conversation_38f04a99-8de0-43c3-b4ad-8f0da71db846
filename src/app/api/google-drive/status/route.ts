import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';
import { getGoogleDriveStatus } from '@/lib/google-drive/client';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated
    const supabase = await createClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    // Get Google Drive status
    const status = await getGoogleDriveStatus(user.id);

    return NextResponse.json({
      success: true,
      data: status
    });
  } catch (error) {
    console.error('Error getting Google Drive status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get Google Drive status' 
      }, 
      { status: 500 }
    );
  }
}
