#!/usr/bin/env node

/**
 * Gmail Processing Test Script for Reco Engine
 * 
 * This script allows you to test Gmail processing manually
 * without waiting for the cron job.
 * 
 * Usage:
 * node scripts/test-gmail-processing.js [hour]
 * 
 * Examples:
 * node scripts/test-gmail-processing.js        # Test current hour
 * node scripts/test-gmail-processing.js 9      # Test hour 9 (9 AM)
 * node scripts/test-gmail-processing.js all    # Test all hours with users
 */

const { processGmailForHour } = require('./gmail-cron.js');

async function main() {
  const args = process.argv.slice(2);
  const hourArg = args[0];

  console.log('🧪 Gmail Processing Test Script');
  console.log('===============================');
  console.log('');

  try {
    if (hourArg === 'all') {
      console.log('🔄 Testing all hours (0-23)...');
      
      for (let hour = 0; hour < 24; hour++) {
        console.log(`\n⏰ Testing hour ${hour}:`);
        const results = await processGmailForHour(hour);
        
        if (results.processedUsers > 0) {
          console.log(`   ✅ Found ${results.processedUsers} users to process`);
        } else {
          console.log(`   📭 No users scheduled for hour ${hour}`);
        }
        
        // Small delay between hours
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } else {
      const targetHour = hourArg ? parseInt(hourArg) : new Date().getHours();
      
      if (isNaN(targetHour) || targetHour < 0 || targetHour > 23) {
        console.error('❌ Invalid hour. Please provide a number between 0-23');
        process.exit(1);
      }
      
      console.log(`🕐 Testing Gmail processing for hour ${targetHour}...`);
      console.log('');
      
      const results = await processGmailForHour(targetHour);
      
      console.log('');
      console.log('📊 Test Results:');
      console.log(`   - Target hour: ${results.targetHour}`);
      console.log(`   - Processed users: ${results.processedUsers}`);
      console.log(`   - Total receipts: ${results.totalReceipts}`);
      console.log(`   - Total emails: ${results.totalEmails}`);
      console.log(`   - Errors: ${results.errors.length}`);
      
      if (results.errors.length > 0) {
        console.log('');
        console.log('⚠️ Errors:');
        results.errors.forEach(error => console.log(`   - ${error}`));
      }
    }
    
    console.log('');
    console.log('✅ Test completed successfully');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Show usage if help requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Gmail Processing Test Script');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/test-gmail-processing.js [hour]');
  console.log('');
  console.log('Examples:');
  console.log('  node scripts/test-gmail-processing.js        # Test current hour');
  console.log('  node scripts/test-gmail-processing.js 9      # Test hour 9 (9 AM)');
  console.log('  node scripts/test-gmail-processing.js all    # Test all hours');
  console.log('');
  console.log('Options:');
  console.log('  -h, --help    Show this help message');
  process.exit(0);
}

// Run the test
main();
